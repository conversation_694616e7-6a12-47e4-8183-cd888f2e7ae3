import API from '../constants/api';
import { BaseResponse } from '../models/common';
import { Item001InitGetItemListModel, Item001InitGetItemListResult } from '../models/item001-init-get-itemlist';
import { Item002InitGetItemQueryModel, Item002InitGetItemQueryResult } from '../models/item002-init-get-itemquery';
import createAPI from './baseApi';

/**
 * ItemAPI
 */
class ItemAPI {
    /**
     * item001InitGetItemList
     * @param data Item001InitGetItemListModel
     * @returns Promise<BaseResponse<Item001InitGetItemListResult>>
     */
    static item001InitGetItemList = (
        data: Item001InitGetItemListModel
    ): Promise<BaseResponse<Item001InitGetItemListResult>> => {
        return createAPI<Item001InitGetItemListResult>({
            url: API.ITEM001_INIT_GET_ITEMLIST,
            data: data,
        });
    };

    /**
     * item002InitGetItemQuery
     * @param data Item001InitGetItemListModel
     * @returns Promise<BaseResponse<Item002InitGetItemQueryResult>>
     */
    static item002InitGetItemQuery = (
        data?: Item002InitGetItemQueryModel
    ): Promise<BaseResponse<Item002InitGetItemQueryResult>> => {
        return createAPI<Item002InitGetItemQueryResult>({
            url: API.ITEM002_INIT_GET_ITEMQUERY,
            data: data,
        });
    };
}

export default ItemAPI;
