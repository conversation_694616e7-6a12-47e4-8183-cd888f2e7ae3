import { Typography } from '@mui/material';
import { memo, useCallback, useMemo } from 'react';
import { useLoaderData, useLocation, useNavigate } from 'react-router-dom';
import './styles.scss';
import { isBrowser, isTablet } from 'react-device-detect';
import Screens from '../../../../constants/screens';
import CommonAPI from '../../../../apis/common';
import Utils from '../../../../utils/utils';
import ChargeAPI from '../../../../apis/charge';
import CommonUtils from '../../../../utils/common';
import StorageServices from '../../../../services/storage.service';
import Container from '../../../../components/Container';
import Button from '../../../../components/Button';
import { ChargeIntBnkPay006MainExecuteChargeModel } from '../../../../models/chargeintbnkpay006-main-execute-charge';
import moment from 'moment';
import { BankListItem } from '../../../../models/chargeintbnkpay005-init-get-bankinfo';
import { Home001InitGetCoinItem } from '../../../../models/home001-init-get-coin';
import momenttz from 'moment-timezone';

/**
 * ResultCode
 */
export enum ResultCode {
    R000 = 'R000',
    RC01 = 'RC01',
}

/**
 * ResultStatus
 */
export enum ResultStatus {
    SUCCESS = 'success',
    FAILURE = 'failure',
}

/**
 * PaymentResult
 */
export interface PaymentResult {
    result_code: string;
    status: string;
    payment_type: string;
}

/**
 * ChargeIntBnkPay006
 * ID: CHARGEINTBNKPAY006
 * Name: BankPay決済 (チャージ内容確認)
 * チャージ内容確認画面
 * @returns React.JSX.Element
 */
const ChargeIntBnkPay006 = (): React.JSX.Element => {
    const location = useLocation();
    const navigate = useNavigate();
    const loaderData = useLoaderData() as Home001InitGetCoinItem;
    const dataMedalServiceItem = useMemo(() => loaderData, [loaderData]);

    const chargeAmount = useMemo(() => location?.state?.chargeAmount, [location?.state?.chargeAmount]);
    const bankInfo = useMemo(() => location?.state?.bankInfo, [location?.state?.bankInfo]) as BankListItem;

    /**
     * handleClickBackButton
     */
    const handleClickBackButton = useCallback(() => {
        CommonAPI.commonUserTransitionHistory({
            OriginScreenName: window.location.pathname,
            DestinationScreenName: StorageServices.Local.get('PREVIOUS_SCREEN'),
            ElementName: Utils.t('common.button.back_button'),
            DeviceID: isBrowser ? 'PC' : isTablet ? 'Tablet' : 'mobile',
        });
        navigate(-1);
    }, [navigate]);

    /**
     * handleClickCancelButton
     */
    const handleClickCancelButton = useCallback(() => {
        CommonAPI.commonUserTransitionHistory({
            OriginScreenName: window.location.pathname,
            DestinationScreenName: Screens.CHARGE,
            ElementName: Utils.t('common.button.cancel'),
            DeviceID: isBrowser ? 'PC' : isTablet ? 'Tablet' : 'mobile',
        });
        navigate(Screens.CHARGEINT_BNKPAY005);
    }, [navigate]);

    /**
     * handlePaymentSuccess
     * handle when payment success
     * @param result PaymentResult
     */
    const handlePaymentSuccess = useCallback(() => {
        try {
            CommonAPI.commonUserTransitionHistory({
                OriginScreenName: window.location.pathname,
                DestinationScreenName: Screens.CHARGEINT_BNKPAY007,
                ElementName: Utils.t('charge.charge_confirm.accept')?.replaceAll('\n', ''),
                DeviceID: isBrowser ? 'PC' : isTablet ? 'Tablet' : 'mobile',
            });
            navigate(Screens.CHARGEINT_BNKPAY007, {
                state: {
                    ...location?.state,
                },
                replace: true,
            });
        } catch (error) {
            console.log(error);
        }
    }, [location?.state, navigate]);

    /**
     * handleClickCloseButton
     */
    const handleClickCloseButton = useCallback(() => {
        CommonUtils.closeMessageById('charge-int-bank-pay006-execute-fail');
    }, []);

    /**
     * handleClickExecuteButton
     */
    const handleClickExecuteButton = useCallback(async () => {
        const CURRENT_TIME = momenttz.tz('Asia/Tokyo');

        const payload: ChargeIntBnkPay006MainExecuteChargeModel = {
            MedalServiceID: dataMedalServiceItem?.MedalServiceID,
            Amount: Number(chargeAmount),
            useLock: true,
            NowDateTime: moment(CURRENT_TIME, 'YYYY/MM/DD HH:mm:ss.SSS').format('YYYY/MM/DD HH:mm:ss.SSS'),
        };
        const { status, Message } = await ChargeAPI.chargeIntBnkPay006MainExecuteCharge(payload);

        switch (status) {
            case 0:
                handlePaymentSuccess();
                break;
            case 3:
                CommonUtils.showMessage({
                    type: 'ERROR',
                    id: 'charge-int-bank-pay006-execute-fail',
                    message: (
                        <div className="wrapper-mess-register-fail">
                            {Utils.t('charge.bnk_pay.bnk_pay006.message_ten_minus')}
                        </div>
                    ),
                    iconClass: 'icon-charge-int-bank-pay006-execute-fail',
                    actions: {
                        direction: 'horizontal',
                        children: [
                            <Button
                                name="closeButton"
                                key="close"
                                className="w-100"
                                variant="contained"
                                onClick={handleClickCloseButton}
                            >
                                {Utils.t('common.button.close_kanji')}
                            </Button>,
                        ],
                    },
                });
                break;
            default:
                CommonUtils.showMessage({
                    message: Utils.t(Message || 'api.common.unknown_error'),
                    type: 'ERROR',
                });
                break;
        }
    }, [dataMedalServiceItem?.MedalServiceID, chargeAmount, handlePaymentSuccess, handleClickCloseButton]);

    /**
     * renderValueActionHistory
     */
    const renderValueActionHistory = useMemo(() => {
        return {
            HistoryType: 'BLANCE',
            HistoryKey: dataMedalServiceItem?.MedalServiceID,
            HistoryValue: String(chargeAmount),
        };
    }, [chargeAmount, dataMedalServiceItem?.MedalServiceID]);

    return useMemo(
        () => (
            <Container
                screenName="ChargeIntBnkPay006"
                useHeader={{
                    title: `${Utils.t('charge.bnk_pay.bnk_pay006.title')}`,
                    type: 'backLeftTitleCenterAndMenuRight',
                    onLeftButtonClick: handleClickBackButton,
                }}
            >
                <div className="charge-int-bnk-pay006 d-flex flex-column">
                    <div className="container-charging-confirm">
                        <div className="total-confirm">
                            <Typography className="title_bank_name">
                                {Utils.t('charge.bnk_pay.bnk_pay006.title_bank_name')}
                            </Typography>

                            <Typography className="confirm-value">
                                <span className="ff-roboto-bold" style={{ fontSize: 30 }}>
                                    {`${bankInfo?.BankName}${Utils.t('charge.bnk_pay.bnk_pay006.char_end_bank_name')}`}
                                </span>
                            </Typography>
                        </div>
                        <div className="total-confirm">
                            <Typography className="text-message-confirm">
                                {Utils.t('charge.charge_confirm.total_text')}
                            </Typography>

                            <Typography className="confirm-value">
                                <span className="ff-roboto-bold" style={{ fontSize: 40 }}>
                                    {Utils.formatNumber(location?.state?.chargeAmount)}
                                </span>
                                <span style={{ fontSize: 15 }}>{Utils.t('common.text.yen_currency')}</span>
                            </Typography>
                        </div>
                    </div>
                    <div className="footer-confirm mt-auto mb-3">
                        <Typography fontSize={14} className="text-start text-confirm">
                            {Utils.t('charge.bnk_pay.bnk_pay006.message_footer')}
                        </Typography>
                        <div className="d-flex justify-content-between mt-3 btn-container">
                            <Button
                                name="cancelButton"
                                className="flex-1"
                                variant="outlined"
                                fontSize={17}
                                height={49}
                                onClick={handleClickCancelButton}
                            >
                                {Utils.t('common.button.cancel')}
                            </Button>
                            <Button
                                name="executeButton"
                                className="flex-1"
                                fontSize={17}
                                height={49}
                                onClick={handleClickExecuteButton}
                                actionHistory={renderValueActionHistory}
                            >
                                {Utils.t('charge.charge_confirm.accept')}
                            </Button>
                        </div>
                    </div>
                </div>
            </Container>
        ),
        [
            handleClickBackButton,
            location?.state?.chargeAmount,
            handleClickCancelButton,
            handleClickExecuteButton,
            renderValueActionHistory,
            bankInfo?.BankName,
        ]
    );
};
export default memo(ChargeIntBnkPay006);
