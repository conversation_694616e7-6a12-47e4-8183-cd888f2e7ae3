.common-component {
    &.common-label-container {
        .label {
            font-size: 16px;
            font-family: "Noto Sans JP", Meiryo, メイリオ, Arial, Helvetica, sans-serif;
            color: #222222;
        }

        .sub-label {
            margin-top: 4px;
            font-size: 12px;
            color: #222222;
        }

        .required {
            margin-left: 4px;
            transform: translateY(-1px);

            &-container {
                border-width: 1px;
                border-color: #d31e2d;
                border-style: solid;
                padding-left: 4px;
                padding-right: 4px;
                height: 20px;
            }

            .label {
                font-size: 11px;
                font-family: "Noto Sans JP Bold", Meiryo, メイリオ, Arial, Helvetica, sans-serif;
                color: #d31e2d;
            }
        }

        .optional {
            margin-left: 4px;
            transform: translateY(-3px);

            &-container {
                background-color: #fff;
                border: 1px solid #707070;
                padding-left: 4px;
                padding-right: 4px;
                height: 20px;
            }

            .optional-label-container {
                border: solid 1px #666666;
                padding: 0 2px;
            }

            .label {
                font-size: 11px;
                font-family: "Noto Sans JP Bold", <PERSON><PERSON>, メイリオ, <PERSON><PERSON>, Helvetica, sans-serif;
                color: #707070;
            }
        }
    }

    .common-error-container {
        margin-top: 8px;
        min-height: 22px;

        .error {
            padding: 0px 4px;
            font-size: 14px;
            font-family: "Noto Sans JP", Meiryo, メイリオ, Arial, Helvetica, sans-serif;
            color: #d31e2d;
            line-height: 22px;
        }
    }
}
