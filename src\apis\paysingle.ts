import API from '../constants/api';
import { BaseResponse } from '../models/common';
import {
    PaySingle001MainSearchStorePublicCodeModel,
    PaySingle001MainSearchStorePublicCodeResult,
} from '../models/paysingle001-main-search-storepubliccode';
import {
    PaySingle002MainSearchStorePublicCodeModel,
    PaySingle002MainSearchStorePublicCodeResult,
} from '../models/paysingle002-main-search-storepubliccode';
import {
    PaySingle004MainExecutePaymentModel,
    PaySingle004MainExecutePaymentResult,
} from '../models/paysingle004-main-execute-payment';
import createAPI from './baseApi';

class PaySingleAPI {
    /**
     * paySingle001MainSearchStorePublicCode
     * @params data PaySingle001MainSearchStorePublicCodeModel
     * @returns Promise<BaseResponse<PaySingle001MainSearchStorePublicCodeResult>
     */
    static paySingle001MainSearchStorePublicCode = (
        data: PaySingle001MainSearchStorePublicCodeModel
    ): Promise<BaseResponse<PaySingle001MainSearchStorePublicCodeResult>> => {
        return createAPI<PaySingle001MainSearchStorePublicCodeResult>({
            url: API.PAY_SINGLE001_MAIN_SEARCH_STORE_PUBLIC_CODE,
            data,
        });
    };

    /**
     * paySingle002MainSearchStorePublicCode
     * @params data PaySingle002MainSearchStorePublicCodeModel
     * @returns Promise<BaseResponse<PaySingle002MainSearchStorePublicCodeResult>
     */
    static paySingle002MainSearchStorePublicCode = (
        data: PaySingle002MainSearchStorePublicCodeModel
    ): Promise<BaseResponse<PaySingle002MainSearchStorePublicCodeResult>> => {
        return createAPI<PaySingle002MainSearchStorePublicCodeResult>({
            url: API.PAY_SINGLE002_MAIN_SEARCH_STORE_PUBLIC_CODE,
            data,
        });
    };

    /**
     * paySingle004MainExecutePayment
     * @params data PaySingle004MainExecutePaymentModel
     * @returns Promise<BaseResponse<PaySingle004MainExecutePaymentResult>>
     */
    static paySingle004MainExecutePayment = (
        data: PaySingle004MainExecutePaymentModel
    ): Promise<BaseResponse<PaySingle004MainExecutePaymentResult>> => {
        return createAPI({
            url: API.PAY_SINGLE004_MAIN_EXECUTE_PAYMENT,
            data,
        });
    };
}

export default PaySingleAPI;
