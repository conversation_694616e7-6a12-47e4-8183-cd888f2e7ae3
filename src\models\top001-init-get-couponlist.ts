import { CommonCallback } from './common';

/**
 * Top001InitGetCouponListModel
 */
export interface Top001InitGetCouponListModel extends CommonCallback {
    // ブランドID
    BrandID: string;
    // 1ページあたりの取得アイテム数
    PageSize: number;
}

/**
 * Top001InitGetCouponItem
 */
export interface Top001InitGetCouponItem {
    // クーポンID
    OpenCouponID?: string;
    // タイトル
    Title?: string;
    // 画像
    CouponTopImage?: string;
    // コンテンツ
    Content?: string;
    // イベントチケット説明
    StoreName?: string;
    // 配付終了日時
    EndDateTime?: string;
}

/**
 * Top001InitGetCouponListResult
 */
export interface Top001InitGetCouponListResult {
    // クーポン一覧
    CouponList: Top001InitGetCouponItem[];
}
