import { CommonCallback } from './common';

/**
 * RocketsSurvey001InitGetSurveyListModel
 */
export interface RocketsSurvey001InitGetSurveyListModel extends CommonCallback {
    // アンケートID
    SurveyID: string;
}

/**
 * RocketsSurvey001InitGetSurveyAnswerItem
 */
export interface RocketsSurvey001InitGetSurveyAnswerItem {
    // 選択回答ID
    ChoiceID: string;
    // 選択回答内容
    Text: string;
    // 自由記述回答フラグ
    FreeInputFlag: boolean;
}

/**
 * RocketsSurvey001InitGetSurveyQuestionItem
 */
export interface RocketsSurvey001InitGetSurveyQuestionItem {
    // 質問ID
    QuestionID: string;
    // 質問タイプ
    QuestionType: string;
    // 必須フラグ
    RequireFlag: boolean;
    // 質問文
    Text: string;
    // アンケート選択回答内容
    AnswerList: RocketsSurvey001InitGetSurveyAnswerItem[];
}

/**
 * RocketsSurvey001InitGetSurveyListResult
 */
export interface RocketsSurvey001InitGetSurveyListResult {
    // アンケートID
    SurveyID: string;
    // タイトル
    SurveyTitle: string;
    // 説明
    SurveyDetail: string;
    // 開始日時
    StartDateTime: string;
    // 終了日時
    EndDateTime: string;
    // アンケート質問内容
    QuestionList: RocketsSurvey001InitGetSurveyQuestionItem[];
}
