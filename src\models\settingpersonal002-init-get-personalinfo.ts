import { CommonCallback } from './common';

/**
 * Basic4InfoItem
 */
export interface Basic4InfoItem {
    Name?: string;
    Birthday?: string;
    Gender?: number;
    Address?: string;
}

/**
 * MCASCheerDetailsItem
 */
export interface MCASCheerDetailsItem {
    IdentityVerifiedFlag?: boolean;
    Basic4Info?: Basic4InfoItem;
}

/**
 * SettingPersonal002InitGetPersonalInfoResult
 */
export interface SettingPersonal002InitGetPersonalInfoResult extends CommonCallback {
    LastName: string;
    FirstName: string;
    DateOfBirth: string;
    Gender: string;
    PostCode: string;
    Prefectures: string;
    Municipalities: string;
    TownArea: string;
    HouseNumber: string;
    BuildingName: string;
    MCASCheerDetails?: MCASCheerDetailsItem;
}
