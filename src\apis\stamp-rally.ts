import API from '../constants/api';
import { BaseResponse } from '../models/common';
import {
    StampRally001InitGetStampRallyListModel,
    StampRally001InitGetStampRallyListResult,
} from '../models/stamprally001-init-get-stamp-rally-list';
import {
    StampRally002InitGetStampRallyDetailModel,
    StampRally002InitGetStampRallyDetailResult,
} from '../models/stamprally002-init-get-stamp-rally-detail';
import {
    StampRally003InitGetStampRallyItemModel,
    StampRally003InitGetStampRallyItemResult,
} from '../models/stamprally003-init-get-stamp-rally-Item';
import createAPI from './baseApi';

/**
 * StampRallyAPI
 */
class StampRallyAPI {
    /**
     * stampRally001InitGetStampRallyList
     * @param data StampRally001InitGetStampRallyListModel
     * @returns Promise<BaseResponse<StampRally001InitGetStampRallyListResult>>
     */
    static stampRally001InitGetStampRallyList = (
        data: StampRally001InitGetStampRallyListModel
    ): Promise<BaseResponse<StampRally001InitGetStampRallyListResult>> => {
        return createAPI<StampRally001InitGetStampRallyListResult>({
            url: API.STAMPRALLY001_INIT_GET_STAMPRALLY_LIST,
            data: data,
        });
    };

    /**
     * stampRally002InitGetStampRallyDetail
     * @param data StampRally002InitGetStampRallyDetailModel
     * @returns Promise<BaseResponse<StampRally002InitGetStampRallyDetailResult>>
     */
    static stampRally002InitGetStampRallyDetail = (
        data: StampRally002InitGetStampRallyDetailModel
    ): Promise<BaseResponse<StampRally002InitGetStampRallyDetailResult>> => {
        return createAPI<StampRally002InitGetStampRallyDetailResult>({
            url: API.STAMPRALLY002_INIT_GET_STAMPRALLY_LIST_DETAIL,
            data: data,
        });
    };

    /**
     * stampRally003InitGetStampRallyItem
     * @param data StampRally003InitGetStampRallyItemModel
     * @returns Promise<BaseResponse<StampRally003InitGetStampRallyItemResult>>
     */
    static stampRally003InitGetStampRallyItem = (
        data: StampRally003InitGetStampRallyItemModel
    ): Promise<BaseResponse<StampRally003InitGetStampRallyItemResult>> => {
        return createAPI<StampRally003InitGetStampRallyItemResult>({
            url: API.STAMPRALLY003_INIT_GET_STAMPRALLY_LIST_ITEM,
            data: data,
        });
    };
}

export default StampRallyAPI;
