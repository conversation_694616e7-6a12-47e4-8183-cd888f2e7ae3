import { CommonCallback } from './common';

/**
 * Login000MainGetEntryEnableFlagModel
 */
export interface Login000MainGetEntryEnableFlagModel extends CommonCallback {
    // ブランドID
    BrandID?: string;
}

/**
 * Login000MainGetEntryEnableFlagResult
 */
export interface Login000MainGetEntryEnableFlagResult {
    // 新規登録可否
    EntryButtonEnableFlag: boolean;
    // 新規登録開始日時
    EntryButtonReleaseDay: string;
    // 新規登録終了日時
    EntryButtonCloseDay: string;
}
