import { CommonCallback } from './common';

/**
 * ChargeIntBnkPay009MainRegisterBankAccountModel
 */
export interface ChargeIntBnkPay009MainRegisterBankAccountModel extends CommonCallback {
    // 銀行コード
    BankCode: string;
    // 口座名義人名
    AccountName: string;
    // 支店コード
    BranchCode: string;
    // 口座番号
    AccountNum: string;
    // 預金種別
    DepositType: number;
}

/**
 * ChargeIntBnkPay009MainRegisterBankAccountResult
 */
export interface ChargeIntBnkPay009MainRegisterBankAccountResult {
    // 口座ラベル
    AccountLabel: string;
    // 戻り先URL
    BackURL: string;
    // 会員用アクセストークン
    CustomerAccessToken: string;
    // Pay事業者認証鍵世代番号
    ProcessorAuthenticationKeyIndex: string;
    // メッセージ認証コード
    MAC: string;
    // 銀行コード
    BankCode: string;
    // 口座名義人名
    AccountName?: string;
    // 支店コード
    BranchCode?: string;
    // 口座番号
    AccountNum?: string;
    // 預金種別
    DepositType?: string;

    // field return by 3rd party it can be have or not have
    vResultCode?: string;
    accountId?: string;
    accountLabel?: string;
    backUrl?: string;
    bankpayAccountsRegisterUrl?: string;
    customerAccessToken?: string;
    mac?: string;
    memberId?: string;
    preparedProcessId?: string;
    processId?: string;
    processorAuthenticationKeyIndex?: string;
    merrMsg?: string;
    mstatus?: string;
    serviceType?: string;
}
