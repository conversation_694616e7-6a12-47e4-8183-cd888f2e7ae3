import { CommonCallback } from './common';

/**
 * NftCollection001InitGetNftUserListModel
 */
export interface NftCollection001InitGetNftUserListModel extends CommonCallback {
    // 表示フラグ
    ViewFlag: number;
}

/**
 * NftCollection001InitGetNftUserItem
 */
export interface NftCollection001InitGetNftUserItem {
    // NFTコンテンツID
    NFTContentID: string;
    // コンテンツ画像
    ContentImage: string;
    // シリアルナンバー
    SerialNumber: number;
    // 発行日時
    PublishDate: string;
    // カードID
    NFTCardID: string;
    // コンテンツ名
    ContentName: string;
    // 特典チケットフラグ(true：特典チケットあり、false：特典チケットなし)
    TicketFlg: boolean;
    // 先行販売中フラグ(true：先行販売中、false：先行販売期間外)
    PreSaleSalesFlg: boolean;
    // IPIDフラグ(true：IPID設定あり、false：IPID設定なし)
    IPIDFlg: boolean;
    // 二次流通チケット再配布フラグ(true：移転時にチケットを新たに配布する、false：移転時にチケットを配布しない)
    ResaleTicketFlg: boolean;
    // 所有状況区分
    Ownership: number;
    // コンテンツ画像（アクセス制限用）
    ContentImageSigned: string;
    // NFTカテゴリID
    NFTCategoryID: string;
}

/**
 * NftCollection001InitGetNftUserListResult
 */
export interface NftCollection001InitGetNftUserListResult {
    // CardList
    CardList: NftCollection001InitGetNftUserItem[];
    //  応援者名
    CheerName: string;
    // 所持NFT総数
    TotalCount: number;
    // 所持NFT種類数
    UniqueCount: number;
}
