#common-qr-scan-container {
    background-color: #222222;
    height: 100vh;
    overflow: auto;
    .qr-scan-header {
        background-color: #222222;
        border-bottom: 1px solid #cccccc;
        &-title {
            color: #cccccc;
            align-items: center;
            font-family: '<PERSON>o Sans JP Bold', Meiryo, メイリオ, Arial, Helvetica, sans-serif;
            font-size: 15px;
        }
        .back-icon {
            min-width: 52px;
            padding-left: 15px;
            cursor: pointer;
            display: flex;
            align-items: center;
        }
        .close-icon {
            min-width: 52px;
            min-height: 50px;
            display: flex;
            justify-content: center;
            align-items: center;
            overflow: hidden;
            padding-left: 16px;
            padding-right: 16px;
            cursor: pointer;
        }
    }
    .qr-scan-body {
        position: relative;
        overflow: hidden;
        .qr-scan-title {
            text-align: center;
            color: #ffffff;
            padding: 20px 10px;
            font-size: 16px;
            font-family: '<PERSON><PERSON> Bold', <PERSON><PERSON>, メイリオ, Arial, Helvetica, sans-serif;
        }
        .qr-reader-item {
            section {
                padding-top: 110% !important;
                @media screen and (max-height: 800px) and (min-width: 600px) {
                    padding-top: 80% !important;
                }
            }
        }
        .qr-red-center-box {
            top: 0px;
            left: 0px;
            z-index: 1;
            box-sizing: border-box;
            border: 50px solid rgba(0, 0, 0, 0.3);
            box-shadow: rgba(255, 0, 0, 0.5) 0px 0px 0px 5px inset;
            position: absolute;
            width: 100%;
            height: 100%;
        }
    }
    .qr-scan-input {
        padding: 30px 10px 10px 20px;
        cursor: pointer;
        .qr-scan-input-description {
            color: #ffffff;
        }
        .icon-circle {
            margin-right: 16px;
            display: inline-flex;
            align-items: center;
            transform: translateY(4px);
        }
    }
    .close-icon {
        min-width: 52px;
        min-height: 50px;
        display: flex;
        justify-content: center;
        align-items: center;
        overflow: hidden;
        padding-left: 16px;
        padding-right: 16px;
        cursor: pointer;
    }
}
.qr-permission-error {
    background-color: #ffffff;
    padding: 15px;
    border-radius: 5px;
    margin: auto 1rem !important;
    display: flex;
    flex-direction: column;
    max-height: 100%;
    &-message {
        white-space: break-spaces;
        word-break: break-all;
        font-size: 15px;
        flex: 1;
    }
}
.dialog-error-permission-error {
    margin-top: unset !important;
    margin-bottom: unset !important;
}
.dialog-error-children {
    height: calc(100% - 88px);
}
