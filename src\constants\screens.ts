// define screen path here
const Screens = {
    APP: '/',
    HOME: '/',
    WALLET001: '/wallet',
    LOGIN: '/login',
    LOGIN001: '/login001',
    LOGIN_OPEN_ID: '/login-open-id',
    APPLICATION_FORM: '/application-form',
    APPLICATION_INPUT: '/application-input',
    APPLICATION_CONFIRM: '/application-confirm',
    FAQ: '/faq',
    NEWS: '/news',
    MY_NEWS: '/news/my',
    IMPORTANT_NEWS: '/news/important',
    DETAIL_NEWS: '/news/detail/:id',
    MENU: '/menu',
    MENU002: '/menu002',
    CAMPAIGN: '/campaign',

    // for register workflow
    REGISTER: '/register',
    REGISTER_ACCOUNT: '/register/account',
    REGISTER_ACCOUNT_AUTH: '/register/account',
    REGISTER_ACCOUNT_VERIFY: '/register/account/verify',
    REGISTER_ACCOUNT_TERMS: '/register/account/termsUse',
    REGISTER_ACCOUNT_INFO: '/register/account/info',

    PAYMENT_AMOUNT: '/payment/amount',
    PAYMENT_STORE_INPUT: '/payment/store-input',
    PAYMENT_SELECTION: '/payment/selection',
    PAYMENT_CONFIRM: '/payment/confirm',
    PAYMENT_COMPLETE: '/payment/complete',
    STORE_LIST: '/store-list',
    STORE_DETAIL: '/store/:id/detail',
    CHARGE: '/charge',
    // charge input
    CHARGE_INPUT: '/charge/input',
    // type payment
    CARD: '/card',
    CVS: '/cvs',
    BANK: '/bank',
    BANKPAY: '/bankpay',

    CHARGE_CONFIRM: '/charge/confirm',
    CHARGE_COMPLETE: '/charge/complete',
    CHARGE_QR: '/charge/qr',
    CHARGE_QR_SCAN: '/charge/qr/scan',
    CHARGE_QR_INPUT: '/charge/qr/input',
    CHARGE_QR_COMPLETE: '/charge/qr/complete',
    CHARGE_PURCHASE_GIFT: '/charge/purchase-gift',
    CHARGE_PURCHASE_GIFT_PAYMENT_METHOD: '/charge/purchase-gift/method',
    CHARGE_PURCHASE_GIFT_COMPLETE: '/charge/purchase-gift/complete',
    CHARGEPREPID001: '/charge/serial-code',
    CHARGEPREPID002: '/charge/serial-code/complete',
    CHARGE_SERIAL_CODE_SCAN: '/charge/serial-code/scan',
    CHARGEINT_BNKPAY001: '/charge/bnkpay',
    CHARGEINT_BNKPAY002: '/charge/bnkpay/002',
    CHARGEINT_BNKPAY004: '/charge/bnkpay/004',
    CHARGEINT_BNKPAY005: '/charge/bnkpay/005',
    CHARGEINT_BNKPAY006: '/charge/bnkpay/006',
    CHARGEINT_BNKPAY007: '/charge/bnkpay/007',
    CHARGEINT_BNKPAY008: '/charge/bnkpay/008',
    CHARGEINT_BNKPAY009: '/charge/bnkpay/009',
    SPONSORS: '/sponsors',
    SETTING: '/setting',
    IDENTIFICATION001: '/identification001',
    // payment
    PAYMULTI: '/payment/home',
    PAYMULTI003: '/payment/home/<USER>',
    PAYMULTI002: '/payment/home/<USER>',
    PAYMULTI004: '/payment/home/<USER>',
    PAYMULTI005: '/payment/home/<USER>',
    PAYMULTI006: '/payment/home/<USER>',
    PAYSINGLE001: '/payment/gift',
    PAYSINGLE002: '/payment/gift/store-input',
    PAYSINGLE003: '/payment/gift/input',
    PAYSINGLE004: '/payment/gift/confirm',
    PAYSINGLE005: '/payment/gift/complete',
    PAYMENT_GIFT_CERTIFICATE_SUCCESS: '/payment/gift/success',
    CONTACT: '/contact',
    RESET_PASS: '/reset-pass',
    RESET_PASS_PIN: '/reset-pass/pin',
    RESET_PASS_ZODIAC: '/reset-pass/zodiac',
    RESET_PASS_CONFIRM: '/reset-pass/confirm',
    RESET_PASS_COMPLETE: '/reset-pass/complete',
    ICONSLIST: '/iconslist',
    // Setting workflow
    SETTING_CHANGE_EMAIL: '/setting/change-email',
    SETTING_CHANGE_EMAIL_INPUT: '/setting/change-email/input',
    SETTING_CHANGE_EMAIL_VERIFY: '/setting/change-email/verify',
    SETTING_CHANGE_EMAIL_COMPLETE: '/setting/change-email/complete',
    SETTING_CHANGE_TEL: '/setting/change-tel',
    SETTING_CHANGE_TEL_ZODIAC: '/setting/change-tel/zodiac',
    SETTING_CHANGE_TEL_INPUT: '/setting/change-tel/input',
    SETTING_CHANGE_TEL_PIN: '/setting/change-tel/pin',
    SETTING_CHANGE_TEL_COMPLETE: '/setting/change-tel/complete',
    SETTING_CHANGE_ZODIAC: '/setting/change-zodiac',
    SETTING_CHANGE_ZODIAC_NEW: '/setting/change-zodiac/new',
    SETTING_CHANGE_PASSWORD: '/setting/change-password',
    SETTING_CHANGE_PASSWORD_INPUT: '/setting/change-password/input',
    SETTING_CHANGE_PASSWORD_COMPLETE: '/setting/change-password/complete',
    SETTING_WITHDRAW_ACCOUNT: '/setting/withdraw-account',
    SETTINGPERSONAL001: '/setting/personal',
    SETTINGPERSONAL002: '/setting/personal/view',
    SETTINGPERSONAL003: '/setting/personal/input',
    SETTINGPERSONAL004: '/setting/personal/complete',
    // Coupons
    COUPON_LIST: '/coupon',
    COUPON_LIST_AVAILABLE: '/coupon/available',
    COUPON_LIST_USED: '/coupon/used',
    COUPON_DETAIL: '/coupon/detail/:id',
    COUPON_USE_DETAIL: '/coupon/detail/use/:id',
    COUPON_USE_CONFIRM: '/coupon/confirm',
    // History
    HISTORY001: '/history/:medalServiceId',
    HISTORY_TRANSACTION: '/history/:medalServiceId/list',
    HISTORY_EXPIRY: '/history/:medalServiceId/expiry/list',
    // Survey
    SURVEY001: '/survey/survey001',
    SURVEY002: '/survey/input/:surveyId',
    SURVEY003: '/survey/input/confirm/:surveyId',
    SURVEY004: '/survey/completed',
    // Charge vouchr
    CHARGE_VOUCHR001: '/charge/gift-input',
    CHARGE_VOUCHR002: '/charge/gift-payment',
    CHARGE_VOUCHR003: '/charge/gift-complete',
    // Charge qr read
    CHARGE_QR_CODE_READ_001: '/charge/qr-code-read',
    CHARGE_QR_CODE_READ_002: '/charge/qr-code-read/002',
    CHARGE_QR_CODE_READ_003: '/charge/qr-code-read/003',

    // Ticket
    TICKET001: '/ticket/ticket001',
    TICKET001_AVAILABLE: '/ticket/ticket001/available',
    TICKET001_USED: '/ticket/ticket001/used',
    TICKET002: '/ticket/ticket002/:userEventTicketID',
    TICKET003: '/ticket/ticket003',

    // Events
    EVENT: '/event',
    EVENT001: '/event/event001',
    EVENT002: '/event/event002',
    EVENT003: '/event/:id/detail',
    EVENT004: '/event/event004',
    EVENT005: '/event/event005',
    EVENT006: '/event/event006',
    // Stamp Rally
    STAMP_RALLY_HOME: '/stamp-rally',
    STAMP_RALLY001: '/stamp-rally/stamp-rally001',
    STAMP_RALLY002: '/stamp-rally/stamp-rally002/:stampRallyId',
    STAMP_RALLY005: '/stamp-rally/stamp-rally005',
    STAMP_RALLY007: '/stamp-rally/stamp-rally007',
    STAMP_RALLY004: '/stamp-rally/stamp-rally004',
    STAMP_RALLY006: '/stamp-rally/stamp-rally006',

    // charge qr code display
    CHARGE_QR_CODE_PRESENT001: '/charge-qr-code/present001',

    // Event Form
    EVENT_FORM: '/event-form',
    EVENT_FORM001: '/event-form/event-form001',
    EVENT_FORM002: '/event-form/event-form002/:formId',
    EVENT_FORM003: '/event-form/event-form003',

    // NFT
    NFT: '/nft',
    NFT001: '/nft/nft001',
    NFT002: '/nft/nft002/:categoryID',
    NFT003: '/nft/nft003/:categoryID/:nftContentID',
    NFT005: '/nft/nft005',
    NFT006: '/nft/nft006',
    NFT007: '/nft/nft007',
    NFT008: '/nft/nft008',
    NFT009: '/nft/nft009',
    NFT011: '/nft/nft011',

    NFT_COLLECTION: '/nft/collection',
    NFT_COLLECTION001: '/nft/collection001',
    NFT_COLLECTION002: '/nft/collection002/:nftContentID/:serialNumber',
    // Mission
    MISSION_HOME: '/mission',
    MISSION002: '/mission/mission002/:missionId',
    MISSION003: '/mission/mission003',
    MISSION005: '/mission/mission005/:missionId/:missionDetailId',

    // EC routers
    EC: '/ec',
    EC_ITEM001: '/ec/item001',
    EC_ITEM002: '/ec/item002/:itemId',
    EC_CART001: '/ec/cart001',
    EC_PURCHASE001: '/ec/purchase001',
    EC_PURCHASE002: '/ec/purchase002',
    EC_PURCHASE003: '/ec/purchase003',
    EC_PURCHASE004: '/ec/purchase004',
    EC_PAYMENT001: '/ec/payment001',

    DELETE_ACCOUNT001: '/setting/delete-account001',
    DELETE_ACCOUNT002: '/setting/delete-account002',
    DELETE_ACCOUNT003: '/setting/delete-account003',

    TERM_OF_USE: '/term-of-use',
    TERMS: '/terms',
    LAW: '/law',

    // Item
    ITEM: '/item',
    ITEM001: '/item/item001',
    // DAO Voting
    DAO_VOTING001: '/:daoId/voting/voting001',
    DAO_VOTING004: '/:daoId/voting/voting004',
    DAO_VOTING004_DETAIL: '/:daoId/voting/voting004/:themeId',
    DAO_VOTING_PREVIEW: '/:daoId/voting/voting004/preview',
    DAO_VOTING_CREATION: '/:daoId/dao-voting-creation',
    DAO_VOTING002: '/:daoId/voting/voting002/:themeId',

    // DAO Community
    DAO_COMMUNITY001_NO_ID: '/:daoId/community/community008',
    DAO_COMMUNITY001: '/:daoId/community/community008/:categoryId/:channelId',
    DAO_COMMUNITY001_WITH_THREAD_ID: '/:daoId/community/community008/:categoryId/:channelId/:threadId',
    DAO_COMMUNITY001_WITH_CHAT_ID: '/:daoId/community/community008/:categoryId/:channelId/:threadId/:chatId',
    DAO_COMMUNITY002: '/:daoId/community/community002',
    DAO_COMMUNITY002_MENTIONS: '/:daoId/community/community002/mentions',
    DAO_COMMUNITY002_EDIT: '/:daoId/community/community002/edit',
    DAO_COMMUNITY003: '/:daoId/community/community003',
    DAO_COMMUNITY003_COMPLETED: '/:daoId/community/community003/completed',
    DAO_COMMUNITY004: '/:daoId/community/community004',
    DAO_COMMUNITY004_COMPLETED: '/:daoId/community/community004/completed',
    DAO_COMMUNITY005: '/:daoId/community/community005',
    DAO_COMMUNITY005_COMPLETED: '/:daoId/community/community005/completed',
    DAO_COMMUNITY006: '/:daoId/community/community006',
    DAO_COMMUNITY006_COMPLETED: '/:daoId/community/community006/completed',

    // DAO Top
    DAO_TOP: '/:daoId/top/top001',
    DAO_TOP001: '/dao',
    DAO_TOP002: '/:daoId/top/top002',
    DAO_TOP003: '/:daoId/top/top003',
    DAO_TOP004: '/:daoId/top/top004',
    DAO_TOP005: '/:daoId/top/top005',
    DAO_TOP006: '/:daoId/top/top006',
    DAO_TOP_PREVIEW: '/:daoId/top/top-preview',

    // mobile order routers
    MOBILE_ORDER_ITEM001: '/mobile-order/:storeID/item001',
    MOBILE_ORDER_ITEM002: '/mobile-order/:storeID/item002/:itemID',
    MOBILE_ORDER_CART001: '/mobile-order/:storeID/cart001',
    MOBILE_ORDER_HISTORY001: '/mobile-order/:storeID/history001',
    MOBILE_ORDER_CONFIRM001: '/mobile-order/:storeID/confirm001',

    // transfer
    TRANSFER: '/transfer',
    TRANSFER001: '/transfer/transfer001/:medalServiceId',
    TRANSFER002: '/transfer/transfer002',
    TRANSFER003: '/transfer/transfer003',
    TRANSFER004: '/transfer/transfer004',
    TRANSFER005: '/transfer/transfer005',
    TRANSFER006: '/transfer/transfer006',
    TRANSFER007: '/transfer/transfer007',
    TRANSFER008: '/transfer/transfer008',
    TRANSFER009: '/transfer/transfer009',

    // brand
    BRAND: '/brand',
    BRAND001: '/brand/brand001',
    BRAND002: '/brand/brand002',

    // QRCODE Present
    QRCODE_PRESENT: '/qrcode-present',
    QRCODE_PRESENT001: '/qrcode-present/qrcode-present001',
    QRCODE_PRESENT002: '/qrcode-present/qrcode-present002',
    QRCODE_PRESENT003: '/qrcode-present/qrcode-present003',

    // charge veritrans-popjs for native app
    VERITRANS_PAYMENT: '/veritrans-payment',

    // COMMON QR CODE READ
    COMMON_QRCODE_READ_002: '/common-qrcode-read002',

    // IDEA_DAO
    IDEA_DAO: '/ideadao',
    IDEADAO_TASK_DETAIL: '/ideadao/:daoId/tasks/:taskId',
    IDEA_DAO001: '/ideadao/:daoId',
    IDEA_DAO002: '/ideadao/ideadao002/:daoId',
    IDEA_DAO003: '/ideadao/ideadao003',
    IDEA_DAO004: '/ideadao/ideadao004',
    IDEA_DAO005: '/ideadao/:daoId/edit',
    IDEA_DAO006: '/ideadao/ideadao006',
    IDEA_DAO007: '/ideadao/:daoId/members',
    // IDEA_DAO008
    IDEA_DAO008_VIEW: '/ideadao/:daoId/profile',
    IDEA_DAO008_EDIT: '/ideadao/:daoId/profile/edit',
    IDEA_DAO008_CONFIRM: '/ideadao/:daoId/profile/confirm',
    IDEA_DAO010: '/ideadao/:daoId/tasks/create',
    IDEA_DAO010_CONFIRM_CREATE: '/ideadao/:daoId/tasks/confirm-create',
    // IDEA_DAO009
    IDEADAO_TASKS: '/ideadao/:daoId/tasks',
    IDEADAO_CREATE: '/ideadao/create',
    IDEADAO_DETAIL: '/ideadao/:daoId/detail',

    IDEADAO011: '/ideadao/:daoId/tasks/:taskId',
    IDEADAO011_OVERVIEW: '/ideadao/:daoId/tasks/:taskId/overview',
    IDEADAO011_FILE: '/ideadao/:daoId/tasks/:taskId/file',

    // MYNUMBER
    MY_NUMBER: '/my-number',
    MY_NUMBER001: '/my-number/my-number001',
    MY_NUMBER002: '/my-number/my-number002',
    MY_NUMBER003: '/my-number/my-number003',
    MY_NUMBER004: '/my-number/my-number004',
    MY_NUMBER005: '/my-number/my-number005',
    MY_NUMBER006: '/my-number/my-number006',
    MY_NUMBER008: '/my-number/my-number008',
    MY_NUMBER009: '/my-number/my-number009',
    MY_NUMBER010: '/my-number/my-number010',
    MY_NUMBER011: '/my-number/my-number011',

    // MYNUMBER Test
    MY_NUMBER_TEST: '/my-number',
    MY_NUMBER000_TEST: '/my-number/my_number000-test',
    MY_NUMBER002_TEST: '/my-number/my-number002-test',
    MY_NUMBER003_TEST: '/my-number/my-number003-test',
    MY_NUMBER005_TEST: '/my-number/my-number005-test',
    MY_NUMBER006_TEST: '/my-number/my-number006-test',
    MY_NUMBER008_TEST: '/my-number/my-number008-test',
    MY_NUMBER010_TEST: '/my-number/my-number010-test',

    LOGIN_OIDC: '/idpredirect',
};

export default Screens;
