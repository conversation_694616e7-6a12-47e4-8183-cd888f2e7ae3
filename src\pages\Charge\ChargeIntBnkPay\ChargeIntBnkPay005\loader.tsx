import { redirect } from 'react-router-dom';
import StorageServices from '../../../../services/storage.service';
import { Home001InitGetCoinItem } from '../../../../models/home001-init-get-coin';
import Screens from '../../../../constants/screens';

/**
 * chargeIntBnkPay005Loader
 * @returns Promise<EventForm002InitGetEventFormDetailResult | Response>
 */
const chargeIntBnkPay005Loader = async (): Promise<Home001InitGetCoinItem | Response> => {
    const dataMedalServiceItem = StorageServices.Local.get('medalServiceItem') as Home001InitGetCoinItem;
    if (dataMedalServiceItem) {
        return dataMedalServiceItem;
    }

    return redirect(Screens.WALLET001);
};

export default chargeIntBnkPay005Loader;
