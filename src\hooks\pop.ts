import { useCallback, useEffect, useState } from 'react';
import CommonAPI from '../apis/common';
import API from '../constants/api';
import Environments from '../constants/environments';
import StorageServices from '../services/storage.service';
import CommonUtils from '../utils/common';
import Utils, { __DEV__ } from '../utils/utils';

/**
 * usePopJS
 */
const usePopJS = (): void => {
    const session = StorageServices.Session.get<string>('data-client-key');
    const [clientKey, setClientKey] = useState<string>('');

    /**
     * handleGetClientKey
     * @param count number. Default 1
     */
    const handleGetClientKey = useCallback(async (count: number) => {
        if (count > 5) {
            CommonUtils.showMessage({
                message: Utils.t('error_messages.system_error'),
                type: 'ERROR',
            });
            return;
        }
        __DEV__ && console.log('[handleGetClientKey] current retry: ', count);
        __DEV__ && console.log('[handleGetClientKey] START waiting for 5s');
        __DEV__ && (await Utils.sleep(5000));
        __DEV__ && console.log('[handleGetClientKey] END waiting for 5s');
        const { result } = await CommonAPI.commonGetClientKey({
            useLock: false,
            BrandID: Environments.brandID,
        });

        __DEV__ && console.log('[handleGetClientKey] PopClientKey: ', result?.PopClientKey);
        if (result?.PopClientKey) {
            setClientKey(result.PopClientKey);
        } else {
            __DEV__ && console.log('[handleGetClientKey] PopClientKey empty, run reload process.');
            await Utils.sleep(1000);
            handleGetClientKey(++count);
        }
    }, []);

    useEffect(() => {
        if (session) {
            setClientKey(session);
        } else {
            handleGetClientKey(1);
        }
    }, [handleGetClientKey, session]);

    /**
     * handleLoadPopJS
     * @param key string
     * @param count number. Default 1
     */
    const handleLoadPopJS = useCallback(async (key: string, count = 1) => {
        if (count > 5) {
            CommonUtils.showMessage({
                message: Utils.t('error_messages.system_error'),
                type: 'ERROR',
            });
            return;
        }
        __DEV__ && console.log('[handleLoadPopJS] current retry: ', count);
        const popScripts = document.querySelectorAll('script#pop-veritrans-jp');
        const popIframes = document.querySelectorAll('iframe#pop-veritrans');

        popScripts && popScripts.forEach((el) => el.remove());
        popIframes && popIframes.forEach((el) => el.remove());

        await Utils.sleep(500);

        const script = document.createElement('script');
        script.id = 'pop-veritrans-jp';
        script.type = 'text/javascript';
        script.addEventListener('error', () => {
            __DEV__ && console.log('load js file error, run reload process.');
            handleLoadPopJS(key, ++count);
        });
        script.setAttribute('data-client-key', key);
        script.src = `${API.POP_VERITRANS_URL}?timestamp=${new Date().getTime()}`;

        __DEV__ && console.log('[handleLoadPopJS] START waiting for 5s');
        __DEV__ && (await Utils.sleep(5000));
        document.head.appendChild(script);
        __DEV__ && console.log('[handleLoadPopJS] END waiting for 5s');
    }, []);

    useEffect(() => {
        if (clientKey) {
            StorageServices.Session.set('data-client-key', clientKey);
            handleLoadPopJS(clientKey);
        }
    }, [clientKey, handleLoadPopJS]);
};

export default usePopJS;
