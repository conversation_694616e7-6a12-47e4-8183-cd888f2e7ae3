import { BrowserQRCodeReader, IScannerControls } from '@zxing/browser';
import { ReactNode, memo, useCallback, useEffect, useMemo, useRef } from 'react';
import { CameraUtils } from '../../utils/camera';
import { __DEV__ } from '../../utils/common';
import './styles.scss';

type ScanResultType =
    | 'MediaStreamNotFound'
    | 'StreamDecodeFailure'
    | 'VideoCannotPlayError'
    | 'QrCodeScanTimeout'
    | 'UnknownError';

/**
 * ScanResult
 */
export interface ScanResult {
    /**
     * status
     */
    status: 'SUCCESS' | 'FAILURE';
    /**
     * result
     */
    result?: string;
    /**
     * error
     */
    error?: string;
    /**
     * fullError
     */
    fullError?: unknown;
    /**
     * type
     * Only available when error occurred
     */
    type?: ScanResultType;
}

/**
 * QrCodeReaderProps
 */
export interface QrCodeReaderProps {
    /**
     * videoConstraints
     */
    videoConstraints?: MediaTrackConstraints;
    /**
     * ViewFinder
     */
    ViewFinder?: ReactNode;
    /**
     * scanTimeout
     */
    scanTimeout?: number;
    /**
     * onScanResult
     * @param result ScanResult
     */
    onScanResult?: (result: ScanResult) => void;
    /**
     * onLoaded
     * fire when component is already loaded
     */
    onLoaded?: () => void;
}

/**
 * QrCodeReader
 * @returns React.JSX.Element
 */
const QrCodeReader = (props: QrCodeReaderProps): React.JSX.Element => {
    const {
        onScanResult,
        videoConstraints = {
            width: 600,
            height: 600,
            facingMode: 'environment',
            aspectRatio: 1,
        },
        ViewFinder,
        scanTimeout,
        onLoaded,
    } = useMemo(() => props, [props]);
    const codeReader = new BrowserQRCodeReader();
    const videoWrapperRef = useRef<HTMLDivElement>(null);
    const timer = useRef<NodeJS.Timer>();
    const codeReaderResult = useRef<IScannerControls>();

    /**
     * handleQRCodeReader
     */
    const handleQRCodeReader = useCallback(async () => {
        try {
            // get media devices
            const mediaStream = await CameraUtils.getUserMedia({
                video: videoConstraints,
                audio: false,
            });

            // cannot get media stream
            if (!mediaStream) {
                onScanResult?.({
                    status: 'FAILURE',
                    type: 'MediaStreamNotFound',
                });
                return;
            }

            // create video element
            const videoElement = document.getElementById('qr-code-reader-video') as HTMLVideoElement;

            // set video muted
            videoElement.muted = true;

            // set video source object is media stream
            await CameraUtils.setStream(videoElement, mediaStream);

            // after loaded metadata
            videoElement.onloadedmetadata = (): void => {
                // play video
                videoElement.play().then(() => {
                    // dispatch onLoaded event if available
                    onLoaded?.();
                });
            };

            // start timeout if scanTimeout is set
            if (scanTimeout) {
                timer.current = setTimeout(() => {
                    onScanResult?.({
                        status: 'FAILURE',
                        type: 'QrCodeScanTimeout',
                        error: 'Scan timeout',
                    });
                }, scanTimeout);
            }

            // listen for qr decode
            codeReaderResult.current = await codeReader.decodeFromStream(mediaStream, undefined, (response) => {
                if (response) {
                    const text = response.getText();
                    onScanResult?.({
                        status: 'SUCCESS',
                        result: text,
                    });
                }
            });
            // eslint-disable-next-line @typescript-eslint/no-explicit-any
        } catch (error: any) {
            onScanResult?.({
                status: 'FAILURE',
                error: error?.message,
                fullError: error,
                type: error?.name || 'UnknownError',
            });
        } finally {
            // clear timeout if available
            clearTimeout(timer.current);
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);

    useEffect(() => {
        handleQRCodeReader().catch((error) => {
            __DEV__ && console.log(error?.message);
        });
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);

    useEffect(() => {
        return () => {
            codeReaderResult?.current?.stop();
            // also clear timeout when component unmount
            clearTimeout(timer.current);
        };
    }, []);

    return useMemo(
        () => (
            <div ref={videoWrapperRef} id="qr-code-reader-wrapper">
                <video
                    width={'100%'}
                    height={'100%'}
                    id="qr-code-reader-video"
                    autoPlay={true}
                    playsInline={true}
                    muted={true}
                />
                {ViewFinder}
            </div>
        ),
        [ViewFinder]
    );
};

export default memo(QrCodeReader);
