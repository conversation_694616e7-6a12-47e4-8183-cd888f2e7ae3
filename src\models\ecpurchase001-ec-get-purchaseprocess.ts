import { CommonCallback } from './common';

/**
 * CartListItem
 */
export interface CartListItem {
    // 商品ID
    ItemID: string;
    // 商品名
    ItemName: string;
    // 商品画像ファイル名
    ItemImage: string;
    // 金額
    Price: number;
    // 数量
    Quantity: number;
}

/**
 * PickupLocationItem
 */
export interface PickupLocationItem {
    // 場所ID
    LocationID: string;
    // 場所名称
    LocationName: string;
}

/**
 * ECPurchase001ECGetPurchaseProcessModel
 */
export interface ECPurchase001ECGetPurchaseProcessModel extends CommonCallback {
    // 加盟店ID
    StoreID: string;
}

/**
 * ECPurchase001ECGetPurchaseProcessResult
 */
export interface ECPurchase001ECGetPurchaseProcessResult {
    // 氏名（姓）
    LastName?: string;
    // 氏名（名）
    FirstName?: string;
    // 電話番号
    TelephoneNumber?: string;
    // 選択済み受け取り場所ID
    SelectLocationID: string;
    // 商品リスト
    CartList: CartListItem[];
    // 商品リスト
    PickupLocationList: PickupLocationItem[];
}
