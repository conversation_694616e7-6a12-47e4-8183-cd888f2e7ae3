import { CommonCallback } from './common';

/**
 * Nft009MainExecutePaymentModel
 */
export type Nft009MainExecutePaymentModel = CommonCallback & {
    NFTContentID: string;
    StoreID: string;
    PaymentType: number;
    SerialNumber: number;
    CurrentLatitude: number;
    CurrentLongitude: number;
    Items: ItemsModel[];
};

/**
 * ItemsModel
 */
export interface ItemsModel {
    MedalServiceID: string;
    PaymentAmount: number;
}

/**
 * Nft009MainExecutePaymentResult
 */
export interface Nft009MainExecutePaymentResult {
    // ResultCode
    ResultCode: string;
    // Status
    Status: string;
    // Message
    Message: string;
    // OrderID
    OrderID: string;
    // PaymentKey
    PaymentKey: string;
    // PopClientKey
    PopClientKey: string;
}
