import { CommonCallback } from './common';

/**
 * Top001InitGetMissionListModel
 */
export interface Top001InitGetMissionListModel extends CommonCallback {
    // ブランドID
    BrandID: string;
    // 1ページあたりの取得アイテム数
    PageSize: number;
}

/**
 * Top001InitGetMissionItem
 */
export interface Top001InitGetMissionItem {
    // ミッションID
    MissionID: string;
    // ミッション画像
    MissionImage: string;
    // ミッション名
    MissionName: string;
    // ミッション開始日
    StartDateTime: string;
    // ミッション終了日
    EndDateTime: string;
    // ミッション説明
    MissionDescription: string;
}

/**
 * Top001InitGetMissionListResult
 */
export interface Top001InitGetMissionListResult {
    // ミッション一覧
    MissionList: Top001InitGetMissionItem[];
}
