.charge-int-bnk-pay006 {
    height: calc(100vh - 55px);

    .container-charging-confirm {
        .total-confirm {
            margin: 16px;
            .title_bank_name {
                color: #222222;
                margin-top: 30px;
            }
            .text-message-confirm {
                color: #222222;
                margin-top: 20px;
            }
            .confirm-value {
                font-family: 'Noto Sans JP Bold', Meiryo, メイリオ, Arial, Helvetica, sans-serif;
                color: #222222;
                text-align: end;

                span {
                    margin-left: 4px;
                }
            }
        }
    }
    .footer-confirm {
        margin: 16px;
        .text-confirm {
            color: #222222;
        }
    }

    .btn-container {
        gap: 23px;
    }
}

.icon-charge-int-bank-pay006-execute-fail {
    position: absolute;
    top: -36px;
    left: 50%;
    transform: translate(-50%, 0);
}