import _ from 'lodash';
import { ReactNode, memo, useMemo } from 'react';
import { ErrorBoundary, FallbackProps } from 'react-error-boundary';
import Button from '../Button/Button';
import Footer, { useCommonFooter } from '../Footer/Footer';
import Header, { HeaderProps, useCommonHeader } from '../Header/Header';
import ScrollToTop from '../ScrollToTop';
import { ScrollToTopProps } from '../ScrollToTop/ScrollToTop';

/**
 * ContainerProps
 */
export interface ContainerProps {
    /**
     * screen name
     */
    screenName: string;
    /**
     * children
     */
    children: ReactNode;
    /**
     * className
     */
    className?: string;
    /**
     * useHeader
     */
    useHeader?: boolean | HeaderProps;
    /**
     * useFooter
     */
    useFooter?: boolean;
    /**
     * useScrollToTop
     */
    useScrollToTop?: boolean | ScrollToTopProps;
}

/**
 * ErrorFallback
 * @param param FallbackProps
 * @returns React.JSX.Element
 */
export const ErrorFallback = ({ error, resetErrorBoundary }: FallbackProps): React.JSX.Element => {
    return (
        <div role="alert">
            <p>Something went wrong:</p>
            <pre>{error.message}</pre>
            <Button variant="outlined">Try again</Button>
        </div>
    );
};

/**
 * Container
 * @param prop ContainerProps
 * @returns React.JSX.Element
 */
const Container = (props: ContainerProps): React.JSX.Element => {
    const { useFooter, useHeader, children, className, useScrollToTop } = useMemo(() => props, [props]);
    const { height: headerHeight } = useCommonHeader(useHeader as boolean);
    const { height: footerHeight } = useCommonFooter(useFooter as boolean);

    // is show children
    const isShowChildren = useMemo(() => {
        if (useHeader && _.isNil(headerHeight)) {
            return false;
        }
        if (useFooter && _.isNil(footerHeight)) {
            return false;
        }
        return true;
    }, [footerHeight, headerHeight, useFooter, useHeader]);

    return useMemo(
        () => (
            <ErrorBoundary FallbackComponent={ErrorFallback}>
                {useHeader && <Header {...(useHeader as HeaderProps)} />}
                <div
                    className={className}
                    hidden={!isShowChildren}
                    style={{
                        paddingTop: headerHeight,
                        paddingBottom: footerHeight,
                    }}
                >
                    {children}
                </div>
                {useFooter && <Footer />}
                {useScrollToTop && <ScrollToTop {...(useScrollToTop as ScrollToTopProps)} />}
            </ErrorBoundary>
        ),
        [children, className, footerHeight, headerHeight, isShowChildren, useFooter, useHeader, useScrollToTop]
    );
};

export default memo(Container);
