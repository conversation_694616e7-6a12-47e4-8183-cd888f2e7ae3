import clsx from 'clsx';
import {
    ForwardedRef,
    InputHTMLAttributes,
    forwardRef,
    memo,
    useCallback,
    useEffect,
    useImperativeHandle,
    useMemo,
    useRef,
    useState,
} from 'react';
import './styles.scss';

// OmittedInputElement
type OmittedInputElement = Omit<InputHTMLAttributes<HTMLInputElement>, 'onChange' | 'prefix'>;

/**
 * InputQuantityProps
 */
interface InputQuantityProps extends OmittedInputElement {
    /**
     * minValue
     */
    minValue?: number;
    /**
     * maxValue
     */
    maxValue: number;
    /**
     * onChange
     */
    onChange: (quantity: number) => void;
}

/**
 * InputQuantityForwardedRef
 */
interface InputQuantityForwardedRef {
    /**
     * inputRef
     * input's references
     */
    inputRef: React.RefObject<HTMLInputElement>;
}

/**
 * InputQuantity
 * @returns React.JSX.Element
 */
const InputQuantity = forwardRef(
    (props: InputQuantityProps, ref: ForwardedRef<InputQuantityForwardedRef>): React.JSX.Element => {
        const { value: inputValue = '', minValue = 0, maxValue, onChange, onBlur, ...rest } = props || {};

        // input ref
        const inputRef = useRef<HTMLInputElement>(null);

        // current value of input
        const [value, setValue] = useState(inputValue);

        useEffect(() => {
            setValue(inputValue);
        }, [inputValue]);

        useImperativeHandle(
            ref,
            () => ({
                inputRef,
            }),
            []
        );

        /**
         * handleClickChangeQuantity
         * @param type string
         */
        const handleClickChangeQuantity = useCallback(
            (type: string) => () => {
                switch (type) {
                    case 'ADD':
                        if (Number(value) < maxValue) {
                            onChange(Number(value) + 1);
                            setValue((prev) => String(Number(prev) + 1));
                        }
                        break;
                    case 'SUBTRACT':
                        if (Number(value) > minValue) {
                            onChange(Number(value) - 1);
                            setValue((prev) => String(Number(prev) - 1));
                        }
                        break;
                    default:
                        break;
                }
            },
            [maxValue, minValue, onChange, value]
        );

        return useMemo(
            () => (
                <div className="input-quantity">
                    <div
                        className={clsx('subtract', {
                            disabled: maxValue === 0 || Number(value) === minValue || rest?.disabled,
                        })}
                        onClick={handleClickChangeQuantity('SUBTRACT')}
                    >
                        ー
                    </div>
                    <div className="quantity-number">
                        <input
                            {...rest}
                            ref={inputRef}
                            type="number"
                            value={value}
                            onBlur={onBlur}
                            autoComplete="off"
                            readOnly
                        />
                    </div>
                    <div
                        className={clsx('add', {
                            disabled: maxValue === 0 || Number(value) === maxValue || rest?.disabled,
                        })}
                        onClick={handleClickChangeQuantity('ADD')}
                    >
                        ＋
                    </div>
                </div>
            ),
            [handleClickChangeQuantity, maxValue, minValue, onBlur, rest, value]
        );
    }
);

InputQuantity.displayName = 'InputQuantity';

export default memo(InputQuantity);
