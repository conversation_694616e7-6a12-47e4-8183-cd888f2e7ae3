import { CommonCallback } from './common';

/**
 * CommonECUpdateCartModel
 */
export interface CommonECUpdateCartModel extends CommonCallback {
    // 商品ID
    ItemID: string;
    // 数量
    Quantity: number;
    // 買い物かご更新日時
    UpdateDateTime?: string;
    // 数量更新区分
    QuantityUpdateType: number;
    // IsItemUpdate
    IsItemUpdate?: boolean;
}

/**
 * CommonECUpdateCartResult
 */
export interface CommonECUpdateCartResult {
    // 数量（追加後の数量）
    Quantity: number;
    // 更新日時
    UpdateDateTime: string;
    // 商品件数
    ItemCount: number;
    // 商品追加済みフラグ
    IsAdded: boolean;
}
