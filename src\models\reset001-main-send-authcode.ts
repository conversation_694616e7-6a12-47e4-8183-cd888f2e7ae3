import { CommonCallback } from './common';

/**
 * reset001-main-send-authcode
 */
export interface MainSendAuthCode001Model extends CommonCallback {
    // ブランドID
    BrandID: string;
    // ユーザーID
    UserID: string;
}

/**
 * reset002-main-send-authcode
 */
export interface MainCheckAuthCode002Model extends CommonCallback {
    // ブランドID
    BrandID: string;
    // ユーザーID
    UserID: string;
    // 認証コード
    AuthCode: string;
}

/**
 * reset002-main-check-authcode
 */
export interface MainSendAuthCode002Model extends CommonCallback {
    // ブランドID
    BrandID: string;
    // ユーザーID
    UserID: string;
}

/**
 * reset005-main-reset-Password
 */
export interface MainResetPassword005Model extends CommonCallback {
    // ブランドID
    BrandID: string;
    // ユーザーID
    UserID: string;
    // ユーザーID
    Password: string;
}

/**
 * MainSendAuthCode001Result
 */
export interface MainSendAuthCode001Result {
    // 認証コード送信方法設定フラグ 1：SMS送信、2：メール送信
    AuthCodeSendMethod: number;
    // TelephoneNumberOrMailAddress
    TelephoneNumberOrMailAddress: string;
}
