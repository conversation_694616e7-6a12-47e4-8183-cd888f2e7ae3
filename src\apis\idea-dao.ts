import API from '../constants/api';
import { BaseResponse } from '../models/common';
import {
    IdeaDao001DaoGetContributionModel,
    IdeaDao001DaoGetContributionResult,
} from '../models/ideadao001-dao-get-contribution';
import {
    IdeaDao001InitGetIdeaDaoListModel,
    IdeaDao001InitGetIdeaDaoListResult,
} from '../models/ideadao001-init-get-ideadao-list';
import { IdeaDao003MainCreateDaoModel, IdeaDao003MainCreateDaoResult } from '../models/ideadao003-main-create-dao';
import { IdeaDao005MainUpdateIdeaDaoModel } from '../models/ideadao005-main-update-ideadao';
import {
    IdeaDao007InitGetMembersListModel,
    IdeaDao007InitGetMembersListResult,
} from '../models/ideadao007-init-get-members-list';
import { IdeaDao008DaoGetProfileModel, IdeaDao008DaoGetProfileResult } from '../models/ideadao008-dao-get-profile';
import { IdeaDao008DaoUpdateProfileModel } from '../models/ideadao008-dao-update-profile';
import {
    IdeaDao009InitGetTaskListModel,
    IdeaDao009InitGetTaskListResult,
} from '../models/ideadao009-init-get-task-list';
import { IdeaDao010MainCreateTaskModel, IdeaDao010MainCreateTaskResult } from '../models/ideadao010-main-create-task';
import {
    IdeaDao011InitGetTaskInfoDetailModel,
    IdeaDao011InitGetTaskInfoDetailResult,
} from '../models/ideadao011-init-get-taskinfodetail';
import { IdeaDao011MainAddTaskAttachmentsModel } from '../models/ideadao011-main-add-taskattachments';
import { IdeaDao011MainDeleteTaskAttachmentsModel } from '../models/ideadao011-main-delete-taskattachments';
import { IdeaDao011MainUpdateTaskApprovedModel } from '../models/ideadao011-main-update-taskapproved';
import { IdeaDao011MainUpdateTaskCompletedModel } from '../models/ideadao011-main-update-taskcompleted';
import { IdeaDao011MainUpdateTaskInProGressModel } from '../models/ideadao011-main-update-taskinprogress';
import { IdeaDao011MainUpdateTaskNotStartedModel } from '../models/ideadao011-main-update-tasknotstarted';
import createAPI from './baseApi';

/**
 * IdeaDaoAPI
 */
class IdeaDaoAPI {
    /**
     * ideaDao010MainCreateTask
     * @param data IdeaDao010MainCreateTaskModel
     * @returns Promise<BaseResponse<IdeaDao010MainCreateTaskResult>>
     */
    static ideaDao010MainCreateTask = (
        data: IdeaDao010MainCreateTaskModel
    ): Promise<BaseResponse<IdeaDao010MainCreateTaskResult>> => {
        return createAPI<IdeaDao010MainCreateTaskResult>({
            url: API.IDEADAO010_MAIN_CREATE_TASK,
            data,
        });
    };

    /**
     * ideaDao001InitGetIdeaDaoList
     * @param data IdeaDao001InitGetIdeaDaoListModel
     * @returns Promise<IdeaDao001InitGetIdeaDaoListResult>
     */
    static ideaDao001InitGetIdeaDaoList = (
        data: IdeaDao001InitGetIdeaDaoListModel
    ): Promise<BaseResponse<IdeaDao001InitGetIdeaDaoListResult>> => {
        return createAPI<IdeaDao001InitGetIdeaDaoListResult>({
            url: API.IDEADAO001_INIT_GET_IDEADAO_LIST,
            data,
        });
    };

    /**
     * ideaDao001DaoGetContribution
     * @param data IdeaDao001DaoGetContributionModel
     * @returns Promise<IdeaDao001DaoGetContributionResult>
     */
    static ideaDao001DaoGetContribution = (
        data: IdeaDao001DaoGetContributionModel
    ): Promise<BaseResponse<IdeaDao001DaoGetContributionResult>> => {
        return createAPI<IdeaDao001DaoGetContributionResult>({
            url: API.IDEADAO001_DAO_GET_CONTRIBUTION,
            data,
        });
    };

    /**
     * ideaDao003MainCreateDao
     * @param data IdeaDao003MainCreateDaoModel
     * @returns Promise<IdeaDao003MainCreateDaoResult>
     */
    static ideaDao003MainCreateDao = (
        data: IdeaDao003MainCreateDaoModel
    ): Promise<BaseResponse<IdeaDao003MainCreateDaoResult>> => {
        return createAPI<IdeaDao003MainCreateDaoResult>({
            url: API.IDEADAO003_MAIN_CREATE_DAO,
            data,
        });
    };

    /**
     * ideaDao005MainUpdateIdeaDao
     * @param data IdeaDao005MainUpdateIdeaDaoModel
     * @returns Promise<BaseResponse>
     */
    static ideaDao005MainUpdateIdeaDao = (data: IdeaDao005MainUpdateIdeaDaoModel): Promise<BaseResponse> => {
        return createAPI({
            url: API.IDEADAO005_MAIN_UPDATE_IDEADAO,
            data,
        });
    };

    /**
     * ideaDao009InitGetTaskList
     * @param data IdeaDao009InitGetTaskListModel
     * @returns Promise<BaseResponse<IdeaDao009InitGetTaskListResult>>
     */
    static ideaDao009InitGetTaskList = (
        data: IdeaDao009InitGetTaskListModel
    ): Promise<BaseResponse<IdeaDao009InitGetTaskListResult>> => {
        return createAPI<IdeaDao009InitGetTaskListResult>({
            url: API.IDEADAO009_INIT_GET_TASK_LIST,
            data,
        });
    };

    /**
     * ideaDao007InitGetMembersList
     * @param data IdeaDao007InitGetMembersListModel
     * @returns Promise<BaseResponse<IdeaDao007InitGetMembersListResult>>
     */
    static ideaDao007InitGetMembersList = (
        data: IdeaDao007InitGetMembersListModel
    ): Promise<BaseResponse<IdeaDao007InitGetMembersListResult>> => {
        return createAPI<IdeaDao007InitGetMembersListResult>({
            url: API.IDEADAO007_INIT_GET_MEMBERS_LIST,
            data,
        });
    };

    /**
     * ideaDao008DaoGetProfile
     * @param data IdeaDao008DaoGetProfileModel
     * @returns Promise<BaseResponse<IdeaDao008DaoGetProfileResult>>
     */
    static ideaDao008DaoGetProfile = (
        data: IdeaDao008DaoGetProfileModel
    ): Promise<BaseResponse<IdeaDao008DaoGetProfileResult>> => {
        return createAPI<IdeaDao008DaoGetProfileResult>({
            url: API.IDEADAO008_DAO_GET_PROFILE,
            data,
        });
    };

    /**
     * ideaDao008DaoUpdateProfile
     * @param data IdeaDao008DaoUpdateProfileModel
     * @returns Promise<BaseResponse>
     */
    static ideaDao008DaoUpdateProfile = (data: IdeaDao008DaoUpdateProfileModel): Promise<BaseResponse> => {
        return createAPI({
            url: API.IDEADAO008_DAO_UPDATE_PROFILE,
            data,
        });
    };

    /**
     * ideaDao011InitGetTaskInfo
     * @param data IdeaDao011InitGetInfoModel
     * @returns Promise<BaseResponse<IdeaDao011InitGetInfoResult>>
     */
    static ideaDao011InitGetTaskInfo = (
        data: IdeaDao011InitGetTaskInfoDetailModel
    ): Promise<BaseResponse<IdeaDao011InitGetTaskInfoDetailResult>> => {
        return createAPI<IdeaDao011InitGetTaskInfoDetailResult>({
            url: API.IDEADAO011_INIT_GET_TASKINFODETAIL,
            data,
        });
    };

    /**
     * ideaDao011MainUpdateTaskNotStarted
     * @param data IdeaDao011MainUpdateTaskNotStartedModel
     * @returns Promise<BaseResponse>
     */
    static ideaDao011MainUpdateTaskNotStarted = (
        data: IdeaDao011MainUpdateTaskNotStartedModel
    ): Promise<BaseResponse> => {
        return createAPI({
            url: API.IDEADAO011_MAIN_UPDATE_TASKNOTSTARTED,
            data,
        });
    };

    /**
     * ideaDao011MainUpdateTaskInProGress
     * @param data IdeaDao011MainUpdateTaskInProGressModel
     * @returns Promise<BaseResponse>
     */
    static ideaDao011MainUpdateTaskInProGress = (
        data: IdeaDao011MainUpdateTaskInProGressModel
    ): Promise<BaseResponse> => {
        return createAPI({
            url: API.IDEADAO011_MAIN_UPDATE_TASKINPROGRESS,
            data,
        });
    };

    /**
     * ideaDao011MainUpdateTaskCompleted
     * @param data IdeaDao011MainUpdateTaskCompletedModel
     * @returns Promise<BaseResponse>
     */
    static ideaDao011MainUpdateTaskCompleted = (
        data: IdeaDao011MainUpdateTaskCompletedModel
    ): Promise<BaseResponse> => {
        return createAPI({
            url: API.IDEADAO011_MAIN_UPDATE_TASKCOMPLETED,
            data,
        });
    };

    /**
     * ideaDao011MainUpdateTaskApproved
     * @param data IdeaDao011MainUpdateTaskApprovedModel
     * @returns Promise<BaseResponse>
     */
    static ideaDao011MainUpdateTaskApproved = (data: IdeaDao011MainUpdateTaskApprovedModel): Promise<BaseResponse> => {
        return createAPI({
            url: API.IDEADAO011_MAIN_UPDATE_TASKAPPROVED,
            data,
        });
    };

    /**
     * ideaDao011MainAddTaskAttachments
     * @param data IdeaDao011MainAddTaskAttachmentsModel
     * @returns Promise<BaseResponse>
     */
    static ideaDao011MainAddTaskAttachments = (data: IdeaDao011MainAddTaskAttachmentsModel): Promise<BaseResponse> => {
        return createAPI({
            url: API.IDEADAO011_MAIN_ADD_TASKATTACHMENTS,
            data,
        });
    };

    /**
     * ideaDao011MainDeleteTaskAttachments
     * @param data IdeaDao011MainDeleteTaskAttachmentsModel
     * @returns Promise<BaseResponse>
     */
    static ideaDao011MainDeleteTaskAttachments = (
        data: IdeaDao011MainDeleteTaskAttachmentsModel
    ): Promise<BaseResponse> => {
        return createAPI({
            url: API.IDEADAO011_MAIN_DELETE_TASKATTACHMENTS,
            data,
        });
    };
}

export default IdeaDaoAPI;
