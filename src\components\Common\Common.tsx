import { Typography } from '@mui/material';
import { useMemo } from 'react';
import Icons from '../Icons/Icons';
import './styles.scss';

/**
 * RequiredLabelProps
 */
export interface RequiredLabelProps {
    /**
     * input's label
     */
    label?: string | React.JSX.Element;
    /**
     * make input is require or not
     */
    required?: boolean;
    /**
     * optional
     */
    optional?: boolean;
}

/**
 * RequiredLabel
 * @param props RequiredLabelProps
 * @returns React.JSX.Element
 */
export const RequiredLabel = (props: RequiredLabelProps): React.JSX.Element => {
    const { label, required, optional } = props;
    return useMemo(
        () => (
            <div className="d-flex flex-row align-items-center common-component common-label-container">
                <div className="label">
                    {label}
                    {Boolean(required) && (
                        <div className="required d-inline-block">
                            <div className="d-flex justify-content-center align-items-center required-container">
                                <Typography className="label">必須</Typography>
                            </div>
                        </div>
                    )}
                    {(Boolean(optional) || Bo<PERSON>an(!required)) && (
                        <div className="optional d-inline-block">
                            <div className="d-flex justify-content-center align-items-center optional-label-container">
                                <Typography className="label">任意</Typography>
                            </div>
                        </div>
                    )}
                </div>
            </div>
        ),
        [label, optional, required]
    );
};

/**
 * CommonErrorProps
 */
export interface CommonErrorProps {
    /**
     * error
     */
    error?: React.ReactNode;
    /**
     * component id.
     */
    componentId?: string;
    /**
     * errorIconSize
     */
    iconSize?: {
        w: number;
        h: number;
    };
    /**
     * errorStyle
     */
    errorStyle?: React.CSSProperties;
}

/**
 * CommonError
 * @param props CommonErrorProps
 * @returns React.JSX.Element
 */
export const CommonError = (props: CommonErrorProps): React.JSX.Element => {
    const {
        error,
        errorStyle,
        iconSize = {
            w: 16,
            h: 16,
        },
        componentId = 'common',
    } = useMemo(() => props, [props]);

    return useMemo(
        () => (
            <div className="common-component">
                {error && (
                    <div className="d-flex flex-row common-error-container">
                        <div
                            className="d-flex align-items-center justify-content-center"
                            style={{
                                width: iconSize.w,
                                height: 22,
                            }}
                        >
                            <Icons.UnreadNotificationIcon width={iconSize.w} height={iconSize.h} />
                        </div>
                        <div id={`${componentId}-error`} className="error" style={errorStyle}>
                            {error}
                        </div>
                    </div>
                )}
            </div>
        ),
        [componentId, error, errorStyle, iconSize.h, iconSize.w]
    );
};
