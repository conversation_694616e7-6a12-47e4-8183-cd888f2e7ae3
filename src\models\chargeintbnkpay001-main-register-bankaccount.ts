import { CommonCallback } from './common';

/**
 * ChargeIntBnkPay001MainRegisterBankAccountModel
 */
export interface ChargeIntBnkPay001MainRegisterBankAccountModel extends CommonCallback {
    // 銀行コード
    BankCode: string;
}

/**
 * ChargeIntBnkPay001MainRegisterBankAccountResult
 */
export interface ChargeIntBnkPay001MainRegisterBankAccountResult {
    mac: string;
    accountLabel: string;
    bankpayAccountsRegisterUrl: string;
    customerAccessToken: string;
    processorAuthenticationKeyIndex: string;
    backUrl: string;
}
