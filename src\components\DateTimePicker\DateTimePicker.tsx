import { TextFieldProps } from '@mui/material';
import { DesktopDateTimePicker, LocalizationProvider, renderTimeViewClock } from '@mui/x-date-pickers';
import { AdapterMoment } from '@mui/x-date-pickers/AdapterMoment';
import { BaseDateTimePickerProps } from '@mui/x-date-pickers/DateTimePicker/shared';
import { BaseTimeValidationProps } from '@mui/x-date-pickers/internals';
import clsx from 'clsx';
import { Moment } from 'moment';
import { memo, useMemo } from 'react';
import PickerIcon from '../../assets/images/icon/date-picker-icon.png';
import './styles.scss';

/**
 * DateTimePickerProps
 */
// eslint-disable-next-line @typescript-eslint/no-explicit-any
export interface DateTimePickerProps extends BaseTimeValidationProps, BaseDateTimePickerProps<Moment, any> {
    /**
     * inputFormat
     * @default 'YYYY/MM/DD HH:mm'
     */
    inputFormat?: string;
    /**
     * inputProps
     */
    inputProps?: Omit<TextFieldProps, 'disabled'>;
    /**
     * readOnly
     * @default false
     */
    readOnly?: boolean;
    /**
     * disabled
     * @default false
     */
    disabled?: boolean;
    /**
     * error
     */
    error?: boolean | string;
    /**
     * iconCalendar
     */
    iconCalendar?: () => JSX.Element;
    /**
     * is24Hours
     */
    is24Hours?: boolean;
}

/**
 * DateTimePickerIcon
 * @returns JSX.Element
 */
export const DateTimePickerIcon = (): JSX.Element => {
    return <img src={PickerIcon} width={17} height={18} />;
};

/**
 * DateTimePicker
 */
const DateTimePicker = (props: DateTimePickerProps): JSX.Element => {
    const {
        inputFormat = 'YYYY/MM/DD HH:mm',
        inputProps,
        readOnly,
        disabled,
        error,
        is24Hours = true,
        iconCalendar,
        ...rest
    } = useMemo(() => props, [props]);

    return useMemo(
        () => (
            <LocalizationProvider
                dateAdapter={AdapterMoment}
                adapterLocale="ja-JP"
                dateFormats={{ year: 'YYYY年', month: 'MM月', monthShort: 'M月', monthAndYear: 'M月 YYYY' }}
            >
                <DesktopDateTimePicker
                    {...rest}
                    disabled={disabled}
                    className={clsx('common-date-time-picker-component', {
                        error,
                    })}
                    ampm={!is24Hours}
                    ampmInClock={!is24Hours}
                    format={inputFormat}
                    viewRenderers={{
                        hours: renderTimeViewClock,
                        minutes: renderTimeViewClock,
                        seconds: renderTimeViewClock,
                    }}
                    slots={{
                        openPickerIcon: iconCalendar ? iconCalendar : DateTimePickerIcon,
                    }}
                    slotProps={{
                        textField: {
                            ...inputProps,
                            disabled: readOnly || disabled,
                            className: clsx({
                                'read-only': readOnly,
                            }),
                        },
                    }}
                />
                {error && (
                    <div className="d-flex align-items-center common-date-time-picker-error-container">
                        <div className="error">{error}</div>
                    </div>
                )}
            </LocalizationProvider>
        ),
        [disabled, error, iconCalendar, inputFormat, inputProps, is24Hours, readOnly, rest]
    );
};

export default memo(DateTimePicker);
