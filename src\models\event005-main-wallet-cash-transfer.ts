import { CommonCallback } from './common';

/**
 * ItemMedalServiceDetail
 */
export interface ItemMedalServiceDetail {
    MedalServiceID: string;
    PaymentAmount: number;
}

/**
 * ItemEventDetail
 */
export interface ItemEventDetail {
    EventTicketDetailID: string;
    EventTicketBuyNumber: number;
}

/**
 * ChoiceItem
 */
export interface ChoiceItem {
    // 選択回答ID
    ChoiceID?: string;
    AnswerRank?: string;
}

/**
 * AnswerItem
 */
export interface AnswerItem {
    // 質問ID
    QuestionID?: string;
    // 自由記述回答内容
    FreeInputText?: string;
    ChoiceList?: ChoiceItem[];
}

/**
 * Event005MainWalletCashTransferModel
 */
export interface Event005MainWalletCashTransferModel extends CommonCallback {
    StoreID: string;
    TotalPaymentAmount: number;
    Items: ItemMedalServiceDetail[];
    EventTicketID: string;
    EventTicketDetailList: ItemEventDetail[];
    // フォームID
    FormID?: string;
    AnswerList?: AnswerItem[];
}
