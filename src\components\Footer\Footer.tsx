import { Paper, Typography } from '@mui/material';
import clsx from 'clsx';
import _ from 'lodash';
import { ReactElement, cloneElement, memo, useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { isBrowser, isTablet } from 'react-device-detect';
import { useLocation, useNavigate, useOutletContext } from 'react-router-dom';
import { ContextType } from '../../App';
import CommonAPI from '../../apis/common';
import { Actions } from '../../constants/actions';
import API from '../../constants/api';
import Screens from '../../constants/screens';
import useUser from '../../hooks/user';
import useWallet from '../../hooks/wallet';
import { BaseResponse } from '../../models/common';
import { Wallet001InitGetCoinItem, Wallet001InitGetCoinModel } from '../../models/wallet001-init-get-coin';
import AuthUtils from '../../utils/auth';
import Utils from '../../utils/utils';
import CommonScannerModal from '../CommonScannerModal/CommonScannerModal';
import Icons from '../Icons/Icons';
import './styles.scss';
import CommonQrCodeRead001 from '../../pages/CommonQrCodeRead/CommonQrCodeRead001';
import CommonUtils from '../../utils/common';
import { CommonDialogRef } from '../CommonDialog/CommonDialog';

/**
 * FooterIconProps
 */
export interface FooterIconProps {
    /**
     * icon's name
     */
    name?: string;
    /**
     * focused
     */
    focused?: boolean;
    /**
     * unreadIcon
     */
    unreadIcon?: React.ReactNode;
}

/**
 * FooterItemProps
 */
export interface FooterItemProps {
    /**
     * name
     * footer item name
     */
    name?: string;
    /**
     * router
     * footer item router
     */
    router: string;
    /**
     * matches
     */
    matches: string[];
    /**
     * icon
     * footer item icon
     */
    icon?: ReactElement<FooterIconProps>;
    /**
     * label
     */
    label: string;
    /**
     * onClick
     * custom footer item click
     * @returns void
     */
    onClick?: () => void;
    /**
     * action key
     */
    actionKey?: string;
}

/**
 * FooterItem
 * @param props FooterItemProps
 * @returns React.JSX.Element
 */
export const FooterItem = (props: FooterItemProps): React.JSX.Element => {
    const location = useLocation();
    const navigate = useNavigate();
    const { name, label, icon, matches, router } = useMemo(() => props, [props]);
    // footer item focus
    const focused = useMemo(() => matches.includes(location.pathname), [location.pathname, matches]);

    /**
     * onItemClickHandler
     */
    const onItemClickHandler = useCallback(() => {
        const pathname = window.location.pathname;
        if (router) {
            if (pathname !== router) {
                CommonAPI.commonUserTransitionHistory({
                    OriginScreenName: pathname,
                    DestinationScreenName: router,
                    ElementName: label?.replaceAll('\n', ''),
                    DeviceID: isBrowser ? 'PC' : isTablet ? 'Tablet' : 'mobile',
                });
            }
            navigate(router);
        }
    }, [label, navigate, router]);

    return useMemo(
        () => (
            <div
                className={clsx('d-flex w-100 justify-content-center flex-column align-items-center footer-item', {
                    selected: focused,
                })}
                onClick={onItemClickHandler}
            >
                {icon && (
                    <div className="d-flex justify-content-center align-items-center icon position-relative">
                        {cloneElement(icon, {
                            name,
                            focused,
                        })}
                    </div>
                )}
                <Typography
                    className={clsx('label', {
                        selected: focused,
                    })}
                >
                    {label}
                </Typography>
            </div>
        ),
        [focused, onItemClickHandler, icon, name, label]
    );
};

/**
 * FooterIcon
 * @param props FooterIconProps
 * @returns React.JSX.Element
 */
const FooterIcon = (props: FooterIconProps): React.JSX.Element => {
    const { name, focused } = useMemo(() => props, [props]);

    return useMemo(() => {
        switch (name) {
            case 'wallet':
                return <Icons.WalletIcon color={focused ? 'var(--app-base-color)' : '#707070'} />;
            case 'search':
                return <Icons.SearchIcon color={focused ? 'var(--app-base-color)' : '#707070'} />;
            case 'collection':
                return <Icons.FooterCollection color={focused ? 'var(--app-base-color)' : '#707070'} />;
            case 'community':
                return <Icons.CommunityPurpose color={focused ? 'var(--app-base-color)' : '#707070'} />;
            default:
                return <div />;
        }
    }, [focused, name]);
};

/**
 * Footer
 * ID: COMMONFOOTER001
 * @returns React.JSX.Element
 */
const Footer = (): React.JSX.Element => {
    const navigate = useNavigate();
    const { userInfo } = useUser();
    const dialogRef = useRef<CommonDialogRef>(null);
    // translatePullDown
    const { translatePullDown } = useOutletContext<ContextType>() || {};

    const [walletCoinItem, setWalletCoinItem] = useState<Wallet001InitGetCoinItem[]>();
    const userLogin001Flag = useMemo(() => {
        return userInfo?.haveLogin001MainGetLoginEnableFlag || undefined;
    }, [userInfo?.haveLogin001MainGetLoginEnableFlag]);
    const { postWallet001InitGetCoin } = useWallet();
    /**
     * handleGetCoin
     */
    const handleGetCoin = useCallback(() => {
        const payloadWallet001InitGetCoin: Wallet001InitGetCoinModel = {
            useLock: true,
            successCallback: (response: BaseResponse<Wallet001InitGetCoinItem[]>) => {
                const { status, result, Message } = response;
                if (status === API.STATUS_CODE.SUCCESS && result) {
                    setWalletCoinItem(result);
                } else {
                    CommonUtils.showMessage({
                        message: Utils.t(Message || 'api.common.unknown_error'),
                        type: 'ERROR',
                    });
                }
            },
        };
        postWallet001InitGetCoin(payloadWallet001InitGetCoin);
    }, [postWallet001InitGetCoin]);

    useEffect(() => {
        const isLogin = AuthUtils.retrieveToken('idToken');
        if (isLogin) {
            handleGetCoin();
        }
    }, [handleGetCoin, userLogin001Flag]);

    /**
     * handleClickPaymentButton
     * handle click on [支払う] button
     */
    const handleClickPaymentButton = useCallback(async () => {
        const isLogin = AuthUtils.retrieveToken('idToken');
        if (isLogin) {
            dialogRef.current?.toggleDialog(true);
        } else {
            CommonAPI.commonUserTransitionHistory({
                OriginScreenName: window.location.pathname,
                DestinationScreenName: Screens.LOGIN,
                ElementName: Utils.t('footer.items.payment')?.replaceAll('\n', ''),
                DeviceID: isBrowser ? 'PC' : isTablet ? 'Tablet' : 'mobile',
            });
            navigate(Screens.LOGIN);
        }
    }, [navigate]);

    /**
     * handleCloseModal
     */
    const handleCloseModal = useCallback(() => {
        dialogRef.current?.toggleDialog(false);
    }, []);

    return useMemo(
        () => (
            <Paper
                sx={{
                    position: 'fixed',
                    bottom: 0,
                    left: 0,
                    right: 0,
                    boxShadow: 'none',
                    background: 'transparent',
                    zIndex: 999,
                    marginLeft: 'calc(100vw - 100%)',
                }}
            >
                <div id="AppFooter" className="container">
                    <div className="d-flex flex-row w-100 justify-content-between footer-container">
                        {/* ウォレット */}
                        <FooterItem
                            name={'wallet'}
                            label={Utils.t('footer.items.wallet')}
                            router={Screens.WALLET001}
                            matches={[Screens.HOME, Screens.WALLET001]}
                            icon={<FooterIcon />}
                            actionKey={Actions.WALLET001}
                        />
                        <div className="item-separator" />

                        {/* さがす */}
                        <FooterItem
                            name={'search'}
                            label={Utils.t('footer.items.search')}
                            router={Screens.STORE_LIST}
                            matches={[Screens.STORE_LIST]}
                            icon={<FooterIcon />}
                            actionKey={Actions.STORE002}
                        />

                        {/* 支払う */}
                        <div
                            className={clsx(
                                'd-flex justify-content-center align-items-center footer-item-center position-relative'
                            )}
                        >
                            <div className="footer-item-center-overlay" />
                            <div
                                className="d-flex flex-column footer-item-center-container"
                                onClick={handleClickPaymentButton}
                            >
                                <Icons.QRNewIcon />
                                <Typography className="label fs-12">{Utils.t('footer.items.qrcode_read')}</Typography>
                            </div>
                        </div>

                        {/* コレクション */}
                        <FooterItem
                            name={'collection'}
                            label={Utils.t('footer.items.collection')}
                            router={Screens.NFT_COLLECTION001}
                            matches={[Screens.NFT_COLLECTION]}
                            icon={<FooterIcon />}
                            actionKey={Actions.NFT_COLLECTION001}
                        />
                        <div className="item-separator" />

                        {/* コミュニティを押下した際、DAOTOP001 DAOトップ */}
                        <FooterItem
                            name={'community'}
                            label={Utils.t('footer.items.community')}
                            router={Screens.DAO_TOP001}
                            matches={[Screens.HOME, Screens.DAO_TOP001]}
                            icon={<FooterIcon />}
                            actionKey={Actions.MENU001}
                        />
                    </div>
                    <CommonScannerModal ref={dialogRef}>
                        <CommonQrCodeRead001 onClose={handleCloseModal} walletCoinItem={walletCoinItem} />
                    </CommonScannerModal>
                </div>
            </Paper>
        ),
        // translatePullDown change => rerender
        // eslint-disable-next-line react-hooks/exhaustive-deps
        [handleClickPaymentButton, handleCloseModal, userInfo?.haveUnreadNews, translatePullDown, walletCoinItem]
    );
};

export default memo(Footer);

/**
 * useCommonFooter
 * @param useFooter boolean
 */
export const useCommonFooter = (
    useFooter?: boolean
): {
    height: number | undefined;
} => {
    const [height, setHeight] = useState<number | undefined>();

    useEffect(() => {
        const target = document.querySelector('#AppFooter');
        if (target && useFooter) {
            Utils.createAppObserver((entries) => {
                const { contentRect } = _.last(entries) as ResizeObserverEntry;
                if (contentRect) {
                    setHeight(contentRect.height + 24);
                }
            }).observe(target);
        }
    }, [useFooter]);

    return useMemo(
        () => ({
            height,
        }),
        [height]
    );
};
