import API from '../constants/api';
import Environments from '../constants/environments';
import { BaseResponse } from '../models/common';
import {
    Nft001InitGetNftCategoryListItem,
    Nft001InitGetNftCategoryListModel,
} from '../models/nft001-init-get-nft-category-list';
import {
    Nft002InitGetNftCategoryDetailModel,
    Nft002InitGetNftCategoryDetailResult,
} from '../models/nft002-init-get-nft-category-detail';
import {
    Nft002InitGetNftPurchaseListModel,
    Nft002InitGetNftPurchaseListResult,
} from '../models/nft002-init-get-nft-purchase-list';
import { Nft002MainSendNftUserLikeModel } from '../models/nft002-main-send-nft-user-like';
import { Nft003InitGetNftDetailModel, Nft003InitGetNftDetailResult } from '../models/nft003-init-get-nft-detail';
import { Nft003MainSendNftUserLikeModel } from '../models/nft003-main-send-nft-user-like';
import { Nft005InitCheckNftQrModel } from '../models/nft005-init-check-nft-qr';
import { Nft006InitCheckNftQrModel } from '../models/nft006-init-check-nft-qr';
import { Nft007InitGetNftModel, Nft007InitGetNftResult } from '../models/nft007-init-get-nft';
import { Nft008InitGetCoinModel, Nft008InitGetCoinResult } from '../models/nft008-init-get-coin';
import { Nft008InitGetNftModel, Nft008InitGetNftResult } from '../models/nft008-init-get-nft';
import { Nft008MainCheckNftSerialModel } from '../models/nft008-main-check-nft-serial';
import { Nft009MainExecutePaymentModel, Nft009MainExecutePaymentResult } from '../models/nft009-main-execute-payment';
import {
    NftCollection001InitGetNftUserListModel,
    NftCollection001InitGetNftUserListResult,
} from '../models/nftcollection001-init-get-nft-user-list';
import {
    NftCollection002InitGetNftUserModel,
    NftCollection002InitGetNftUserResult,
} from '../models/nftcollection002-init-get-nft-user';
import Utils from '../utils/utils';

import createAPI from './baseApi';

/**
 * NftAPI
 */
class NftAPI {
    /**
     * nft001InitGetNftCategoryList
     * @param data Nft001InitGetNftCategoryListModel
     * @returns Promise<BaseResponse<Nft001InitGetNftCategoryListItem[]>>
     */
    static nft001InitGetNftCategoryList = (
        data: Nft001InitGetNftCategoryListModel
    ): Promise<BaseResponse<Nft001InitGetNftCategoryListItem[]>> => {
        return createAPI<Nft001InitGetNftCategoryListItem[]>({
            url: API.NFT001_INIT_GET_NFT_CATEGORY_LIST,
            data: {
                // set default brandID
                BrandID: Environments.brandID,
                ...data,
            },
        });
    };

    /**
     * nft002InitGetNftCategoryDetail
     * @param data Nft002InitGetNftCategoryDetailModel
     * @returns Promise<BaseResponse<Nft002InitGetNftCategoryDetailResult>>
     */
    static nft002InitGetNftCategoryDetail = (
        data: Nft002InitGetNftCategoryDetailModel
    ): Promise<BaseResponse<Nft002InitGetNftCategoryDetailResult>> => {
        return createAPI<Nft002InitGetNftCategoryDetailResult>({
            url: API.NFT002_INIT_GET_NFT_CATEGORY_DETAIL,
            data: {
                // set default brandID
                BrandID: Environments.brandID,
                ...data,
            },
        });
    };

    /**
     * nft002MainSendNftUserLike
     * @param data Nft002MainSendNftUserLikeModel
     * @returns Promise<BaseResponse>
     */
    static nft002MainSendNftUserLike = (data: Nft002MainSendNftUserLikeModel): Promise<BaseResponse> => {
        return createAPI({
            url: API.NFT002_MAIN_SEND_NFT_USER_LIKE,
            data,
        });
    };

    /**
     * nft001InitGetNftCategoryList
     * @param data Nft002InitGetNftPurchaseListModel
     * @returns Promise<BaseResponse<Nft002InitGetNftPurchaseListResult>>
     */
    static nft002InitGetNftPurchaseList = (
        data: Nft002InitGetNftPurchaseListModel
    ): Promise<BaseResponse<Nft002InitGetNftPurchaseListResult>> => {
        return createAPI<Nft002InitGetNftPurchaseListResult>({
            url: API.NFT002_INIT_GET_NFT_PURCHASE_LIST,
            data: {
                // set default brandID
                BrandID: Environments.brandID,
                CheerID: Utils.getCheerID() || '',
                ...data,
            },
        });
    };

    /**
     * nft003InitGetNftDetail
     * @param data Nft003InitGetNftDetailModel
     * @returns Promise<BaseResponse<Nft003InitGetNftDetailResult>>
     */
    static nft003InitGetNftDetail = (
        data: Nft003InitGetNftDetailModel
    ): Promise<BaseResponse<Nft003InitGetNftDetailResult>> => {
        return createAPI<Nft003InitGetNftDetailResult>({
            url: API.NFT003_INIT_GET_NFT_DETAIL,
            data: {
                // set default brandID
                BrandID: Environments.brandID,
                CheerID: Utils.getCheerID() || '',
                ...data,
            },
        });
    };

    /**
     * nft008InitGetCoin
     * @param data Nft008InitGetNftModel
     * @returns Promise<BaseResponse<Nft008InitGetCoinResult[]>>
     */
    static nft008InitGetCoin = (data: Nft008InitGetCoinModel): Promise<BaseResponse<Nft008InitGetCoinResult[]>> => {
        return createAPI<Nft008InitGetCoinResult[]>({
            url: API.NFT008_INIT_GET_COIN,
            data,
        });
    };

    /**
     * nft003MainSendNftUserLike
     * @param data Nft003MainSendNftUserLikeModel
     * @returns Promise<BaseResponse>
     */
    static nft003MainSendNftUserLike = (data: Nft003MainSendNftUserLikeModel): Promise<BaseResponse> => {
        return createAPI({
            url: API.NFT003_MAIN_SEND_NFT_USER_LIKE,
            data,
        });
    };

    /**
     * nft008InitGetNft
     * @param data Nft008InitGetNftModel
     * @returns Promise<BaseResponse<Nft008InitGetNftResult>>
     */
    static nft008InitGetNft = (data: Nft008InitGetNftModel): Promise<BaseResponse<Nft008InitGetNftResult>> => {
        return createAPI<Nft008InitGetNftResult>({
            url: API.NFT008_INIT_GET_NFT,
            data,
        });
    };

    /**
     * nft008MainCheckNftSerial
     * @param data Nft008MainCheckNftSerialModel
     * @returns Promise<BaseResponse>
     */
    static nft008MainCheckNftSerial = (data: Nft008MainCheckNftSerialModel): Promise<BaseResponse> => {
        return createAPI({
            url: API.NFT008_MAIN_CHECK_NFT_SERIAL,
            data,
        });
    };

    /**
     * nft005InitCheckNftQr
     * @param data Nft005InitCheckNftQrModel
     * @returns Promise<BaseResponse>
     */
    static nft005InitCheckNftQr = (data: Nft005InitCheckNftQrModel): Promise<BaseResponse> => {
        return createAPI({
            url: API.NFT005_INIT_CHECK_NFT_QR,
            data,
        });
    };

    /**
     * nft009MainExecutePayment
     * @param data Nft009MainExecutePaymentModel
     * @returns Promise<BaseResponse<Nft009MainExecutePaymentResult>>
     */
    static nft009MainExecutePayment = (
        data: Nft009MainExecutePaymentModel
    ): Promise<BaseResponse<Nft009MainExecutePaymentResult>> => {
        return createAPI({
            url: API.NFT009_MAIN_EXECUTE_PAYMENT,
            data,
        });
    };

    /**
     * nft006InitCheckNftQr
     * @param data Nft006InitCheckNftQrModel
     * @returns Promise<BaseResponse>
     */
    static nft006InitCheckNftQr = (data: Nft006InitCheckNftQrModel): Promise<BaseResponse> => {
        return createAPI({
            url: API.NFT006_INIT_CHECK_NFT_QR,
            data,
        });
    };

    /**
     * nftCollection001InitGetNftUserList
     * @param data NftCollection001InitGetNftUserListModel
     * @returns Promise<BaseResponse<NftCollection001InitGetNftUserListResult>>
     */
    static nftCollection001InitGetNftUserList = (
        data: NftCollection001InitGetNftUserListModel
    ): Promise<BaseResponse<NftCollection001InitGetNftUserListResult>> => {
        return createAPI<NftCollection001InitGetNftUserListResult>({
            url: API.NFTCOLLECTION001_INIT_GET_NFT_USER_LIST,
            data,
        });
    };

    /**
     * nftCollection002InitGetNftUser
     * @param data NftCollection002InitGetNftUserModel
     * @returns Promise<BaseResponse<NftCollection002InitGetNftUserResult>>
     */
    static nftCollection002InitGetNftUser = (
        data: NftCollection002InitGetNftUserModel
    ): Promise<BaseResponse<NftCollection002InitGetNftUserResult>> => {
        return createAPI<NftCollection002InitGetNftUserResult>({
            url: API.NFTCOLLECTION002_INIT_GET_NFT_USER,
            data,
        });
    };

    /**
     * nft007InitGetNft
     * @param data Nft007InitGetNftModel
     * @returns Promise<BaseResponse<Nft007InitGetNftResult>>
     */
    static nft007InitGetNft = (data: Nft007InitGetNftModel): Promise<BaseResponse<Nft007InitGetNftResult>> => {
        return createAPI<Nft007InitGetNftResult>({
            url: API.NFT007_INIT_GET_NFT,
            data,
        });
    };
}
export default NftAPI;
