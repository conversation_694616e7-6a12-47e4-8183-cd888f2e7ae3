import { CommonCallback } from './common';

/**
 * DaoIDItem
 */
export interface DaoIDItem {
    DaoID: string;
}

/**
 * IdeaDao001InitGetIdeaDaoListModel
 */
export interface IdeaDao001InitGetIdeaDaoListModel extends CommonCallback {
    DaoIDList?: DaoIDItem[];
}

/**
 * IdeaDaoListItem
 */
export interface RevenueSharingRatioItem {
    // 発案者・応援者
    InvatorAndCheers?: number;
    // リーダー
    Leaders?: number;
    // メンバー・投資家（タスク）
    MembersAndInvestors?: number;
}

/**
 * IdeaDaoLeaderItem
 */
export interface IdeaDaoLeaderItem {
    // ニックネーム
    NickName?: string;
    // プロフィール画像ファイル名
    ProfileImageFileName?: string;
}

/**
 * IdeaDaoListItem
 */
export interface IdeaDaoListItem {
    // DAOID
    DaoID?: string;
    // DAO名
    DaoName?: string;
    // 概要
    OverView?: string;
    // 参加者数
    ParticipantCount?: number;
    // DAO画像ファイル名
    DaoImageFileName?: string;
    // 解決したい課題・叶えたい未来
    OurMission?: string;
    // 求めるスキル
    RequiredSkill?: string;
    // 収益分配率
    RevenueSharingRatio?: RevenueSharingRatioItem;
    // タスクトークン保有量
    TaskTokenAmount?: number;
    // リーダー
    Leaders: IdeaDaoLeaderItem[];
    // 参加状態
    ParticipantStatus: number;
    // 妄想DAOリーダーフラグ 0: リーダーではない  1: リーダー
    LeadersFlag?: number;
    // 発案トークン保有量
    IdeaTokenAmount?: number;
    // 応援トークン保有量
    CheerTokenAmount?: number;
    // リーダートークン保有量
    LeaderTokenAmount?: number;
    // 貢献度
    ContributionDegree?: number;
}

/**
 * IdeaDao001InitGetIdeaDaoListResult
 */
export interface IdeaDao001InitGetIdeaDaoListResult {
    // IdeaDaoリスト
    IdeaDaoList: IdeaDaoListItem[];
}
