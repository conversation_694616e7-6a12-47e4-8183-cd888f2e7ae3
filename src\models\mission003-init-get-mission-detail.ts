import { CommonCallback } from './common';

/**
 * Mission003InitGetMissionDetailModel
 */
export interface Mission003InitGetMissionDetailModel extends CommonCallback {
    // スタンプラリーID
    MissionID: string;
    // ミッションID
    MissionDetailID: string;
}

/**
 * Mission003InitGetMissionDetailResult
 */
export interface Mission003InitGetMissionDetailResult {
    // 終了日
    EndDateTime: string;
    // ミッション詳細タイプ
    MissionDetailType: string;
    // ミッション画像
    MissionDetailImage: string;
    // ミッション名称
    MissionDetailName: string;
    // ミッション説明文
    MissionDetailDescription: string;
    // 問い合わせ先
    InquireTo?: string;
    // 住所
    Address: string;
    // 注意事項
    MissionDetailNotes: string;
    // ミッション区分
    MissionDetailCategory: string;
    // 条件達成区分
    // LOGIN：ログイン/ BALANCE：チャージ/ PAYMENT：支払い/ EVENT：イベント/ QRCODE：QRコードを読み込む/ QRCODE_OCATION：現地でGPSかQRコードを読み込む
    ConditionType: string[];
    // 条件達成回数
    ConditionActionCount?: number;
    // 条件達成値
    ConditionValue?: number;
    // 達成報酬種別
    IncentiveType?: number;
    // 達成報酬ID
    IncentiveID?: string;
    // 達成報酬画像
    IncentiveImage?: string;
    // 達成報酬説明文
    IncentiveDescription?: string;
    // 読み込み管理フラグ
    ScanStatus?: boolean;
    // クリアミッション数
    ClearMissionDetailCount?: number;
    // クリアミッション条件値
    ClearMissionDetailValue?: number;
    // 達成済みフラグ
    CompleteStatus?: boolean;
    // 達成日
    CompleteDateTime?: string;
    // 報酬取得ID
    GetRewardID?: string;
    // 報酬取得日
    GetRewardDateTime?: string;
}
