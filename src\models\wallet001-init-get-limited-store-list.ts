import { CommonCallback } from './common';

/**
 * Wallet001InitGetLimitedStoreListModel
 */
export interface Wallet001InitGetLimitedStoreListModel extends CommonCallback {
    // 1ページあたりの取得アイテム数
    PageSize: number;
}

/**
 * Wallet001InitGetLimitedStoreListItem
 */
export interface Wallet001InitGetLimitedStoreListItem {
    // 加盟店ID
    StoreID?: string;
    // 加盟店画像
    StoreImage?: string;
    // 加盟店名称
    StoreName?: string;
    // 店舗区分
    StoreCategory?: string;
    // 店舗概要
    StoreInformation?: string;
}

/**
 * Wallet001InitGetCouponListResult
 */
export interface Wallet001InitGetLimitedStoreListItemResult {
    // クーポン一覧
    LimitedStoreList: Wallet001InitGetLimitedStoreListItem[];
    // 会員限定の店舗区分ID
    LimitedStoreCategoryID?: string;
}
