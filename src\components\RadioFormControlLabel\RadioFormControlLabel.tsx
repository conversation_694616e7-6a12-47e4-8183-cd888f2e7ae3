import { FormControlLabel, Radio } from '@mui/material';
import { useRadioGroup } from '@mui/material/RadioGroup';
import { styled } from '@mui/system';
import React from 'react';

/**
 * Define the type for the props received by the RadioFormControlLabel component
 */
export type RadioFormControlType = {
    value: boolean | string | number;
    label: string;
    name?: string;
    className?: string;
    color?: string;
};

/**
 * Styled component for customized FormControlLabel
 */
const StyledFormControlLabel = styled(FormControlLabel)(({ theme, checked, color }) => ({
    '.MuiFormControlLabel-label': {
        color: checked ? theme.palette.primary?.main : color ?? '#222222',
    },
    '.MuiRadio-root': {
        color: checked ? theme.palette.primary?.main : color ?? '#757575',
    },
}));

/**
 * RadioFormControlLabel component
 * @param props RadioFormControlType
 * @returns React.JSX.Element
 */
const RadioFormControlLabel = (props: RadioFormControlType): React.JSX.Element => {
    const radioGroup = useRadioGroup();
    // Determine if the current radio button is checked
    const checked = radioGroup?.value === props.value;

    return <StyledFormControlLabel checked={checked} {...props} control={<Radio size="medium" />} />;
};

export default React.memo(RadioFormControlLabel);
