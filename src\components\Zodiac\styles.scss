.common-zodiac-component {
    .container {
        padding-left: 0 !important;
        padding-right: 0 !important;
    }

    .zodiac-container {
        margin-top: 24px;
        margin-bottom: 32px;
    }

    .zodiac-item {
        cursor: pointer;
        padding-left: 10px;
        padding-right: 10px;
        height: 90px;
        transition: all 0.5s ease;
        &.opacity {
            opacity: 0.2;
        }
    }

    .font-14 {
        font-size: 14px !important;
    }
    .p-message {
        font-size: 13px;
        height: 48px;
        .text-message-red {
            color: red;
        }
    }
    .p-link-auth-forgot {
        color: #151f41;
        display: inline;
        margin: auto;
        text-align: center;
        margin-top: -16px !important;
        text-decoration: underline;
        text-underline-offset: 2px;
        &:hover {
            cursor: pointer;
        }
    }

    .item-image-container {
        img {
            image-rendering: optimizeSpeed; // Older versions of FF
            image-rendering: -moz-crisp-edges; // FF 6.0+
            image-rendering: -webkit-optimize-contrast; // Webkit (non standard naming)
            image-rendering: -o-crisp-edges; // OS X & Windows Opera (12.02+)
            image-rendering: crisp-edges; // Possible future browsers.
            -ms-interpolation-mode: nearest-neighbor; // IE (non standard naming)
        }
    }

    .container-pic {
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .cheer-pin-picture {
        width: 75px;
        height: 75px;
        border-radius: 56px;
        padding: 2px;
        margin-bottom: 4px;
        cursor: pointer;
        & div {
            width: 50px;
            height: 50px;
            & img {
                width: 50px;
                height: 50px;
            }
        }
    }
}
