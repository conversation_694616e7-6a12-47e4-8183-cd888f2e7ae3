import { useCallback, useMemo } from 'react';
import { useDispatch } from 'react-redux';
import HomeAPI from '../apis/home';
import { BaseResponse } from '../models/common';
import { Home001InitGetBannerListModel, Home001InitGetBannerListResult } from '../models/home001-init-get-banner-list';
import { Home001InitGetCoinItem, Home001InitGetCoinModel } from '../models/home001-init-get-coin';
import { Home001InitGetCouponListModel, Home001InitGetCouponListResult } from '../models/home001-init-get-coupon-list';
import {
    Home001InitGetNewsUnreadFlagModel,
    Home001InitGetNewsUnreadFlagResult,
} from '../models/home001-init-get-news-unread-flag';
import {
    Home001InitGetNewsUnreadLatestModel,
    Home001InitGetNewsUnreadLatestResult,
} from '../models/home001-init-get-news-unread-latest';
import { Home001InitGetUserInfoModel, Home001InitGetUserInfoResult } from '../models/home001-init-get-userinfo';
import { apiCommon } from '../redux/actions/common';
import {
    Home001InitGetEventTicketListModel,
    Home001InitGetEventTicketListResult,
} from '../models/home001-init-get-event-ticket-list';

interface UseHome {
    postHome001InitGetNewsUnreadLatest: (payload: Home001InitGetNewsUnreadLatestModel) => void;
    postHome001InitGetCoin: (payload: Home001InitGetCoinModel) => void;
    postHome001InitGetCouponList: (payload: Home001InitGetCouponListModel) => void;
    postHome001InitGetEventTicketList: (payload: Home001InitGetEventTicketListModel) => void;
    postHome001InitGetBannerList: (payload: Home001InitGetBannerListModel) => void;
    postHome001InitGetNewsUnreadFlag: (payload: Home001InitGetNewsUnreadFlagModel) => void;
    postHome001InitGetUserInfo: (payload: Home001InitGetUserInfoModel) => void;
}

/**
 * useHome
 */
const useHome = (): UseHome => {
    const dispatch = useDispatch();

    /**
     * postHome001InitGetNewsUnreadLatest
     * @param payload Home001InitGetNewsUnreadLatestModel
     */
    const postHome001InitGetNewsUnreadLatest = useCallback(
        (payload: Home001InitGetNewsUnreadLatestModel) => {
            // get payload
            const { successCallback, errorCallback } = payload || {};

            // create promiser
            const promiser = HomeAPI.home001InitGetNewsUnreadLatest(payload);

            /**
             * handleResponse
             * @param response BaseResponse<Home001InitGetNewsUnreadLatestResult>
             */
            const handleResponse = (response: BaseResponse<Home001InitGetNewsUnreadLatestResult>): void => {
                successCallback?.(response);
            };

            // dispatch apiCommon
            dispatch(
                apiCommon({
                    promiser,
                    successCallback: handleResponse,
                    errorCallback,
                })
            );
        },
        [dispatch]
    );

    /**
     * postHome001InitGetCoin
     * @param payload Home001InitGetCoinModel
     */
    const postHome001InitGetCoin = useCallback(
        (payload: Home001InitGetCoinModel) => {
            // get payload
            const { successCallback, errorCallback } = payload || {};

            // create promiser
            const promiser = HomeAPI.home001InitGetCoin(payload);

            /**
             * handleResponse
             * @param response BaseResponse<Home001InitGetCoinItem[]>
             */
            const handleResponse = (response: BaseResponse<Home001InitGetCoinItem[]>): void => {
                successCallback?.(response);
            };

            // dispatch apiCommon
            dispatch(
                apiCommon({
                    promiser,
                    successCallback: handleResponse,
                    errorCallback,
                })
            );
        },
        [dispatch]
    );

    /**
     * postHome001InitGetCouponList
     * @param payload Home001InitGetCouponListModel
     */
    const postHome001InitGetCouponList = useCallback(
        (payload: Home001InitGetCouponListModel) => {
            // get payload
            const { successCallback, errorCallback } = payload || {};

            // create promiser
            const promiser = HomeAPI.home001InitGetCouponList(payload);

            /**
             * handleResponse
             * @param response BaseResponse<Home001InitGetCouponListResult>
             */
            const handleResponse = (response: BaseResponse<Home001InitGetCouponListResult>): void => {
                successCallback?.(response);
            };

            // dispatch apiCommon
            dispatch(
                apiCommon({
                    promiser,
                    successCallback: handleResponse,
                    errorCallback,
                })
            );
        },
        [dispatch]
    );

    /**
     * postHome001InitGetEventTicketList
     * @param payload Home001InitGetEventTicketListModel
     */
    const postHome001InitGetEventTicketList = useCallback(
        (payload: Home001InitGetEventTicketListModel) => {
            // get payload
            const { successCallback, errorCallback } = payload || {};

            // create promiser
            const promiser = HomeAPI.home001InitGetEventTicketList(payload);

            /**
             * handleResponse
             * @param response BaseResponse<Home001InitGetCouponListResult>
             */
            const handleResponse = (response: BaseResponse<Home001InitGetEventTicketListResult>): void => {
                successCallback?.(response);
            };

            // dispatch apiCommon
            dispatch(
                apiCommon({
                    promiser,
                    successCallback: handleResponse,
                    errorCallback,
                })
            );
        },
        [dispatch]
    );

    /**
     * postHome001InitGetBannerList
     * @param payload Home001InitGetBannerListModel
     */
    const postHome001InitGetBannerList = useCallback(
        (payload: Home001InitGetBannerListModel) => {
            // get payload
            const { successCallback, errorCallback } = payload || {};

            // create promiser
            const promiser = HomeAPI.home001InitGetBannerList(payload);

            /**
             * handleResponse
             * @param response BaseResponse<Home001InitGetBannerListResult>
             */
            const handleResponse = (response: BaseResponse<Home001InitGetBannerListResult>): void => {
                successCallback?.(response);
            };

            // dispatch apiCommon
            dispatch(
                apiCommon({
                    promiser,
                    successCallback: handleResponse,
                    errorCallback,
                })
            );
        },
        [dispatch]
    );

    /**
     * postHome001InitGetNewsUnreadFlag
     * @param payload Home001InitGetNewsUnreadFlagModel
     */
    const postHome001InitGetNewsUnreadFlag = useCallback(
        (payload: Home001InitGetNewsUnreadFlagModel) => {
            // get payload
            const { successCallback, errorCallback } = payload || {};

            // create promiser
            const promiser = HomeAPI.home001InitGetNewsUnreadFlag(payload);

            /**
             * handleResponse
             * @param response BaseResponse<Home001InitGetNewsUnreadFlagResult>
             */
            const handleResponse = (response: BaseResponse<Home001InitGetNewsUnreadFlagResult>): void => {
                successCallback?.(response);
            };

            // dispatch apiCommon
            dispatch(
                apiCommon({
                    promiser,
                    successCallback: handleResponse,
                    errorCallback,
                })
            );
        },
        [dispatch]
    );

    /**
     * postHome001InitGetUserInfo
     * @param payload Home001InitGetUserInfoModel
     */
    const postHome001InitGetUserInfo = useCallback(
        (payload: Home001InitGetUserInfoModel) => {
            // get payload
            const { successCallback, errorCallback } = payload || {};

            // create promiser
            const promiser = HomeAPI.home001InitGetUserInfo(payload);

            /**
             * handleResponse
             * @param response BaseResponse<Home001InitGetUserInfoResult>
             */
            const handleResponse = (response: BaseResponse<Home001InitGetUserInfoResult>): void => {
                successCallback?.(response);
            };

            // dispatch apiCommon
            dispatch(
                apiCommon({
                    promiser,
                    successCallback: handleResponse,
                    errorCallback,
                })
            );
        },
        [dispatch]
    );

    return useMemo(
        () => ({
            postHome001InitGetBannerList,
            postHome001InitGetCoin,
            postHome001InitGetCouponList,
            postHome001InitGetEventTicketList,
            postHome001InitGetNewsUnreadFlag,
            postHome001InitGetNewsUnreadLatest,
            postHome001InitGetUserInfo,
        }),
        [
            postHome001InitGetBannerList,
            postHome001InitGetCoin,
            postHome001InitGetCouponList,
            postHome001InitGetEventTicketList,
            postHome001InitGetNewsUnreadFlag,
            postHome001InitGetNewsUnreadLatest,
            postHome001InitGetUserInfo,
        ]
    );
};

export default useHome;
