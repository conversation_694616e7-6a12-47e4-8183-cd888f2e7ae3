import API from '../constants/api';
import { BaseResponse } from '../models/common';
import {
    Mission001InitGetMissionListModel,
    Mission001InitGetMissionListResult,
} from '../models/mission001-init-get-mission-list';
import {
    Mission002InitGetMissionDetailModel,
    Mission002InitGetMissionDetailResult,
} from '../models/mission002-init-get-mission-detail';
import {
    Mission003InitGetMissionDetailModel,
    Mission003InitGetMissionDetailResult,
} from '../models/mission003-init-get-mission-detail';
import { Mission003MainUpdateMissionModel } from '../models/mission003-main-update-mission';
import createAPI from './baseApi';

/**
 * MissionAPI
 */
class MissionAPI {
    /**
     * mission001InitGetMissionList
     * @param data Mission001InitGetMissionListModel
     * @returns Promise<BaseResponse<Mission001InitGetMissionListResult>>
     */
    static mission001InitGetMissionList = (
        data: Mission001InitGetMissionListModel
    ): Promise<BaseResponse<Mission001InitGetMissionListResult>> => {
        return createAPI<Mission001InitGetMissionListResult>({
            url: API.MISSION001_INIT_GET_MISSION_LIST,
            data: data,
        });
    };

    /**
     * mission002InitGetMissionDetail
     * @param data Mission002InitGetMissionDetailModel
     * @returns Promise<BaseResponse<Mission002InitGetMissionDetailResult>>
     */
    static mission002InitGetMissionDetail = (
        data: Mission002InitGetMissionDetailModel
    ): Promise<BaseResponse<Mission002InitGetMissionDetailResult>> => {
        return createAPI<Mission002InitGetMissionDetailResult>({
            url: API.MISSION002_INIT_GET_MISSION_DETAIL,
            data: data,
        });
    };

    /**
     * mission003InitGetMissionDetail
     * @param data Mission003InitGetMissionDetailModel
     * @returns Promise<BaseResponse<Mission003InitGetMissionDetailResult>>
     */
    static mission003InitGetMissionDetail = (
        data: Mission003InitGetMissionDetailModel
    ): Promise<BaseResponse<Mission003InitGetMissionDetailResult>> => {
        return createAPI<Mission003InitGetMissionDetailResult>({
            url: API.MISSION003_INIT_GET_MISSION_DETAIL,
            data,
        });
    };

    /**
     * mission003MainUpdateMission
     * @param data Mission003MainUpdateMissionModel
     * @returns Promise<BaseResponse>
     */
    static mission003MainUpdateMission = (data: Mission003MainUpdateMissionModel): Promise<BaseResponse> => {
        return createAPI({
            url: API.MISSION003_MAIN_UPDATE_MISSION,
            data,
        });
    };
}

export default MissionAPI;
