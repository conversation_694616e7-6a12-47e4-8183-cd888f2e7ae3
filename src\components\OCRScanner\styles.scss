#common-ocd-scan-container {
    // background-color: #222222;
    overflow: auto;

    .ocr-camera-container {
        overflow: hidden;
        position: relative;
    }

    .ocd-scan-title {
        text-align: center;
        color: #ffffff;
        padding: 20px 10px;
        font-size: 16px;
    }

    .btn-capture-container {
        display: flex;
        align-items: center;
        justify-content: center;
        background-color: var(--app-base-color);
        width: 80px;
        height: 80px;
        border-radius: 40px;
        cursor: pointer;
        .btn-capture {
            display: flex;
            align-items: center;
            justify-content: center;
            background-color: var(--app-base-color);
            width: 66px;
            height: 66px;
            border-radius: 40px;
            border: #fff 1px solid;
            .text-capture {
                color: #fff;
                text-align: center;
            }
        }
    }

    .overlay-layer {
        // define variable for overlay
        $bg-color: rgba(0, 0, 0, 0.5);
        $available: 16%;
        $no-available: 42%;

        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;

        .top {
            position: absolute;
            width: 100%;
            height: $no-available;
            top: 0;
            left: 0;
            background-color: $bg-color;
        }

        .bottom {
            position: absolute;
            width: 100%;
            height: $no-available;
            bottom: 0;
            background-color: $bg-color;
        }

        .left {
            position: absolute;
            left: 0;
            top: $no-available;
            width: 16px;
            height: $available;
            background-color: $bg-color;
        }

        .right {
            position: absolute;
            right: 0;
            top: $no-available;
            width: 16px;
            height: $available;
            background-color: $bg-color;
        }

        .center {
            position: absolute;
            height: $available;
            top: $no-available;
            left: 16px;
            right: 16px;
            border-width: 4px;
            border-color: $bg-color;
            border-style: solid;
        }

        .read-area {
            position: absolute;
            height: $available;
            top: $no-available;
            left: 16px;
            right: 16px;
            border-width: 4px;
            border-color: red;
            border-style: solid;
            border-radius: 8px;
            overflow: hidden;

        }
        img {
            position: absolute;
            height: $available;
            top: $no-available;
            left: 16px;
            right: 16px;
            border-radius: 8px;
        }
    }

    video::-webkit-media-controls {
        display: none;
    }

    video {
        width: 100%;
        height: 100%;
    }
}
