/* eslint-disable react/jsx-no-bind */
/* eslint-disable @typescript-eslint/explicit-function-return-type */
import { shallow } from 'enzyme';
import CommonTab, { CommonTabProps, TabItem } from '../CommonTab/CommonTab';
import Button from '../Button/Button';
import ReactRouterDom from 'react-router-dom';

// mock useNavigate function
const mockedUseNavigate = jest.fn();

/**
 * mock for react-router-dom lib
 */
jest.mock('react-router-dom', () => ({
    ...jest.requireActual('react-router-dom'),
    useSearchParams: () => null,
    useLoaderData: () => null,
    useNavigate: () => mockedUseNavigate,
    useLocation: () => null,
}));

/**
 * Unit test for CommonTab
 */
describe('Unit test for CommonTab', () => {
    beforeAll(() => {
        mockedUseNavigate.mockReset();

        jest.resetAllMocks();
        jest.restoreAllMocks();
    });

    /**
     * setup element
     * @param p CommonTabProps
     */
    const setup = (p?: Omit<CommonTabProps, 'children'>) => {
        return (
            <CommonTab {...p}>
                <TabItem label={'Tab1'} router={'/tab1'} component={<div>This is mock tab 1</div>} />
                <TabItem label={'Tab2'} router={'/tab2'} component={<div>This is mock tab 2</div>} />
            </CommonTab>
        );
    };

    /**
     * should display two tabs when TabItem is two
     */
    it('should display two tabs', () => {
        const wrapper = shallow(setup());

        const tabs = wrapper.find(TabItem);

        expect(tabs.length).toBe(2);
    });

    /**
     * should display label correctly
     */
    it('should display label correctly', () => {
        const wrapper = shallow(setup());

        const tabs = wrapper.find(TabItem);

        expect(tabs.at(0).props().label).toBe('Tab1');
        expect(tabs.at(1).props().label).toBe('Tab2');
    });

    /**
     * should display custom tabbar
     */
    it('should display custom tabbar', () => {
        const wrapper = shallow(
            <CommonTab>
                <TabItem
                    label={'Tab1'}
                    router={'tab1'}
                    component={<div>This is mock tab 1</div>}
                    renderTab={() => {
                        return <div>CustomTab1</div>;
                    }}
                />
                <TabItem label={'Tab2'} router={'tab2'} component={<div>This is mock tab 2</div>} />
            </CommonTab>
        );

        const tabs = wrapper.find(TabItem);

        expect(tabs.at(0).props().renderTab).toBeTruthy();
        expect(tabs.at(1).props().label).toBe('Tab2');
    });

    /**
     * should display TabItem
     */
    it('should display TabItem', () => {
        const wrapper = shallow(<TabItem label={'Tab1'} router={'tab1'} component={<div>This is mock tab 1</div>} />);

        expect(wrapper.find('div').text()).toEqual('This is mock tab 1');
    });

    /**
     * should change tab (replace) when click on tab
     */
    it('should change tab (replace) when click on tab', () => {
        const location = {
            pathname: '/tab2',
        } as any;
        jest.spyOn(ReactRouterDom, 'useLocation').mockReturnValueOnce(location);

        const wrapper = shallow(setup());

        const buttons = wrapper.find(Button);

        buttons.at(0).simulate('click');

        expect(mockedUseNavigate).toHaveBeenCalledWith('/tab1', { replace: true });
    });

    /**
     * should change tab when click on tab
     */
    it('should change tab when click on tab', () => {
        const wrapper = shallow(setup());

        const buttons = wrapper.find(Button);

        buttons.at(0).simulate('click');

        expect(mockedUseNavigate).toHaveBeenCalledWith('/tab1', { replace: true });
    });

    /**
     * should change tab (push) when click on tab
     */
    it('should change tab (push) when click on tab', () => {
        const location = {
            pathname: '/tab1',
        } as any;
        jest.spyOn(ReactRouterDom, 'useLocation').mockReturnValueOnce(location);
        const wrapper = shallow(
            setup({
                navigationType: 'PUSH',
            })
        );

        const buttons = wrapper.find(Button);

        buttons.at(1).simulate('click');

        expect(mockedUseNavigate).toHaveBeenCalledWith('/tab2');
    });
});
