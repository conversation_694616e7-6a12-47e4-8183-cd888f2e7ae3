import { DatePicker as DatePickerBase, DesktopDatePicker, MobileDatePicker } from '@mui/x-date-pickers';
import { shallow } from 'enzyme';
import DatePicker, { DatePickerIcon, DatePickerProps } from '../DatePicker/DatePicker';

/**
 * Unit test for DatePicker component
 */
describe('Unit test for DatePicker component', () => {
    /**
     * setup element
     * @param p CommonTabProps
     */
    const setup = (p?: DatePickerProps) => {
        return <DatePicker {...p} />;
    };

    /**
     * should display with default props
     */
    it('should display with default props', () => {
        const wrapper = shallow(setup());

        expect(wrapper.find(DesktopDatePicker)).toBeTruthy();
    });

    /**
     * should display with variant is "mobile"
     */
    it('should display with variant is "mobile"', () => {
        const wrapper = shallow(
            setup({
                variant: 'mobile',
            })
        );

        expect(wrapper.find(MobileDatePicker)).toBeTruthy();
    });

    /**
     * should display with variant is "responsive"
     */
    it('should display with variant is "responsive"', () => {
        const wrapper = shallow(
            setup({
                variant: 'responsive',
            })
        );

        expect(wrapper.find(DatePickerBase)).toBeTruthy();
    });

    /**
     * should display with error
     */
    it('should display with error', () => {
        const wrapper = shallow(
            setup({
                error: 'mock error',
            })
        );

        expect(wrapper.find(<div className="error">mock error</div>)).toBeTruthy();
    });

    /**
     * should display with custom input format
     */
    it('should display with custom input format', () => {
        const wrapper = shallow(
            setup({
                inputFormat: 'DD/MM/YYYY',
            })
        );

        const date = wrapper.find(DesktopDatePicker);

        expect(date.props().format).toBe('DD/MM/YYYY');
    });

    /**
     * should display DatePickerIcon
     */
    it('should display DatePickerIcon', () => {
        const wrapper = shallow(setup());

        expect(wrapper.find(DatePickerIcon)).toBeTruthy();
    });

    /**
     * should display DatePickerIcon
     */
    it('should render DatePickerIcon', () => {
        const wrapper = shallow(<DatePickerIcon />);

        const img = wrapper.find('img');
        expect(img).toBeTruthy();
        expect(img.props().width).toBe(17);
        expect(img.props().height).toBe(18);
    });
});
