/* eslint-disable react/jsx-no-bind */
/* eslint-disable @typescript-eslint/explicit-function-return-type */
import { Modal, Slider } from '@mui/material';
import { makeStyles } from '@mui/styles';
import 'cropperjs/dist/cropper.css';
import React, { useCallback, useRef } from 'react';
import Cropper, { ReactCropperElement } from 'react-cropper';
import Utils from '../../utils/utils';
import Button from '../Button';

const useStyles = makeStyles({
    root: {
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
    },
    modal: {
        width: 420,
        height: 'auto',
        backgroundColor: 'white',
        display: 'flex',
        justifyContent: 'center',
        flexFlow: 'column',
        borderRadius: '0px 0px 10px 10px',
        '& .crop-container': {
            height: 400,
            borderRadius: '10px 10px 0px 0px',
            backgroundColor: '#f4f7fb',
            position: 'relative',
            '& .container': {},
            '& .crop-area': {
                border: '3px solid #00A0FF',
            },
            '& .media': {},
        },
        '& .controls': {
            height: 40,
            marginLeft: 50,
            marginRight: 50,
            display: 'flex',
            alignItems: 'center',
            marginTop: 10,
            '& .zoom-range': {
                color: '#00A0FF',
            },
        },
        '& .buttons': {
            height: 40,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between',
            marginRight: 20,
            marginLeft: 20,
            marginBottom: 20,
        },
        '& .close': {
            marginRight: 10,
        },
    },
});

type Props = {
    open: boolean;
    onClose: (state?: string) => void;
    imgSrc: string;
    zoom: number;
    setZoom: (zoom: number) => void;
    showCroppedImage?: (data: string) => void;
    aspectRatio?: number;
    getCroppedImageAsBlob?: (data: Blob | null) => void;
};

const ReactCropperModal: React.FC<Props> = ({
    open,
    onClose,
    imgSrc,
    setZoom,
    zoom,
    showCroppedImage,
    aspectRatio,
    getCroppedImageAsBlob,
}) => {
    const classes = useStyles();
    const cropperRef = useRef<ReactCropperElement>(null);

    /**
     * handleClickCloseCropper
     */
    const handleClickCloseCropper = useCallback((): void => {
        onClose();
    }, [onClose]);

    /**
     * handleClickConfirmButton
     */
    const handleClickConfirmButton = useCallback((): void => {
        // close modal with state is `confirm`
        onClose('confirm');
        // return string image is base64
        showCroppedImage?.(String(cropperRef.current?.cropper.getCroppedCanvas()?.toDataURL()));

        // if getCroppedImageAsBlob function is passed
        if (getCroppedImageAsBlob) {
            const croppedCanvas = cropperRef.current?.cropper?.getCroppedCanvas();

            if (croppedCanvas) {
                // get cropped box size
                const { width, height } = croppedCanvas;

                // calc ratio
                const ratio = width / height;
                // calc resize width
                const resizeWidth = width > height ? Math.min(750, width) : Math.min(750, height) * ratio;
                // calc resize height
                const resizeHeight = height > width ? Math.min(750, height) : resizeWidth / ratio;

                // create new canvas
                const canvas = document.createElement('canvas');

                // set new canvas's size
                canvas.width = resizeWidth;
                canvas.height = resizeHeight;

                // get context
                const context = canvas.getContext('2d');
                // draw croppedCanvas on new canvas
                context?.drawImage(croppedCanvas, 0, 0, resizeWidth, resizeHeight);

                // to blob from new canvas
                canvas.toBlob(getCroppedImageAsBlob, undefined, 1);
            }
        }
    }, [getCroppedImageAsBlob, onClose, showCroppedImage]);

    /**
     * handleClickCloseCropper
     */
    const handleClickCloseModal = useCallback(
        (event: React.SyntheticEvent, reason: string): void => {
            if (reason === 'backdropClick') {
                event.preventDefault();
            } else {
                onClose();
            }
        },
        [onClose]
    );

    return (
        <Modal open={open} onClose={handleClickCloseModal} className={classes.root} disableAutoFocus={true}>
            <div className={classes.modal}>
                <Cropper
                    src={imgSrc}
                    style={{ width: '100%', height: '100%', maxHeight: 350 }}
                    ref={cropperRef}
                    zoomTo={zoom}
                    initialAspectRatio={1}
                    viewMode={1}
                    minCropBoxHeight={10}
                    minCropBoxWidth={10}
                    background={false}
                    responsive={true}
                    autoCropArea={1}
                    checkOrientation={false}
                    guides={true}
                    zoomOnWheel={false}
                    zoomOnTouch={false}
                    aspectRatio={aspectRatio}
                    dragMode="move"
                    restore={true}
                    autoCrop={true}
                    scalable={true}
                />
                <div className="controls">
                    <Slider
                        min={0}
                        value={zoom}
                        max={4}
                        step={0.1}
                        onChange={(e, value) => {
                            if (typeof value === 'number') {
                                setZoom(value);
                            }
                        }}
                        className="zoom-range"
                    />
                </div>
                <div className="buttons">
                    <Button variant="outlined" className="close w-100" onClick={handleClickCloseCropper}>
                        {Utils.t('common.button.cancel')}
                    </Button>
                    <Button name="okBtn" variant="contained" className="ok w-100" onClick={handleClickConfirmButton}>
                        {Utils.t('common.button.confirm')}
                    </Button>
                </div>
            </div>
        </Modal>
    );
};
export default ReactCropperModal;
