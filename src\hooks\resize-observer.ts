import _ from 'lodash';
import { useEffect, useMemo, useState } from 'react';
import Utils from '../utils/utils';

/**
 * ObserverOptions
 */
interface ObserverOptions {
    /**
     * Element's id that want to observer
     */
    id: string;
}

/**
 * useResizeObserver
 * @param options ObserverOptions
 * @returns ResizeObserverEntry
 */
const useResizeObserver = (options: ObserverOptions): ResizeObserverEntry => {
    const id = useMemo(() => options.id, [options.id]);
    const [result, setResult] = useState<ResizeObserverEntry>();

    useEffect(() => {
        const target = document.getElementById(id);

        if (target) {
            Utils.createAppObserver((entries) => {
                const entry = _.last(entries) as ResizeObserverEntry;
                setResult(entry);
            }).observe(target);
        }
    });

    return useMemo(() => (result || {}) as ResizeObserverEntry, [result]);
};

export default useResizeObserver;
