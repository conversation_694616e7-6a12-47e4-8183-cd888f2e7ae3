import { CommonCallback } from './common';

/**
 * Mission001InitGetMissionListModel
 */
export interface Mission001InitGetMissionListModel extends CommonCallback {
    // ブランドID
    BrandID: string;
    // ユーザーID
    CheerID?: string;
    // ページサイズ
    PageSize: number;
    // 開催ステータス
    HeldStatus?: number;
    ExclusiveStartKey?: string;
}

/**
 * Mission001InitGetMissionListItem
 */
export interface Mission001InitGetMissionListItem {
    MissionID: string;
    // ミッション名称
    MissionName: string;
    // ミッション画像
    MissionImage: string;
    // ミッション説明文
    MissionDescription: string;
    // 開始日
    StartDateTime: string;
    // 終了日
    EndDateTime: string;
    // ミッションクリアフラグ
    CompleteStatus: boolean;
}

/**
 * Mission001InitGetMissionListResult
 */
export interface Mission001InitGetMissionListResult {
    // ミッション一覧
    MissionList: Mission001InitGetMissionListItem[];
    // LastEvaluatedKey
    LastEvaluatedKey?: string;
}
