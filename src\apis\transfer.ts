import API from '../constants/api';
import { BaseResponse } from '../models/common';
import { Transfer007InitGetCoinModel, Transfer007InitGetCoinResult } from '../models/transfer007-init-get-coin';
import createAPI from './baseApi';

/**
 * TransferAPI
 */
class TransferAPI {
    /**
     * transfer007InitGetCoin
     * @param data Transfer007InitGetCoinModel
     * @returns Promise<BaseResponse<Transfer007InitGetCoinResult[]>>
     */
    static transfer007InitGetCoin = (
        data: Transfer007InitGetCoinModel
    ): Promise<BaseResponse<Transfer007InitGetCoinResult[]>> => {
        return createAPI<Transfer007InitGetCoinResult[]>({
            url: API.TRANSFER007_INIT_GET_COIN,
            data,
        });
    };
}

export default TransferAPI;
