import { CommonCallback } from './common';

/**
 * CheerRegisterAccountModel
 */
export interface CheerRegisterAccountModel extends CommonCallback {
    // 応援者ID
    CheerID?: number;
    // EOAアドレス（BC用）
    CheerEOAAddress: string;
    // 質問1ID
    QuestionID1: number;
    // 質問2ID
    QuestionID2: number;
    // 質問3ID
    QuestionID3: number;
    // 絵合わせ番号（干支）
    CheerPinPicture1: number;
    // 絵合わせ番号（12星座）
    CheerPinPicture2: number;
    // 限定会員ID
    FanClubID?: string;
    // AccountStatus
    AccountStatus?: string;
    // CheerPassword
    CheerPassword?: string;
}

/**
 * Authorization
 */
export interface Authorization {
    IdentityId: string;
    Token: string;
}

/**
 * CheerRegisterAccountResponse
 */
export interface CheerRegisterAccountResponse {
    // result
    result: Authorization;
    // errorMessage
    errorMessage?: string;
}
