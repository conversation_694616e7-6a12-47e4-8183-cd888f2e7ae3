import { Typography } from '@mui/material';
import { memo, useCallback, useEffect, useMemo, useState } from 'react';
import { useLoaderData, useNavigate } from 'react-router-dom';
import './styles.scss';
import { isBrowser, isTablet } from 'react-device-detect';
import Screens from '../../../../constants/screens';
import Utils from '../../../../utils/utils';
import CommonAPI from '../../../../apis/common';
import StorageServices from '../../../../services/storage.service';
import Button from '../../../../components/Button';
import Container from '../../../../components/Container';
import Input from '../../../../components/Input';
import Icons from '../../../../components/Icons';
import { InitGetChargeLimitModel } from '../../../../models/init-get-chargelimit';
import ChargeAPI from '../../../../apis/charge';
import API from '../../../../constants/api';
import CommonUtils from '../../../../utils/common';
import { BankListItem } from '../../../../models/chargeintbnkpay008-init-get-bankinfo';
import Image from '../../../../components/Image';
import { Home001InitGetCoinItem } from '../../../../models/home001-init-get-coin';

// defaultCurrencyUnit
const defaultCurrencyUnit = ['1000', '3000', '5000', '10000', '30000', '50000'];

/**
 * ChargeIntBnkPay005
 * ID: CHARGEINTBNKPAY005
 * Name: BankPay決済 (金額入力)
 * BankPay決済で金額を入力する画面
 * @returns React.JSX.Element
 */
const ChargeIntBnkPay005 = (): React.JSX.Element => {
    const navigate = useNavigate();
    const loaderData = useLoaderData() as Home001InitGetCoinItem;
    const [chargeLimit, setChargeLimit] = useState(0);
    const [bankInfo, setBankInfo] = useState<BankListItem[]>([]);
    const [chargeAmount, setCurrentValue] = useState('');

    const dataMedalServiceItem = useMemo(() => loaderData, [loaderData]);

    /**
     * handleGetChargeLimit
     */
    const handleGetChargeLimit = useCallback(async () => {
        if (dataMedalServiceItem?.MedalServiceID) {
            const payload: InitGetChargeLimitModel = {
                MedalServiceID: dataMedalServiceItem?.MedalServiceID,
                useLock: true,
            };

            const { status, isSuccess, result, Message } = await ChargeAPI.chargeIntBnkPay005InitGetChargeLimit(
                payload
            );

            if (status === API.STATUS_CODE.SUCCESS && isSuccess && result) {
                setChargeLimit(result.ChargeLimit || 0);
            } else {
                CommonUtils.showMessage({
                    message: Utils.t(Message || 'api.common.unknown_error'),
                    type: 'ERROR',
                });
            }
        }
    }, [dataMedalServiceItem?.MedalServiceID]);

    /**
     * handleClickToRegisterBankAccountButton
     */
    const handleClickToRegisterBankAccountButton = useCallback(() => {
        CommonUtils.closeMessageById('charge-int-bank-pay005-info-fail');
        CommonAPI.commonUserTransitionHistory({
            OriginScreenName: window.location.pathname,
            DestinationScreenName: Screens.CHARGEINT_BNKPAY001,
            ElementName: Utils.t('charge.charge_serial_code.input.btn_submit')?.replaceAll('\n', ''),
            DeviceID: isBrowser ? 'PC' : isTablet ? 'Tablet' : 'mobile',
        });
        navigate(Screens.CHARGEINT_BNKPAY001);
    }, [navigate]);

    /**
     * handleGetBankInfo
     */
    const handleGetBankInfo = useCallback(async () => {
        const { status, result, Message } = await ChargeAPI.chargeIntBnkPay005InitGetBankInfo({
            useLock: true,
        });
        if (status === API.STATUS_CODE.SUCCESS) {
            setBankInfo(result?.BankList || []);
        } else {
            switch (status) {
                case 2:
                    CommonUtils.showMessage({
                        type: 'ERROR',
                        id: 'charge-int-bank-pay005-info-fail',
                        message: (
                            <div className="wrapper-mess-register-fail">
                                <Typography className="white-space-pre-line ff-noto-bold" fontSize={16}>
                                    {Utils.t('charge.bnk_pay.bnk_pay005.message_not_complete')}
                                </Typography>
                            </div>
                        ),
                        iconClass: 'icon-charge-int-bank-pay005-fail',
                        actions: {
                            direction: 'horizontal',
                            children: [
                                <Button
                                    key="delete"
                                    className="w-100"
                                    variant="contained"
                                    name="toRegisterBankAccountButton"
                                    onClick={handleClickToRegisterBankAccountButton}
                                >
                                    {Utils.t('charge.bnk_pay.bnk_pay005.register_bank_account_btn')}
                                </Button>,
                            ],
                        },
                    });
                    break;
                case 3:
                    CommonUtils.showMessage({
                        type: 'ERROR',
                        id: 'charge-int-bank-pay005-info-fail',
                        message: (
                            <div className="wrapper-mess-register-fail">
                                <>
                                    <Typography className="white-space-pre-line ff-noto-bold" fontSize={16}>
                                        {Utils.t('charge.bnk_pay.bnk_pay005.account_disable')}
                                    </Typography>
                                    <Typography className="white-space-pre-line ff-noto-bold" fontSize={16}>
                                        {Utils.t('charge.bnk_pay.bnk_pay005.account_disable_1')}
                                    </Typography>
                                    <Typography className="white-space-pre-line ff-noto-bold" fontSize={16}>
                                        {Utils.t('charge.bnk_pay.bnk_pay005.account_disable_2')}
                                    </Typography>
                                </>
                            </div>
                        ),
                        iconClass: 'icon-charge-int-bank-pay005-fail',
                        actions: {
                            direction: 'horizontal',
                            children: [
                                <Button
                                    key="delete"
                                    className="w-100"
                                    variant="contained"
                                    name="toRegisterBankAccountButton"
                                    onClick={handleClickToRegisterBankAccountButton}
                                >
                                    {Utils.t('charge.bnk_pay.bnk_pay005.register_bank_account_btn')}
                                </Button>,
                            ],
                        },
                    });
                    break;
                case 4:
                    CommonUtils.showMessage({
                        type: 'ERROR',
                        id: 'charge-int-bank-pay005-info-fail',
                        message: (
                            <div className="wrapper-mess-register-fail">
                                <>
                                    <Typography className="white-space-pre-line ff-noto-bold" fontSize={16}>
                                        {Utils.t('charge.bnk_pay.bnk_pay005.account_disable_3')}
                                    </Typography>
                                </>
                            </div>
                        ),
                        iconClass: 'icon-charge-int-bank-pay005-fail',
                        actions: {
                            direction: 'horizontal',
                            children: [
                                <Button
                                    key="delete"
                                    className="w-100"
                                    variant="contained"
                                    name="toRegisterBankAccountButton"
                                    onClick={handleClickToRegisterBankAccountButton}
                                >
                                    {Utils.t('charge.bnk_pay.bnk_pay005.register_bank_account_btn')}
                                </Button>,
                            ],
                        },
                    });
                    break;

                default:
                    CommonUtils.showMessage({
                        message: Utils.t(Message || 'api.common.unknown_error'),
                        type: 'ERROR',
                    });
                    break;
            }
        }
    }, [handleClickToRegisterBankAccountButton]);

    useEffect(() => {
        handleGetChargeLimit();
        handleGetBankInfo();
    }, [handleGetBankInfo, handleGetChargeLimit]);

    /**
     * handleClickDepositButton
     * @param value string
     */
    const handleClickDepositButton = useCallback(
        (value: string) => () => {
            setCurrentValue(Utils.removeNotDigit(value));
        },
        []
    );

    /**
     * handleChangePaymentInput
     * @param event React.ChangeEvent<HTMLInputElement>
     */
    const handleChangePaymentInput = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
        const value = event.target.value;
        if (value) {
            setCurrentValue(Utils.removeNotDigit(value));
        } else {
            setCurrentValue('');
        }
    }, []);

    /**
     * handleClickDoneButton
     */
    const handleClickDoneButton = useCallback(() => {
        CommonAPI.commonUserTransitionHistory({
            OriginScreenName: window.location.pathname,
            DestinationScreenName: Screens.CHARGEINT_BNKPAY006,
            ElementName: Utils.t('charge.charge_input.button.confirm')?.replaceAll('\n', ''),
            DeviceID: isBrowser ? 'PC' : isTablet ? 'Tablet' : 'mobile',
        });
        navigate(Screens.CHARGEINT_BNKPAY006, {
            state: {
                chargeAmount,
                bankInfo: bankInfo[0],
            },
        });
    }, [bankInfo, chargeAmount, navigate]);

    /**
     * handleClickBackButton
     * @param isButton: boolean
     */
    const handleClickBackButton = useCallback(
        (isButton?: boolean) => () => {
            CommonAPI.commonUserTransitionHistory({
                OriginScreenName: window.location.pathname,
                DestinationScreenName: StorageServices.Local.get('PREVIOUS_SCREEN'),
                ElementName: isButton
                    ? Utils.t('charge.charge_input.button.cancel')?.replaceAll('\n', '')
                    : Utils.t('common.button.back_button')?.replaceAll('\n', ''),
                DeviceID: isBrowser ? 'PC' : isTablet ? 'Tablet' : 'mobile',
            });
            navigate(Screens.CHARGE, { state: { medalServiceItem: dataMedalServiceItem } });
        },
        [navigate, dataMedalServiceItem]
    );

    /**
     * renderDefaultCurrency
     * @param item string
     * @param index number
     */
    const renderDefaultCurrency = useCallback(
        (item: string, index: number) => (
            <div key={index} className="option-value-detail">
                <Button
                    className="option-value-input"
                    onClick={handleClickDepositButton(item)}
                    borderRadius={22}
                    height={58}
                    fontSize={20}
                    disableHover
                    name={`depositButton-${item}`}
                    disabled={Number(Utils.removeNotDigit(item)) > chargeLimit}
                >
                    +{Utils.formatNumber(item)}
                </Button>
            </div>
        ),
        [chargeLimit, handleClickDepositButton]
    );

    /**
     * handleClickToDeleteBankAccountButton
     */
    const handleClickToDeleteBankAccountButton = useCallback(() => {
        // CHARGEINTBNKPAY008_BankPay決済 (口座削除) に遷移する。
        CommonAPI.commonUserTransitionHistory({
            OriginScreenName: window.location.pathname,
            DestinationScreenName: Screens.CHARGEINT_BNKPAY008,
            ElementName: '',
            DeviceID: isBrowser ? 'PC' : isTablet ? 'Tablet' : 'mobile',
        });
        navigate(Screens.CHARGEINT_BNKPAY008);
    }, [navigate]);

    /**
     * handleRenderBankInfo
     */
    const handleRenderBankInfo = useMemo(() => {
        let depositType = '';
        switch (Number(bankInfo?.[0]?.DepositType)) {
            case 1:
                depositType = Utils.t('charge.bnk_pay.bnk_pay005.deposit_type_one');
                break;
            case 2:
                depositType = Utils.t('charge.bnk_pay.bnk_pay005.deposit_type_two');
                break;
            default:
                break;
        }

        if (bankInfo?.length === 0) return '';
        return (
            <div className="d-flex justify-content-between align-items-center mt-16 mb-8">
                <div className="d-flex align-items-center">
                    <div className="mr-16">
                        <Image src={bankInfo?.[0]?.BankImagePath} height={32} alt="" />
                    </div>
                    <div>
                        <div>{bankInfo?.[0]?.BankName || ''}</div>
                        <div className="fs-12 color-ccc">{bankInfo?.[0]?.BranchName || ''}</div>
                        <div className="fs-12 color-ccc">
                            {depositType} {bankInfo?.[0]?.MaskedAccountNum}
                        </div>
                    </div>
                </div>
                <div className="cursor-pointer" onClick={handleClickToDeleteBankAccountButton}>
                    <Icons.ArrowIcon color="#1a0dab" width={50} height={25} />
                </div>
            </div>
        );
    }, [bankInfo, handleClickToDeleteBankAccountButton]);

    return useMemo(
        () => (
            <Container
                screenName="ChargeIntBnkPay005"
                useHeader={{
                    type: 'backLeftTitleCenterAndMenuRight',
                    title: Utils.t('charge.bnk_pay.bnk_pay005.title'),
                    onLeftButtonClick: handleClickBackButton(),
                }}
            >
                <div className="charge-int-bnk-pay005">
                    <div className="charging-input">
                        <div className="wrapper-register-info pt-24">
                            <div className="text-center mb-16">
                                <Typography className="ff-noto-bold fs-16">
                                    {Utils.t('charge.bnk_pay.bnk_pay005.message1')}
                                </Typography>
                                <Typography className="ff-noto-bold fs-16">
                                    {Utils.t('charge.bnk_pay.bnk_pay005.message2')}
                                </Typography>
                            </div>
                        </div>
                        <hr className="line" />
                        <Input
                            name="paymentInput"
                            value={Utils.formatNumber(chargeAmount)}
                            label={<span className="fs-14 ff-noto-bold">{Utils.t('charge.charge_input.label')}</span>}
                            subLabel={<span className="fs-14">{Utils.t('charge.charge_input.sub_label')}</span>}
                            placeholder="0"
                            required
                            minLength={0}
                            maxLength={6}
                            onChange={handleChangePaymentInput}
                            className="text-end"
                            useNumberFormat={{
                                separator: ',',
                                maxValue: chargeLimit,
                                x1000: true,
                                equalMaxValue: true,
                            }}
                            suffix={
                                <Typography className="input-suffix">{Utils.t('common.text.yen_currency')}</Typography>
                            }
                        />
                        <Typography className="charge-limit mb-3">
                            {Utils.t('charge.bnk_pay.bnk_pay005.max_label_input', Utils.formatNumber(chargeLimit))}
                        </Typography>
                        <div className="option-value">{defaultCurrencyUnit?.map(renderDefaultCurrency)}</div>
                        {handleRenderBankInfo}
                    </div>
                    <div className="d-flex justify-content-between my-3 px-3 btn-container">
                        <Button
                            name="cancelButton"
                            className="flex-1"
                            variant="outlined"
                            fontSize={14}
                            height={49}
                            onClick={handleClickBackButton(true)}
                        >
                            {Utils.t('charge.charge_input.button.cancel')}
                        </Button>
                        <Button
                            disabled={!chargeAmount}
                            name="doneButton"
                            className="flex-1"
                            fontSize={14}
                            height={49}
                            onClick={handleClickDoneButton}
                        >
                            {Utils.t('charge.charge_input.button.confirm')}
                        </Button>
                    </div>
                </div>
            </Container>
        ),
        [
            handleClickBackButton,
            chargeAmount,
            handleChangePaymentInput,
            chargeLimit,
            renderDefaultCurrency,
            handleClickDoneButton,
            handleRenderBankInfo,
        ]
    );
};

export default memo(ChargeIntBnkPay005);
