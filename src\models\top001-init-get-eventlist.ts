import { CommonCallback } from './common';

/**
 * Top001InitGetEventListModel
 */
export interface Top001InitGetEventListModel extends CommonCallback {
    // ブランドID
    BrandID: string;
    // 1ページあたりの取得アイテム数
    PageSize: number;
}

/**
 * Top001InitGetEventListItem
 */
export interface Top001InitGetEventListItem {
    // イベントチケットID
    EventTicketID: string;
    // イベントチケット名
    EventTicketName: string;
    // イベントチケットトップ画像
    EventTicketTopImage: string;
    // イベントチケット開催日
    EventTicketDateTime: string;
    // イベントチケット説明
    EventTicketDescription: string;
}

/**
 * Top001InitGetEventListResult
 */
export interface Top001InitGetEventListResult {
    // イベントチケット一覧
    EventList: Top001InitGetEventListItem[];
}
