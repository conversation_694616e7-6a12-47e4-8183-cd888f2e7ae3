/* eslint-disable indent */
import { Dialog } from '@mui/material';
import clsx from 'clsx';
import {
    ForwardedRef,
    ReactElement,
    forwardRef,
    memo,
    useCallback,
    useImperativeHandle,
    useMemo,
    useState,
    Ref,
} from 'react';
import Icons from '../Icons/Icons';
import './styles.scss';
import Slide from '@mui/material/Slide';
import { TransitionProps } from '@mui/material/transitions';

/**
 * DialogCallback
 */
interface DialogCallback {
    /**
     * callback function
     */
    callback?: () => void;
}
/**
 * CommonDialogProps
 */
export interface CommonDialogProps {
    /**
     * children
     */
    children: ReactElement;
    /**
     * childrenClassName
     */
    childrenClassName?: string;
    /**
     * xCenter: center horizontal
     */
    xCenter?: boolean;
    /**
     * yCenter: center vertical
     */
    yCenter?: boolean;
    /**
     * useDialogCloseButton
     */
    useDialogCloseButton?: boolean | DialogCallback;
    /**
     * useBackdropDismiss
     */
    useBackdropDismiss?: boolean | DialogCallback;
    /**
     * useTransaction
     */
    useTransaction?: boolean | 'left' | 'right' | 'up' | 'down';
    /**
     * contentClassName
     */
    contentClassName?: string;
    /**
     * keepMounted
     */
    keepMounted?: boolean;
}

/**
 * CommonDialogRef
 */
export interface CommonDialogRef {
    /**
     * toggleDialog
     * @param open boolean
     * @returns void
     */
    toggleDialog: (open?: boolean) => void;

    /**
     * isOpen
     */
    isOpen?: boolean;
}

/**
 * Transition
 */
const Transition = forwardRef(
    (
        props: TransitionProps & {
            children: ReactElement;
            direction?: 'left' | 'right' | 'up' | 'down';
        },
        ref: Ref<unknown>
    ) => {
        const { direction, ...rest } = props;
        return <Slide direction={direction || 'up'} ref={ref} {...rest} />;
    }
);

Transition.displayName = 'Transition';

/**
 * CommonDialog
 * @param props CommonDialogProps
 * @param ref ForwardedRef<CommonDialogRef>
 */
const CommonDialog = forwardRef((props: CommonDialogProps, ref: ForwardedRef<CommonDialogRef>) => {
    const [open, setOpen] = useState(false);
    const {
        children,
        xCenter,
        yCenter,
        useDialogCloseButton,
        useBackdropDismiss,
        childrenClassName,
        useTransaction,
        contentClassName,
        keepMounted = false,
    } = useMemo(() => props, [props]);

    /**
     * toggleDialog
     */
    const toggleDialog = useCallback((open?: boolean) => {
        if (open !== undefined) {
            setOpen(open);
        } else if (open === undefined) {
            setOpen((prev) => !prev);
        }
    }, []);

    useImperativeHandle(
        ref,
        () => ({
            toggleDialog,
            isOpen: open,
        }),
        [open, toggleDialog]
    );

    /**
     * onCloseButtonClickHandler
     */
    const onCloseButtonClickHandler = useCallback(() => {
        // toggle dialog
        toggleDialog();
        // if useDialogCloseButton available
        if (useDialogCloseButton) {
            const { callback } = useDialogCloseButton as DialogCallback;
            callback?.();
        }
    }, [toggleDialog, useDialogCloseButton]);

    /**
     * onBackdropClickHandler
     * @param event any
     * @param reason 'backdropClick' | 'escapeKeyDown'
     */
    const onCloseHandler = useCallback(
        (_: unknown, reason: 'backdropClick' | 'escapeKeyDown') => {
            if (reason === 'backdropClick' && useBackdropDismiss) {
                // toggle dialog
                toggleDialog();
                const { callback } = useBackdropDismiss as DialogCallback;
                callback?.();
            }
        },
        [toggleDialog, useBackdropDismiss]
    );

    /**
     * onBackdropClickHandler
     */
    const onBackdropClickHandler = useCallback(() => {
        // if useBackdropDismiss
        if (useBackdropDismiss) {
            // toggle dialog
            toggleDialog();
            const { callback } = useBackdropDismiss as DialogCallback;
            callback?.();
        }
    }, [toggleDialog, useBackdropDismiss]);

    return useMemo(
        () => (
            <Dialog
                open={open}
                className="common-dialog-component"
                disableScrollLock={false}
                onClose={onCloseHandler}
                TransitionComponent={useTransaction ? Transition : undefined}
                keepMounted={keepMounted}
            >
                <div
                    className={clsx('d-flex w-100 h-100 dialog-content', contentClassName, {
                        'justify-content-center': xCenter,
                        'align-items-center': yCenter,
                    })}
                >
                    {useDialogCloseButton && (
                        <div className="close-button-container" onClick={onCloseButtonClickHandler}>
                            <Icons.CloseIconHaveBackground />
                        </div>
                    )}
                    {useBackdropDismiss && (
                        <div
                            className="d-flex w-100 h-100 position-absolute dialog-content-overlay"
                            onClick={onBackdropClickHandler}
                        />
                    )}
                    <div className={clsx('dialog-content-children p-16 d-flex', childrenClassName)}>{children}</div>
                </div>
            </Dialog>
        ),
        [
            open,
            onCloseHandler,
            useTransaction,
            keepMounted,
            xCenter,
            yCenter,
            contentClassName,
            useDialogCloseButton,
            onCloseButtonClickHandler,
            useBackdropDismiss,
            onBackdropClickHandler,
            childrenClassName,
            children,
        ]
    );
});

// display name for CommonDialog
CommonDialog.displayName = 'CommonDialog';

export default memo(CommonDialog);
