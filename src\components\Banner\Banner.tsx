import React, { useCallback, useMemo, memo } from 'react';
import { Login001InitGetBannerListItem } from '../../models/login001-init-get-banner-list';
import Frame from '../Frame/Frame';
import Image from '../Image/Image';

/**
 * BannerProps
 */
export type BannerProps = {
    data: Login001InitGetBannerListItem[];
    height?: number;
    width?: number;
};

/**
 * ID: Banner
 * Name: Banner common
 * @returns React.JSX.Element
 */
function Banner(props: BannerProps): React.JSX.Element {
    const { data = [], height = 80, width = 345 } = useMemo(() => props, [props]);

    /**
     * handleClickImage
     * handle on click image, should transition to LinkURL if available
     * @param item Login001InitGetBannerListItem
     * @param type '_blank' | '_self'
     */
    const handleClickImage = useCallback(
        (item: Login001InitGetBannerListItem, type: '_blank' | '_self' = '_blank') =>
            () => {
                if (item.LinkURL) {
                    window.open(item.LinkURL, type);
                }
            },
        []
    );

    /**
     * renderBannerItem
     * @param item Login001InitGetBannerListItem
     */
    const renderBannerItem = useCallback(
        (item: Login001InitGetBannerListItem) => {
            return (
                <Frame aspectRatio={{ w: width, h: height }} key={item.BannerID}>
                    <Image
                        className="w-100 h-100 d-block mt-8 image-banner"
                        src={item.FileName}
                        alt=""
                        onClick={handleClickImage(item)}
                    />
                </Frame>
            );
        },
        [handleClickImage, height, width]
    );

    return React.useMemo(() => <div id="banner-common">{data.map(renderBannerItem)}</div>, [data, renderBannerItem]);
}

export default memo(Banner);
