import { CommonCallback } from './common';

/**
 * MobileOrderItem002MobileOrderGetItemModel
 */
export interface MobileOrderItem002MobileOrderGetItemModel extends CommonCallback {
    ItemID: string;
    BrandID?: string;
    CheerID?: string;
}

/**
 * MobileOrderItem002Image
 */
export interface MobileOrderItem002Image {
    FileName: string;
    Main: number;
}

/**
 * MobileOrderItem002MobileOrderGetItemResult
 */
export interface MobileOrderItem002MobileOrderGetItemResult {
    ItemID: string;
    ItemName: string;
    Caption: string;
    Explanation: string;
    ItemImageList: MobileOrderItem002Image[];
    TaxPrice: number;
    AvailableStock: number;
    DeleteFlag: boolean;
    UpdateDateTime: string;
    Quantity: number;
    ItemCount: number;
    IsAdded: boolean;
    ItemStatus: string;
}
