import { CommonCallback } from './common';

/**
 * CommonGetMedalInfoModel
 */
export interface CommonGetMedalInfoModel extends CommonCallback {
    // メダルサービスID
    MedalServiceID: string;
}

/**
 * MedalGroupDetail
 */
export interface MedalGroupDetail {
    StoreGroupName: string;
    CurrencyUnit: string;
    ProductGroupImage: string;
    UsePriority: number;
    LinkedMedalAwardSID: string;
    LinkedMedalAwardGID: string;
    ProductGroupName: string;
    StoreGroupImage: string;
}

/**
 * CommonGetMedalInfoResult
 */
export interface CommonGetMedalInfoResult {
    // メダルサービスID
    MedalServiceID: string;
    // メダルサービス種別
    MedalServiceType: number;
    // メダルサービス名
    MedalServiceName: string;
    // メダルサービス画像
    MedalServiceImage?: string;
    // メインメダルフラグ
    MainMedalFlag?: boolean;
    // メダルグループ詳細
    MedalGroupDetails?: {
        [key: string]: MedalGroupDetail;
    };
    // 特典メダル配布量
    MedalAwardDistribution?: number;
    // 特典メダル付与率（%）
    MedalAwardGrantRate?: number;
    // 特典メダル最大付与額
    MedalAwardMaxGrant?: number;
    // 特典メダル配布量を超える配布
    MedalAwardDistributionOverFlag?: boolean;
    // 有効期限日数（なければ 180 日）
    ExpirationDateDays?: number;
    // 商品IDリスト
    ProductIDList?: string[];
    // 直接入力上限額
    MaximumPrice?: number;
    // チャージ限度額
    LimitAmount?: number;
    // メダルサービス開始日
    MedalServiceStartDate?: string;
    // メダルサービス終了日
    MedalServiceEndDate?: string;
    // メダルサービス表示フラグ
    MedalServiceEnableFlag?: boolean;
    // 商品取引レート
    ProductRate?: number;
    // 支払い開始日
    PaymentStartDate?: string;
    // 支払い終了日
    PaymentEndDate?: string;
    // チャージ開始日
    ChargeStartDate?: string;
    // チャージ終了日
    ChargeEndDate?: string;
    // 商品使用期限日
    ProductLimitDate?: string;
    // メダルサービスURL
    MedalServiceURL?: string;
    // メダルチャージ方法
    MedalChargeMethod?: string[];
    // 残高
    MedalServiceBalance?: string;
}
