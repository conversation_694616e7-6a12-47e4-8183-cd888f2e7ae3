import { CommonCallback } from './common';

/**
 * StampRally002InitGetStampRallyDetailModel
 */
export interface StampRally002InitGetStampRallyDetailModel extends CommonCallback {
    BrandID: string;
    CheerID?: string;
    StampRallyID: string;
}

/**
 * StampRally002InitGetStampRallyDetailResult
 */
export interface StampRally002InitGetStampRallyDetailResult {
    StampRallyID: string;
    StampRallyName: string;
    EndDateTime: string;
    StampRallyLatitude: number;
    StampRallyLongitude: number;
    StampRallyScale: number;
    OrderFlag: boolean;
    UrgingFlag: boolean;
    MissionCount: number;
    ClearMissionCount: number;
    DetailDisplayAutoFlag: boolean;
    StampRallyMissionList?: StampRally002MissionItem[];
}

/**
 * StampRally002MissionItem
 */
export interface StampRally002MissionItem {
    MissionID: string;
    RewardID: string;
    MissionName: string;
    MissionImage: string;
    Address: string;
    MissionLatitude: number;
    MissionLongitude: number;
    MissionGPSRange: number;
    MissionCategory: number;
    MissionRoute: number;
    ClearRule: number;
    CompleteDateTime: string;
    GetRewardDateTime: string;
    MarkerID?: string;
    IsCheckPoint?: boolean;
    IsBackgroundRedGoal?: boolean;
    MissionSort?: string;
    NeedClearMissionCount: number;
    IncentiveType?: number;
    BeforeMissionClearFlag?: boolean;
}
