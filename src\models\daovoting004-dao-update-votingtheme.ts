import { CommonCallback } from './common';

/**
 * DAOVoting004DaoUpdateVotingThemeModel
 */
export interface DAOVoting004DaoUpdateVotingThemeModel extends CommonCallback {
    // DAOID
    DaoID: string;
    // テーマID
    ThemeID: string;
    // タイトル
    Title: string;
    // 内容説明
    Explanation: string;
    // 投票開始日時
    VotingStartDateTime: string;
    // 投票終了日時
    VotingEndDateTime: string;
    // 投票成立数
    VotingSuccessCount: number;
    // 1票換算GT
    ConversionGT: number;
    // 投票形式コード
    VotingFormatCode: number;
    // 通常画像ファイル名
    NormalVotingImageFileName?: string;
    // 投票候補リスト
    VotingCandidateList?: DAOVoting004VotingCandidateItem[];
}

/**
 * DAOVoting004VotingCandidateItem
 */
export interface DAOVoting004VotingCandidateItem {
    // 投票候補内容
    CandidateContent: string;
    // 画像ファイル名
    ImageFileName?: string;
}
