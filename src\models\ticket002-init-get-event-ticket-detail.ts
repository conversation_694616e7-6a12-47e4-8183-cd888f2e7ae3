import { CommonCallback } from './common';

/**
 * Ticket002InitGetEventTicketDetailModel
 */
export interface Ticket002InitGetEventTicketDetailModel extends CommonCallback {
    UserEventTicketID: string;
}

/**
 * Ticket002InitGetEventTicketDetailResult
 */
export interface Ticket002InitGetEventTicketDetailResult {
    UserEventTicketID: string;
    EventTicketPurchaseDateTime: string;
    EventTicketID: string;
    EventTicketDetailID: string;
    EventTicketEntryTime: string;
    EventTicketExitTime: string;
    EventTicketPriceName: string;
    EventTicketPrice: number;
    EventTicketUsedFlag: boolean;
    EventTicketUsedDateTime: string;
    EventTicketTopImage: string;
    EventTicketName: string;
    EventTicketDateTime: string;
    EventTicketNotes: string;
    HPURL: string;
    EventTicketReserveID: string;
}
