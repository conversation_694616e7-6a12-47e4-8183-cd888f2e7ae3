import { CommonCallback } from './common';

/**
 * DAOVoting004DaoGetVotingDefaultInfoModel
 */
export interface DAOVoting004DaoGetVotingDefaultInfoModel extends CommonCallback {
    // DAOID
    DaoID: string;
}

/**
 * DAOVoting004VotingFormatItem
 */
export interface DAOVoting004VotingFormatItem {
    // 投票形式コード
    VotingFormatCode: number;
    // 投票形式名
    VotingFormatName: string;
}

/**
 * DAOVoting004DaoGetVotingDefaultInfoResult
 */
export interface DAOVoting004DaoGetVotingDefaultInfoResult {
    // 投票形式リスト
    VotingFormatList: DAOVoting004VotingFormatItem[];
    // 1票換算GT
    ConversionGT: number;
    // 投票成立数
    VotingSuccessCount: number;
    // ブロックチェーン利用フラグ
    BlockchainFlag: boolean;
}
