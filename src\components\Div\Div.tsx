import _ from 'lodash';
import { ReactNode, useEffect, useMemo, useRef, HTMLAttributes } from 'react';
import Utils from '../../utils/utils';

/**
 * DivProps
 */
interface DivProps extends Omit<HTMLAttributes<HTMLDivElement>, 'children'> {
    /**
     * children
     */
    children?: ReactNode;
    /**
     * onLayout
     * @param el Element
     * @returns void
     */
    onLayout?: (el: Element) => void;
}

/**
 * Div component
 * @param props DivProps
 * @returns React.JSX.Element
 */
const Div = (props: DivProps): React.JSX.Element => {
    const { children, onLayout, ...rest } = useMemo(() => props, [props]);
    const divRef = useRef<HTMLDivElement>(null);

    useEffect(() => {
        if (divRef?.current) {
            Utils.createAppObserver((entries) => {
                const { target } = _.last(entries) as ResizeObserverEntry;
                onLayout?.(target);
            }).observe(divRef.current);
        }
    }, [onLayout]);

    return useMemo(
        () => (
            <div ref={divRef} {...rest}>
                {children}
            </div>
        ),
        [children, rest]
    );
};

export default Div;
