import { CommonCallback } from './common';

/**
 * CommonDaoGetFunctionAuthorityModel
 */
export interface CommonDaoGetFunctionAuthorityModel extends CommonCallback {
    // DAOID
    DaoID: string;
    // 機能ID
    FunctionID: string[];
    // 機能種別
    FunctionType?: number;
}

/**
 * CommonDaoGetFunctionAuthorityResult
 */
export interface CommonDaoGetFunctionAuthorityResult extends CommonCallback {
    // 機能ごとの権限情報
    FunctionAuthority: { [key: string]: boolean };
}
