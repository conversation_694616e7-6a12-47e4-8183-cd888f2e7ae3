import API from '../constants/api';
import { BaseResponse } from '../models/common';
import {
    RocketsSurvey001InitGetSurveyListModel,
    RocketsSurvey001InitGetSurveyListResult,
} from '../models/rocketssurvey001-init-get-survey-list';
import { RocketsSurvey002MainSendAnswerModel } from '../models/rocketssurvey002-main-send-answer';
import {
    Survey002InitGetSurveyListModel,
    Survey002InitGetSurveyListResult,
} from '../models/survey002-init-get-survey-list';
import { Survey001InitGetSurveyItem } from '../models/survey001-init-get-survey-list';
import createAPI from './baseApi';
import { Survey003MainSendAnswerModel } from '../models/survey003-main-send-answer';

/**
 * SurveyAPI
 */
class SurveyAPI {
    /**
     * rocketsSurvey001InitGetSurveyList
     * @param data RocketsSurvey001InitGetSurveyListModel
     * @returns Promise<BaseResponse<RocketsSurvey001InitGetSurveyListResult>>
     */
    static rocketsSurvey001InitGetSurveyList = (
        data: RocketsSurvey001InitGetSurveyListModel
    ): Promise<BaseResponse<RocketsSurvey001InitGetSurveyListResult>> => {
        return createAPI<RocketsSurvey001InitGetSurveyListResult>({
            url: API.ROCKETS_SURVEY001_INIT_GET_SURVEY_LIST,
            data,
        });
    };

    /**
     * rocketsSurvey002MainSendAnswer
     * @param data RocketsSurvey002MainSendAnswerModel
     * @returns Promise<BaseResponse>
     */
    static rocketsSurvey002MainSendAnswer = (data: RocketsSurvey002MainSendAnswerModel): Promise<BaseResponse> => {
        return createAPI({
            url: API.ROCKETS_SURVEY002_MAIN_SEND_ANSWER,
            data,
        });
    };

    /**
     * survey002InitGetSurveyList
     * @param data Survey002InitGetSurveyListModel
     * @returns Promise<BaseResponse<Survey002InitGetSurveyListResult>>
     */
    static survey002InitGetSurveyList = (
        data: Survey002InitGetSurveyListModel
    ): Promise<BaseResponse<Survey002InitGetSurveyListResult>> => {
        return createAPI<Survey002InitGetSurveyListResult>({
            url: API.SURVEY002_INIT_GET_SURVEY_LIST,
            data,
        });
    };

    /**
     * survey001InitGetSurveyList
     * @returns Promise<BaseResponse<Survey001InitGetSurveyItem[]>>
     */
    static survey001InitGetSurveyList = (data: {
        useLock: boolean;
    }): Promise<BaseResponse<Survey001InitGetSurveyItem[]>> => {
        return createAPI<Survey001InitGetSurveyItem[]>({
            url: API.SURVEY001_INIT_GET_SURVEY_LIST,
            data,
        });
    };

    /**
     * survey003MainSendAnswer
     * @param data Survey003MainSendAnswerModel
     * @returns Promise<BaseResponse>
     */
    static survey003MainSendAnswer = (data: Survey003MainSendAnswerModel): Promise<BaseResponse> => {
        return createAPI({
            url: API.SURVEY003_MAIN_SEND_ANSWER,
            data,
        });
    };
}

export default SurveyAPI;
