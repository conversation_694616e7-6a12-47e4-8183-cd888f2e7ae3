import { CommonCallback } from './common';

/**
 * Home001InitGetCouponListModel
 */
export interface Home001InitGetCouponListModel extends CommonCallback {
    // 1ページあたりの取得アイテム数
    PageSize: number;
}

/**
 * Home001InitGetCouponListItem
 */
export interface Home001InitGetCouponListItem {
    // クーポンID
    PresentationCouponID: string;
    // タイトル
    Title: string;
    // 画像
    Image: string;
    // 加盟店名
    StoreName: string;
    // 有効期限
    EndDateTime: string;
}

/**
 * Home001InitGetCouponListResult
 */
export interface Home001InitGetCouponListResult {
    // クーポン一覧
    CouponList: Home001InitGetCouponListItem[];
}
