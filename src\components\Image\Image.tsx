/* eslint-disable max-depth */
/* eslint-disable no-await-in-loop */
import React, { HTMLAttributes, memo, useCallback, useEffect, useMemo, useRef } from 'react';
import Utils from '../../utils/utils';
import { __DEV__ } from '../../utils/common';
import { Typography } from '@mui/material';
import './styles.scss';
/**
 * ImageProps
 */
interface ImageProps extends HTMLAttributes<HTMLImageElement>, React.ImgHTMLAttributes<HTMLImageElement> {
    /**
     * src
     * image's src
     */
    src?: string;
    /**
     * numberOfRetries
     */
    numberOfRetries?: number;
    /**
     * width
     */
    width?: number;
    /**
     * height
     */
    height?: number;
    /**
     * delay
     */
    delay?: number;
    /**
     * onClick
     */
    onClick?: () => void;
    /**
     * src
     * image's src
     */
    classNameCustom?: string;
}

/**
 * Image
 * @param props ImageProps
 * @returns React.JSX.Element
 */
const Image = (props: ImageProps): React.JSX.Element => {
    // number of retries. 5 for default
    const numberOfRetries = useMemo(() => props.numberOfRetries || 5, [props.numberOfRetries]);

    // delay. 1s for default
    const delay = useMemo(() => props.delay || 1000, [props.delay]);

    // image ref. Image will replace this if load success
    const imageRef = useRef<HTMLImageElement>(null);

    /**
     * handleLoadImage
     */
    const handleLoadImage = useCallback(async () => {
        const { src, style, className, width, height, alt, onClick } = props;
        // prevent call re-try if src is null/empty/undefined
        if (!src) return;
        for (let i = 0; i < numberOfRetries; i++) {
            if (__DEV__) {
                console.log('Retry ', i, ' Url: ', String(src).slice(0, 50));
            }
            // fetch image
            const originImage = await Utils.loadImage(src || '');

            // if fetch done
            if (originImage) {
                // set className if available
                className && (originImage.className = className + (onClick ? ' cursor-pointer' : ''));
                // set alt if available
                alt && (originImage.alt = alt);
                // set width if available
                width && (originImage.width = width);
                // set height if available
                height && (originImage.height = height);
                // set event onclick
                onClick && (originImage.onclick = onClick);
                // set style if available
                if (style) {
                    for (const [key, value] of Object.entries(style)) {
                        if (value) {
                            const type = typeof value;
                            if (['string', 'number'].includes(type)) {
                                // eslint-disable-next-line @typescript-eslint/no-explicit-any
                                (originImage.style as any)[key] = type === 'string' ? value : `${value}px`;
                            }
                        }
                    }
                }
                // replace img with originImage
                imageRef?.current?.replaceWith(originImage);
                break;
            }
            // wait for `props.delay` before retry
            await Utils.sleep(delay);
        }
    }, [delay, numberOfRetries, props]);

    useEffect(() => {
        handleLoadImage();
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);

    return useMemo(
        () =>
            props.src ? (
                <img ref={imageRef} {...props} />
            ) : (
                <div
                    className={
                        props.classNameCustom === 'banner-without-image-container'
                            ? 'banner-image-convert-container'
                            : ''
                    }
                >
                    <Typography
                        fontSize={12}
                        color={'#848484'}
                        fontFamily={'"Noto Sans JP", "Meiryo", "メイリオ", "Arial", "Helvetica", sans-serif'}
                        fontWeight={400}
                        lineHeight={1.5}
                        className={props.classNameCustom}
                    >
                        No Image
                    </Typography>
                </div>
            ),
        [props]
    );
};
export default memo(Image);
