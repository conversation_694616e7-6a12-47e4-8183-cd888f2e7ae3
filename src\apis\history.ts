import API from '../constants/api';
import { BaseResponse } from '../models/common';
import {
    History001InitGetHistoryListModel,
    History001InitGetHistoryListResult,
} from '../models/history001-init-get-history-list';
import createAPI from './baseApi';

/**
 * HistoryAPI
 */
class HistoryAPI {
    /**
     * history001InitGetHistoryList
     * @param data History001InitGetHistoryListModel
     * @returns  Promise<BaseResponse<History001InitGetHistoryListResult>>
     */
    static history001InitGetHistoryList = (
        data: History001InitGetHistoryListModel
    ): Promise<BaseResponse<History001InitGetHistoryListResult>> => {
        return createAPI<History001InitGetHistoryListResult>({
            url: API.HISTORY001_INIT_GET_HISTORY_LIST,
            data,
        });
    };
}

export default HistoryAPI;
