import API from '../constants/api';
import { BaseResponse } from '../models/common';
import {
    CommonDaoGetFunctionAuthorityModel,
    CommonDaoGetFunctionAuthorityResult,
} from '../models/common-dao-get-functionauthority';
import {
    CommonDaoGetParticipantStatusModel,
    CommonDaoGetParticipantStatusResult,
} from '../models/common-dao-get-participantstatus';
import { CommonDaoGetUrlModel, CommonDaoGetUrlResult } from '../models/common-dao-get-url';
import { DAOCommunity003DaoPutCategoryModel } from '../models/daocommunity003-dao-put-category';
import {
    DAOCommunity008DaoGetReactionImageModel,
    DAOCommunity008DaoGetReactionImageResult,
} from '../models/daocommunity008-dao-get-reactionimage';
import { DAOTop001DaoGetHotChatModel, DAOTop001DaoGetHotChatResult } from '../models/daotop001-dao-get-hotchat';
import {
    DAOTop001DaoGetParticipantStatusModel,
    DAOTop001DaoGetParticipantStatusResult,
} from '../models/daotop001-dao-get-participantstatus';
import {
    DAOVoting001DaoGetVotingThemeListModel,
    DAOVoting001DaoGetVotingThemeListResult,
} from '../models/daovoting001-dao-get-votingthemelist';
import {
    DAOVoting002DaoGetVotingCheerInfoModel,
    DAOVoting002DaoGetVotingCheerInfoResult,
} from '../models/daovoting002-dao-get-votingcheerinfo';
import {
    DAOVoting002DaoGetVotingInfoModel,
    DAOVoting002DaoGetVotingInfoResult,
} from '../models/daovoting002-dao-get-votinginfo';
import {
    DAOVoting002DaoGetVotingRightModel,
    DAOVoting002DaoGetVotingRightResult,
} from '../models/daovoting002-dao-get-votingright';
import {
    DAOVoting002DaoGetVotingStatusInfoModel,
    DAOVoting002DaoGetVotingStatusInfoResult,
} from '../models/daovoting002-dao-get-votingstatusinfo';
import { DAOVoting002DaoUpdateVotingModel } from '../models/daovoting002-dao-update-voting';
import { DAOVoting004DaoDeleteVotingThemeModel } from '../models/daovoting004-dao-delete-votingtheme';
import {
    DAOVoting004DaoGetVotingDefaultInfoModel,
    DAOVoting004DaoGetVotingDefaultInfoResult,
} from '../models/daovoting004-dao-get-votingdefaultinfo';
import {
    DAOVoting004DaoGetVotingThemeModel,
    DAOVoting004DaoGetVotingThemeResult,
} from '../models/daovoting004-dao-get-votingtheme';
import { DAOVoting004DaoPutVotingThemeModel } from '../models/daovoting004-dao-put-votingtheme';
import { DAOVoting004DaoUpdateVotingThemeModel } from '../models/daovoting004-dao-update-votingtheme';
import {
    IdeaDao007InitGetMembersListModel,
    IdeaDao007InitGetMembersListResult,
} from '../models/ideadao007-init-get-members-list';
import createAPI from './baseApi';

class DaoApi {
    /**
     * daoVoting001DaoGetVotingThemeList
     * @param data DAOVoting001DaoGetVotingThemeListModel
     * @returns Promise<BaseResponse<DAOVoting001DaoGetVotingThemeListResult>>
     */
    static daoVoting001DaoGetVotingThemeList = (
        data: DAOVoting001DaoGetVotingThemeListModel
    ): Promise<BaseResponse<DAOVoting001DaoGetVotingThemeListResult>> => {
        return createAPI<DAOVoting001DaoGetVotingThemeListResult>({
            url: API.DAOVOTING001_DAO_GET_VOTINGTHEMELIST,
            data,
        });
    };

    /**
     * commonDaoGetFunctionAuthority
     * @param data CommonDaoGetFunctionAuthorityModel
     * @returns Promise<BaseResponse<CommonDaoGetFunctionAuthorityResult>>
     */
    static commonDaoGetFunctionAuthority = (
        data: CommonDaoGetFunctionAuthorityModel
    ): Promise<BaseResponse<CommonDaoGetFunctionAuthorityResult>> => {
        return createAPI<CommonDaoGetFunctionAuthorityResult>({
            url: API.COMMON_DAO_GET_FUNCTIONAUTHORITY,
            data,
        });
    };

    /**
     * commonDaoGetUrl
     * @param data CommonDaoGetUrlModel
     * @returns Promise<BaseResponse<CommonDaoGetUrlResult>>
     */
    static commonDaoGetUrl = (data: CommonDaoGetUrlModel): Promise<BaseResponse<CommonDaoGetUrlResult>> => {
        return createAPI<CommonDaoGetUrlResult>({
            url: API.COMMON_DAO_GET_URL,
            data,
        });
    };

    /**
     * commonDaoGetParticipantStatus
     * @param data CommonDaoGetParticipantStatusModel
     * @returns Promise<BaseResponse<CommonDaoGetParticipantStatusResult>>
     */
    static commonDaoGetParticipantStatus = (
        data: CommonDaoGetParticipantStatusModel
    ): Promise<BaseResponse<CommonDaoGetParticipantStatusResult>> => {
        return createAPI<CommonDaoGetParticipantStatusResult>({
            url: API.COMMON_DAO_GET_PARTICIPANTSTATUS,
            data,
        });
    };

    /**
     * daoVoting002DaoGetVotingInfo
     * @param data DAOVoting002DaoGetVotingInfoModel
     * @returns Promise<BaseResponse<DAOVoting002DaoGetVotingInfoResult>>
     */
    static daoVoting002DaoGetVotingInfo = (
        data: DAOVoting002DaoGetVotingInfoModel
    ): Promise<BaseResponse<DAOVoting002DaoGetVotingInfoResult>> => {
        return createAPI<DAOVoting002DaoGetVotingInfoResult>({
            url: API.DAOVOTING002_DAO_GET_VOTINGINFO,
            data,
        });
    };

    /**
     * daoVoting002DaoGetVotingRight
     * @param data DAOVoting002DaoGetVotingRightModel
     * @returns Promise<BaseResponse<DAOVoting002DaoGetVotingRightResult>>
     */
    static daoVoting002DaoGetVotingRight = (
        data: DAOVoting002DaoGetVotingRightModel
    ): Promise<BaseResponse<DAOVoting002DaoGetVotingRightResult>> => {
        return createAPI<DAOVoting002DaoGetVotingRightResult>({
            url: API.DAOVOTING002_DAO_GET_VOTINGRIGHT,
            data,
        });
    };

    /**
     * daoVoting002DaoGetVotingStatusInfo
     * @param data DAOVoting002DaoGetVotingStatusInfoModel
     * @returns Promise<BaseResponse<DAOVoting002DaoGetVotingStatusInfoResult>>
     */
    static daoVoting002DaoGetVotingStatusInfo = (
        data: DAOVoting002DaoGetVotingStatusInfoModel
    ): Promise<BaseResponse<DAOVoting002DaoGetVotingStatusInfoResult>> => {
        return createAPI<DAOVoting002DaoGetVotingStatusInfoResult>({
            url: API.DAOVOTING002_DAO_GET_VOTINGSTATUSINFO,
            data,
        });
    };

    /**
     * daoVoting002DaoGetVotingCheerInfo
     * @param data DAOVoting002DaoGetVotingCheerInfoModel
     * @returns Promise<BaseResponse<DAOVoting002DaoGetVotingCheerInfoResult>>
     */
    static daoVoting002DaoGetVotingCheerInfo = (
        data: DAOVoting002DaoGetVotingCheerInfoModel
    ): Promise<BaseResponse<DAOVoting002DaoGetVotingCheerInfoResult>> => {
        return createAPI<DAOVoting002DaoGetVotingCheerInfoResult>({
            url: API.DAOVOTING002_DAO_GET_VOTINGCHEERINFO,
            data,
        });
    };

    /**
     * daoVoting002DaoUpdateVoting
     * @param data DAOVoting002DaoUpdateVotingModel
     * @returns Promise<BaseResponse>
     */
    static daoVoting002DaoUpdateVoting = (data: DAOVoting002DaoUpdateVotingModel): Promise<BaseResponse> => {
        return createAPI({
            url: API.DAOVOTING002_DAO_UPDATE_VOTING,
            data,
        });
    };

    /**
     * daoVoting004DaoGetVotingTheme
     * @param data DAOVoting004DaoGetVotingThemeModel
     * @returns Promise<BaseResponse<DAOVoting004DaoGetVotingThemeResult>>
     */
    static daoVoting004DaoGetVotingTheme = (
        data: DAOVoting004DaoGetVotingThemeModel
    ): Promise<BaseResponse<DAOVoting004DaoGetVotingThemeResult>> => {
        return createAPI<DAOVoting004DaoGetVotingThemeResult>({
            url: API.DAO_VOTING004_DAO_GET_VOTINGTHEME,
            data,
        });
    };

    /**
     * daoVoting004DaoGetVotingDefaultInfo
     * @param data DAOVoting004DaoGetVotingDefaultInfoModel
     * @returns Promise<BaseResponse<DAOVoting004DaoGetVotingDefaultInfoResult>>
     */
    static daoVoting004DaoGetVotingDefaultInfo = (
        data: DAOVoting004DaoGetVotingDefaultInfoModel
    ): Promise<BaseResponse<DAOVoting004DaoGetVotingDefaultInfoResult>> => {
        return createAPI<DAOVoting004DaoGetVotingDefaultInfoResult>({
            url: API.DAO_VOTING004_DAO_GET_VOTINGDEFAULTINFO,
            data,
        });
    };

    /**
     * daoVoting004DaoPutVotingTheme
     * @param data DAOVoting004DaoPutVotingThemeModel
     * @returns Promise<BaseResponse<DAOVoting004DaoPutVotingThemeResult>>
     */
    static daoVoting004DaoPutVotingTheme = (
        data: DAOVoting004DaoPutVotingThemeModel
    ): Promise<BaseResponse<DAOVoting004DaoGetVotingThemeResult>> => {
        return createAPI<DAOVoting004DaoGetVotingThemeResult>({
            url: API.DAO_VOTING004_DAO_PUT_VOTINGTHEME,
            data,
        });
    };

    /**
     * daoVoting004DaoUpdateVotingTheme
     * @param data DAOVoting004DaoupdateVotingThemeModel
     * @returns Promise<BaseResponse<DAOVoting004DaoupdateVotingThemeResult>>
     */
    static daoVoting004DaoUpdateVotingTheme = (
        data: DAOVoting004DaoUpdateVotingThemeModel
    ): Promise<BaseResponse<DAOVoting004DaoGetVotingThemeResult>> => {
        return createAPI<DAOVoting004DaoGetVotingThemeResult>({
            url: API.DAO_VOTING004_DAO_UPDATE_VOTINGTHEME,
            data,
        });
    };

    /**
     * daoVoting004DaoDeleteVotingTheme
     * @param data DAOVoting004DaoDeleteVotingThemeModel
     * @returns Promise<BaseResponse>
     */
    static daoVoting004DaoDeleteVotingTheme = (data: DAOVoting004DaoDeleteVotingThemeModel): Promise<BaseResponse> => {
        return createAPI<BaseResponse>({
            url: API.DAO_VOTING004_DAO_DELETE_VOTINGTHEME,
            data,
        });
    };

    /**
     * daoCommunity003DaoPutCategory
     * @param data DAOCommunity003DaoPutCategoryModel
     * @returns Promise<BaseResponse<BaseResponse>
     */
    static daoCommunity003DaoPutCategory = (data: DAOCommunity003DaoPutCategoryModel): Promise<BaseResponse> => {
        return createAPI<DAOCommunity003DaoPutCategoryModel>({
            url: API.DAO_COMMUNITY003_DAO_PUT_CATEGORY,
            data,
        });
    };

    /**
     * daoTop001DaoGetHotChat
     * @param data DAOTop001DaoGetHotChatModel
     * @returns Promise<BaseResponse<DAOTop001DaoGetHotChatResult>>
     */
    static daoTop001DaoGetHotChat = (
        data: DAOTop001DaoGetHotChatModel
    ): Promise<BaseResponse<DAOTop001DaoGetHotChatResult>> => {
        return createAPI<DAOTop001DaoGetHotChatResult>({
            url: API.DAOTOP001_DAO_GET_HOTCHAT,
            data,
        });
    };

    /**
     * daoTop001DaoGetParticipantStatus
     * @param data DAOTop001DaoGetParticipantStatusModel
     * @returns Promise<BaseResponse<DAOTop001DaoGetParticipantStatusResult>>
     */
    static daoTop001DaoGetParticipantStatus = (
        data: DAOTop001DaoGetParticipantStatusModel
    ): Promise<BaseResponse<DAOTop001DaoGetParticipantStatusResult>> => {
        return createAPI<DAOTop001DaoGetParticipantStatusResult>({
            url: API.DAOTOP001_DAO_GET_PARTICIPANTSTATUS,
            data,
        });
    };

    /**
     * daoCommunity008DaoGetReactionImage
     * @param data DAOCommunity008DaoGetReactionImageModel
     * @returns Promise<BaseResponse<DAOCommunity008DaoGetReactionImageResult>>
     */
    static daoCommunity008DaoGetReactionImage = (
        data: DAOCommunity008DaoGetReactionImageModel
    ): Promise<BaseResponse<DAOCommunity008DaoGetReactionImageResult>> => {
        return createAPI<DAOCommunity008DaoGetReactionImageResult>({
            url: API.DAOCOMMUNITY008_DAO_GET_REACTIONIMAGE,
            data,
        });
    };

    /**
     * ideadao007InitGetMembersList
     * @param data IdeaDao007InitGetMembersListModel
     * @returns Promise<BaseResponse<IdeaDao007InitGetMembersListResult>>
     */
    static ideadao007InitGetMembersList = (
        data: IdeaDao007InitGetMembersListModel
    ): Promise<BaseResponse<IdeaDao007InitGetMembersListResult>> => {
        return createAPI<IdeaDao007InitGetMembersListResult>({
            url: API.IDEADAO007_INIT_GET_MEMBERS_LIST,
            data,
        });
    };
}

export default DaoApi;
