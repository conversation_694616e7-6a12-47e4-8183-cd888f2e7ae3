import { CommonCallback } from './common';

/**
 * Nft003InitGetNftDetailModel
 */
export interface Nft003InitGetNftDetailModel extends CommonCallback {
    // NFTコンテンツID
    NFTContentID: string;
    BrandID?: string;
    CheerID?: string;
}

/**
 * Nft003InitGetNftDetailResult
 */
export interface Nft003InitGetNftDetailResult {
    // コンテンツ名
    ContentName: string;
    // コンテンツ画像
    ContentImage: string;
    // コンテンツ音声
    ContentSound: string;
    // コンテンツ動画
    ContentMovie: string;
    // コンテンツ画像サンプル
    ContentImageSample: string;
    // コンテンツ音声サンプル
    ContentSoundSample: string;
    // コンテンツ動画サンプル
    ContentMovieSample: string;
    // コンテンツ価格
    ContentPrice: number;
    // 総数
    Total: number;
    // 在庫数
    Stock: number;
    // コンテンツタグ
    ContentTag: string[];
    // カードID
    NFTCardID: string;
    // カード情報
    NFTCardInfo: string;
    // コンテンツ説明
    ContentDetail: string;
    // 発行者
    Publisher: string;
    // 販売開始日
    StartDate: string;
    // 販売終了日
    EndDate: string;
    // 購入上限
    Maximum: number;
    // いいね数
    LikeCount: number;
    // 表示フラグ
    DisplayFlg: boolean;
    // 加盟店ID
    StoreID: string;
    // QRコード認証必要フラグ
    QRCodeAuthenticationRequiredFlg: boolean;
    // 位置情報認証必要フラグ
    GPSAuthenticationRequiredFlg: boolean;
    // 決済ボイス使用フラグ
    PaymentVoiceUsedFlg: boolean;
    // クーポンの種類
    TicketTypeFlg: number;
    // クーポンID
    TicketProductID: string;
    // クーポンの名前
    TicketName?: string;
    // クーポン画像
    TicketImage: string;
    // クーポンの説明
    TicketInfo: string;
    // いいね押下フラグ
    LikeFlg: boolean;
    // NFT販売店舗名
    StoreName: string;
    // NFT販売店舗住所の都道府県
    StorePrefectures: string;
    // NFT販売店舗住所の市区町村
    StoreMunicipalities: string;
    // NFT販売店舗住所の町域
    StoreTownArea: string;
    // NFT販売店舗住所の番地
    StoreHouseNumber: string;
    // NFT販売店舗住所の建物名
    StoreBuildingName: string;
    // クーポンの対象店舗名
    CouponStoreName: string;
    // Xへの投稿メッセージ
    XPostMessage: string;
    // Xへの投稿ハッシュタグ
    XHashTag: string;
    // custom
    PaymentType?: string;
    // custom
    NFTContentID?: string;
}
