.charge-int-bnk-pay005 {
    height: calc(100vh - 50px);
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    .color-ccc {
        color: #cccccc;
    }

    .text-charge {
        margin-top: 21px;
        margin-bottom: 20px;
        flex-direction: column;
    }
    .hr-style {
        margin: 0px 16px 16px 16px;
    }
    .charging-input {
        margin: 0px 16px;

        input {
            font-size: 20px;
            color: #707070;
            font-family: 'Robot<PERSON>', Meiryo, メイリオ, Arial, Helvetica, sans-serif !important;
            padding-right: 4px !important;
        }

        .common-component.common-label-container .label {
            font-family: 'Noto Sans JP', Meiryo, メイリオ, Arial, Helvetica, sans-serif;
        }
        .common-component.common-label-container .required .label {
            font-family: "Noto Sans JP Bold", Meiryo, メイリオ, Arial, Helvetica, sans-serif;
        }

        .common-input-component {
            padding-bottom: 8px;

            .label-session {
                margin-bottom: 16px;

                .sub-label {
                    font-size: 14px !important;
                    color: #222222;
                    margin-top: 5px;
                }
            }
        }

        .input-suffix {
            padding: 10px 16px 10px 0;
            font-size: 16px;
            font-family: "Noto Sans JP Bold", <PERSON><PERSON>, メイリオ, Arial, Helvetica, sans-serif;
            color: #222222;
        }

        .charge-limit {
            font-size: 11px;
            color: #222222;
            padding: 0 8px;
            font-weight: bold;
        }

        .button-charge {
            font-family: 'Noto Sans JP', Meiryo, メイリオ, Arial, Helvetica, sans-serif !important;
        }
        .option-value {
            margin: 0px;
            display: flex;
            flex-wrap: wrap;
            gap: 8px 6px;
            .option-value-input {
                font-family: 'Roboto', Meiryo, メイリオ, Arial, Helvetica, sans-serif !important;
                width: 100%;
                background-color: #ffffff;
                border: solid 2px #cccccc;
                color: #222222;
                font-weight: bold !important;
            }
            .option-value-detail {
                padding: 0px;
                width: calc((100% - 8px) / 2);
            }
        }
    }
    .btn-container {
        gap: 23px;
    }
}

.icon-charge-int-bank-pay005-fail {
    position: absolute;
    top: -36px;
    left: 50%;
    transform: translate(-50%, 0);
}