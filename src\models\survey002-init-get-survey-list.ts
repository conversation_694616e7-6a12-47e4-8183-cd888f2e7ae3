import { CommonCallback } from './common';

/**
 * Survey002InitGetSurveyListModel
 */
export interface Survey002InitGetSurveyListModel extends CommonCallback {
    // アンケートID
    SurveyID: string;
}

/**
 * Survey002MedalDistributionList
 */
export interface Survey002MedalDistributionList {
    // メダルサービスID
    MedalServiceID: string;
    // メダルサービス名
    MedalServiceName: string;
    // メダル配布量
    MedalAmount: string;
}

/**
 * Survey002InitGetSurveyAnswerItem
 */
export interface Survey002InitGetSurveyAnswerItem {
    // 選択回答ID
    ChoiceID: string;
    // 選択回答内容
    Text: string;
    // 自由記述回答フラグ
    FreeInputFlag: boolean;
}

/**
 * Survey002InitGetSurveyQuestionItem
 */
export interface Survey002InitGetSurveyQuestionItem {
    // 質問ID
    QuestionID: string;
    // 質問タイプ
    QuestionType: string;
    // 必須フラグ
    RequireFlag: boolean;
    // 質問文
    Text: string;
    // アンケート選択回答内容
    AnswerList: Survey002InitGetSurveyAnswerItem[];
}

/**
 * Survey002InitGetSurveyListResult
 */
export interface Survey002InitGetSurveyListResult {
    // アンケートID
    SurveyID: string;
    // タイトル
    SurveyTitle: string;
    // 説明
    SurveyDetail: string;
    // 開始日時
    StartDateTime: string;
    // 終了日時
    EndDateTime: string;
    // アンケート質問内容
    QuestionList: Survey002InitGetSurveyQuestionItem[];
    // メダル配布内容
    MedalDistributionList: Survey002MedalDistributionList;
    // メダル配布フラグ
    MedalDistributionFlag: boolean;
}
