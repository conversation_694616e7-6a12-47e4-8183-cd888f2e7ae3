import { CommonCallback } from './common';

/**
 * AreaWalletCouponItem
 */
export interface AreaWalletCouponItem {
    AreaCouponID: number;
    AreaCouponName: string;
    Description: string;
    GrantWay: number;
    GrantAmount: number;
    FileName?: string;
    GrantDateAreaCouponID?: number;
    AreaName?: string[];
    ValidDayCount?: number;
    AutoUseFlag?: boolean;
}

/**
 * AreaWalletCouponModel
 */
export interface AreaWalletCouponModel extends CommonCallback {
    // ブランドID
    BrandID: string;
    // ユーザID
    CheerID: number;
    // 処理タイプ 0：入金、1：決済
    ProcessType: number;
    // 地域ID; ProcessTypeが1の場合は必須
    AreaID?: number;
    // 店舗ID; ProcessTypeが1の場合は必須
    StoreID?: number;
}
