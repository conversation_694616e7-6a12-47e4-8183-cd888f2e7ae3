import React, { memo, useCallback, useEffect, useMemo, useState } from 'react';
import Utils from '../../../../utils/utils';
import Typography from '../../../../components/Typography';
import './styles.scss';
import useWindows from '../../../../hooks/windows';
import { BankNameItem } from '../../../../models/chargeintbnkpay002-init-get-bankname';
import ChargeAPI from '../../../../apis/charge';
import API from '../../../../constants/api';
import CommonUtils, { __DEV__ } from '../../../../utils/common';
import { Home001InitGetCoinItem } from '../../../../models/home001-init-get-coin';
import useNavigate from '../../../../hooks/navigate';
import Screens from '../../../../constants/screens';

/**
 * ChargeIntBnkPay002Props
 */
interface ChargeIntBnkPay002Props {
    handleGetStatusRegisterBankAccount: (isStatus?: number) => void;
    medalServiceItem: Home001InitGetCoinItem;
}
/**
 * ChargeIntBnkPay002
 * ID: CHARGEINTBNKPAY002
 * Name: BankPay決済 (口座選択)
 * 登録する口座を選択する画面
 * @returns React.JSX.Element
 */
const ChargeIntBnkPay002 = (props: ChargeIntBnkPay002Props): React.JSX.Element => {
    const { windowSize } = useWindows();
    const [bankListData, setBankListData] = useState<{ [key: string]: BankNameItem[] }>({});
    const navigate = useNavigate();

    /**
     * handleClickBankName
     * @param item BankNameItem
     */
    const handleClickBankName = useCallback(
        (item: BankNameItem) => async () => {
            navigate(Screens.CHARGEINT_BNKPAY009, {
                keepStateInStorage: true,
                state: {
                    BankCode: item?.BankCode,
                    BankName: item?.BankName,
                },
            });
        },
        [navigate]
    );

    /**
     * renderBankItem
     * @param item BankNameItem
     */
    const renderBankItem = useCallback(
        (item: BankNameItem) => {
            return (
                <div className="bank-name" onClick={handleClickBankName(item)}>
                    {item?.BankName}
                </div>
            );
        },
        [handleClickBankName]
    );

    /**
     * renderBankList
     * @param key string
     */
    const renderBankList = useCallback(
        (key: string) => {
            return (
                <div className="w-100">
                    <div className="wrapper-bank-key pl-32">
                        {key}
                        {Utils.t('charge.bnk_pay.bnk_pay002.char_bank_name')}
                    </div>
                    <div className="wrapper-bank-item">{bankListData[key]?.map(renderBankItem)}</div>
                </div>
            );
        },
        [bankListData, renderBankItem]
    );

    /**
     * handleGetBankList
     */
    const handleGetBankList = useCallback(async () => {
        try {
            const {
                isSuccess,
                result,
                status,
                Message = 'api.common.unknown_error',
            } = await ChargeAPI.chargeIntBnkPay002InitGetBankName({
                useLock: true,
            });
            if (status === API.STATUS_CODE.SUCCESS && isSuccess && result) {
                setBankListData(result?.BankNameList);
            } else {
                CommonUtils.showMessage({
                    message: Utils.t(Message || 'api.common.unknown_error'),
                    type: 'ERROR',
                });
            }
        } catch (error) {
            __DEV__ && console.log('error', error);
        }
    }, []);

    useEffect(() => {
        handleGetBankList();
    }, [handleGetBankList]);

    return useMemo(
        () => (
            <div
                className="wrapper-charge-int-bnk-pay002"
                style={{
                    height: windowSize.height - 53,
                }}
            >
                <div className="wrapper-register-info pt-24">
                    <div className="text-center mb-16">
                        <Typography className="ff-noto-bold">
                            {Utils.t('charge.bnk_pay.bnk_pay002.register_info')}
                        </Typography>
                    </div>

                    {Object.keys(bankListData).map(renderBankList)}
                </div>
            </div>
        ),
        [bankListData, renderBankList, windowSize?.height]
    );
};

export default memo(ChargeIntBnkPay002);
