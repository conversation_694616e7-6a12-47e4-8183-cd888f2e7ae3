import { CommonCallback } from './common';

/**
 * CommonWalletGetPaymentModel
 */
export interface CommonWalletGetPaymentModel extends CommonCallback {
    // ユーザID
    BrandID?: string;
    // 応援者ID
    CheerID?: string;
    StoreID: string;
    PaymentType: string;
    SettlementAgency: string;
    SaleType: string;
    ModeType: number;
}

export interface CommonWalletGetPaymentResult {
    // OrderID
    OrderID: string;
    // Status
    Status: string;
    // PaymentKey
    PaymentKey: string;
    // ResultCode
    ResultCode: string;
    // Message
    Message: string;
    // PopClientKey
    PopClientKey: string;
}
