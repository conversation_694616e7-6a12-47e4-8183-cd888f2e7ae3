import { CommonCallback } from './common';

/**
 * Store001MainGetStoreQueryModel
 */
export interface Store001MainGetStoreQueryModel extends CommonCallback {
    PageSize: number;
    ExclusiveStartKey?: string;
    MedalServiceID?: string;
    StoreCategoryIDs?: string[];
    AreaID?: string;
    Keyword?: string;
    BrandID: string;
}

export interface StoreListType {
    StoreID: string;
    StoreImage: string;
    StoreName: string;
    StoreCategory: string;
    StoreInformation: string;
}

/**
 * Store001MainGetStoreQueryResult
 */
export interface Store001MainGetStoreQueryResult {
    StoreList: StoreListType[];
    LastEvaluatedKey?: string;
}
