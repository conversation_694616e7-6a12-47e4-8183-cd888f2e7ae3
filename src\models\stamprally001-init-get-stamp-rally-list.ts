import { CommonCallback } from './common';

/**
 * StampRally001InitGetStampRallyListModel
 */
export interface StampRally001InitGetStampRallyListModel extends CommonCallback {
    BrandID: string;
    CheerID?: string;
    PageSize: number;
    ExclusiveStartKey?: { StampRallyID?: string; StartDateTime?: string };
    HeldStatus?: number;
}

/**
 * StampRally001ListType
 */
export interface StampRally001ListType {
    StampRallyID: string;
    RewardID: string;
    StampRallyImage: string;
    StampRallyName: string;
    StartDateTime: string;
    EndDateTime: string;
    CompleteStatus: boolean;
}

/**
 * StampRally001InitGetStampRallyListResult
 */
export interface StampRally001InitGetStampRallyListResult {
    StampRallyList: StampRally001ListType[];
    LastEvaluatedKey?: { StampRallyID?: string; StartDateTime?: string };
}
