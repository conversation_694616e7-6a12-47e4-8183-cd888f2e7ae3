import API from '../constants/api';
import { BaseResponse } from '../models/common';
import {
    EventForm002InitGetEventFormDetailModel,
    EventForm002InitGetEventFormDetailResult,
} from '../models/eventform002-init-get-eventform-detail';
import createAPI from './baseApi';

/**
 * EventFormAPI
 */
class EventFormAPI {
    /**
     * eventForm002InitGetEventFormDetail
     * @param data EventForm002InitGetEventFormDetailModel
     * @returns Promise<BaseResponse<EventForm002InitGetEventFormDetailResult>>
     */
    static eventForm002InitGetEventFormDetail = (
        data: EventForm002InitGetEventFormDetailModel
    ): Promise<BaseResponse<EventForm002InitGetEventFormDetailResult>> => {
        return createAPI<EventForm002InitGetEventFormDetailResult>({
            url: API.EVENTFORM002_INIT_GET_EVENTFORM_DETAIL,
            data: data,
        });
    };
}

export default EventFormAPI;
