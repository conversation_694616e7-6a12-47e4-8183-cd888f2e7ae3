import { CommonCallback } from './common';

/**
 * Wallet001InitGetCoinModel
 */
export type Wallet001InitGetCoinModel = CommonCallback;

/**
 * Wallet001InitGetCoinMedalGroupDetailsItem
 */
export interface Wallet001InitGetCoinMedalGroupDetailsItem {
    // 商品グループ名
    ProductGroupName: string;
    // 商品グループ画像
    ProductGroupImage: string;
    // 加盟店グループ名
    StoreGroupName: string;
    // 加盟店グループ画像
    StoreGroupImage: string;
    // 利用優先順位
    UsePriority: number;
    // メダル名リスト
    MedalNameList: string[];
}

/**
 * Wallet001InitGetCoinItem
 */
export interface Wallet001InitGetCoinItem {
    // メダルサービスID
    MedalServiceID: string;
    // メダルサービス種別(1:コイン 2:商品券)
    MedalServiceType: number;
    // メダルサービス名
    MedalServiceName: string;
    // メダルサービス画像
    MedalServiceImage: string;
    // メダルサービスURL
    MedalServiceURL: string;
    // メダルサービス開始日
    MedalServiceStartDate: string;
    // メダルサービス終了日
    MedalServiceEndDate: string;
    // 支払い開始日
    PaymentStartDate: string;
    // 支払い終了日
    PaymentEndDate: string;
    // チャージ開始日
    ChargeStartDate: string;
    // チャージ終了日
    ChargeEndDate: string;
    // 支払いボタンの有効フラグ
    PaymentButtonEnableFlag: boolean;
    // チャージボタンの有効フラグ
    ChargeButtonEnableFlag: boolean;
    // 当選口数(商品券購入最大口数)
    WinningCount?: number;
    // 購入済ステータス(0:未購入)
    PurchaseStatus?: number;
    // 商品ID
    ProductID?: string;
    // 商品金額
    ProductPrice?: number;
    // 商品取引レート
    ProductRate?: number;
    // メダルサービス残高
    MedalServiceBalance: number;
    // 有効期限が近い残高有りフラグ
    MedalServiceExpirationFlg: boolean;

    // メダルグループ詳細
    MedalGroupDetails: {
        [key: string]: Wallet001InitGetCoinMedalGroupDetailsItem;
    };
    // メダルチャージ方法
    MedalChargeMethod: string[];
    // MedalServiceEnableFlag
    MedalServiceEnableFlag?: boolean;
    // MainMedalFlag
    MainMedalFlag?: boolean;
    // 有効期限日数
    ExpirationDateDays: number;
}
