import { Typography } from '@mui/material';
import { useCallback, useMemo, useRef } from 'react';
import { useNavigate } from 'react-router-dom';
import Button from '../Button/Button';
import CommonDialog, { CommonDialogRef } from '../CommonDialog/CommonDialog';
import Icons from '../Icons/Icons';
import './styles.scss';
import QrCodeReader, { ScanResult } from '../QrCodeReader/QrCodeReader';
import Utils from '../../utils/utils';

export type Props = {
    onScan?: (data: string | null) => void;
    onChangeMode?: () => void;
    onClose?: () => void;
    header: {
        title: string;
        onLeftButtonClick?: () => void;
        onRightButtonClick?: () => void;
        hideBack?: boolean;
        hideClose?: boolean;
    };
    infoText?: string;
    isHiddenOnChangeMode?: boolean;
};

const message = Utils.t('charge.qr_common.guide_permission');

/**
 * QRReader Component
 */
export default function QRScanner({
    onScan,
    onChangeMode,
    onClose,
    header,
    infoText,
    isHiddenOnChangeMode,
}: Props): React.JSX.Element {
    const navigate = useNavigate();
    const { onLeftButtonClick, onRightButtonClick, hideBack, hideClose } = header;
    const dialogRef = useRef<CommonDialogRef>(null);

    /**
     * Handle left button click
     */
    const handleLeftButtonClick = useCallback(() => {
        onLeftButtonClick ? onLeftButtonClick() : navigate(-1);
    }, [navigate, onLeftButtonClick]);

    /**
     * Handle right button click
     */
    const handleRightButtonClick = useCallback(() => {
        onRightButtonClick?.();
    }, [onRightButtonClick]);

    /**
     * Handle when scan QR successful
     * @param data QR' data
     */
    const handleScan = useCallback(
        (data: string) => {
            if (data) {
                onScan?.(data);
            }
        },
        [onScan]
    );

    /**
     * onCloseErrorDialog
     */
    const onCloseErrorDialog = useCallback(() => {
        dialogRef.current?.toggleDialog(false);
        onChangeMode?.();
    }, [onChangeMode]);

    /**
     * Content dialog error
     */
    const contentDialogError = useMemo(() => {
        return (
            <div className="qr-permission-error">
                <Typography className="mb-2">
                    <b>{Utils.t('charge.qr_common.permission_error')}</b>
                </Typography>
                <p className="qr-permission-error-message overflow-auto">{message}</p>
                <Button className="w-100 mb-2 button-close-permission" variant="outlined" onClick={onCloseErrorDialog}>
                    {Utils.t('charge.qr_common.close_dialog_permission')}
                </Button>
            </div>
        );
    }, [onCloseErrorDialog]);

    /**
     * Function to handle result when use scan QR Code
     */
    const handleResultQRScan = useCallback(
        (result: ScanResult): void => {
            if (result.status === 'SUCCESS') {
                handleScan(result?.result ?? '');
            } else if (result.type === 'QrCodeScanTimeout') {
                onChangeMode?.();
            } else {
                dialogRef?.current?.toggleDialog?.(true);
            }
        },
        [handleScan, onChangeMode]
    );

    /**
     * handleClickQrScanBody
     * @param event React.MouseEvent<HTMLDivElement>
     */
    const handleClickQrScanBody = useCallback((event?: React.MouseEvent<HTMLDivElement>) => {
        event?.stopPropagation();
    }, []);

    return useMemo(
        () => (
            <div>
                <div id="common-qr-scan-container" onClick={onClose}>
                    <div className="qr-scan-header d-flex w-100 align-items-center">
                        {hideBack ? (
                            <div className="back-icon" />
                        ) : (
                            <div className="back-icon" onClick={handleLeftButtonClick}>
                                <Icons.BackIcon color="#ffffff" />
                            </div>
                        )}
                        <div
                            className="d-flex w-100 justify-content-center mx-16 qr-scan-header-title"
                            style={{
                                minHeight: 53,
                                paddingLeft: 0,
                                paddingRight: 0,
                            }}
                            onClick={onClose}
                        >
                            {header.title}
                        </div>

                        {hideClose ? (
                            <div className="back-icon" />
                        ) : (
                            <div className="close-icon" onClick={handleRightButtonClick}>
                                <Icons.CloseIcon color="#ffffff" />
                            </div>
                        )}
                    </div>
                    <div className="qr-scan-body" onClick={handleClickQrScanBody}>
                        <span onClick={onClose}>
                            <Typography className="qr-scan-title">
                                {infoText ? infoText : Utils.t('charge.qr_common.qr_header')}
                            </Typography>
                        </span>
                        <QrCodeReader
                            onScanResult={handleResultQRScan}
                            ViewFinder={<div className="qr-red-center-box" />}
                            // scanTimeout={30000}
                        />
                    </div>
                    {!isHiddenOnChangeMode && (
                        <div className="qr-scan-input" onClick={onChangeMode}>
                            <span className="icon-circle">
                                <Icons.ArrowRightIconWithCircle />
                            </span>
                            <span className="qr-scan-input-description fs-14 ">
                                {Utils.t('charge.qr_common.qr_description')}
                            </span>
                        </div>
                    )}
                </div>
                <CommonDialog
                    ref={dialogRef}
                    xCenter
                    yCenter
                    childrenClassName="dialog-error-children"
                    contentClassName="dialog-error-permission-error"
                >
                    {contentDialogError}
                </CommonDialog>
            </div>
        ),
        [
            onClose,
            hideBack,
            handleLeftButtonClick,
            header.title,
            hideClose,
            handleRightButtonClick,
            handleClickQrScanBody,
            infoText,
            handleResultQRScan,
            isHiddenOnChangeMode,
            onChangeMode,
            contentDialogError,
        ]
    );
}
