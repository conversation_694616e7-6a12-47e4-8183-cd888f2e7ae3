import { useCallback, useMemo } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { RootState } from '../redux';
import { CommonState } from '../redux/states/common';
import { commonActions } from '../redux/slices/common';

type useCommonType = CommonState & {
    updateLanguage: (payload: string) => void;
};

/**
 * useCommon hook
 */
const useCommon = (): useCommonType => {
    const commonState = useSelector((root: RootState) => root.commonReducer);
    const dispatch = useDispatch();

    /**
     * updateLanguage
     * @param payload string
     */
    const updateLanguage = useCallback(
        (payload: string) => {
            dispatch(commonActions.updateLanguage(payload));
        },
        [dispatch]
    );

    return useMemo(
        () => ({
            ...commonState,
            updateLanguage,
        }),
        [commonState, updateLanguage]
    );
};

export default useCommon;
