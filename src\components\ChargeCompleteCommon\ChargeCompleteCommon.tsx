import { Typography } from '@mui/material';
import { memo, useCallback, useMemo } from 'react';
import useWindows from '../../hooks/windows';
import Utils from '../../utils/utils';
import Button from '../Button/Button';
import Icons from '../Icons/Icons';
import './styles.scss';

/**
 * ResultStatus
 */
export enum ResultStatus {
    SUCCESS = 'success',
    FAILURE = 'failure',
}

/**
 * ChargeCompleteType
 */
export type ChargeCompleteType = {
    chargeReason?: string;
    price?: string | number;
    chargeDateTitle?: string;
    chargeDate?: string;
    image?: string;
    onClickCallBack: () => void;
    chargeDescription?: string;
    unit?: string;
    status?: ResultStatus | string;
    buttonName?: string;
    purchasePlanNumber?: number;
    purchaseNumber?: number;
    dateFormatOutput?: string;
    withTime?: boolean;
};

/**
 * ChargeCompleteProps
 */
export interface ChargeCompleteProps {
    data: ChargeCompleteType;
}

/**
 * ChargeComplete
 * @returns React.JSX.Element
 */
const ChargeCompleteCommon = (props: ChargeCompleteProps): React.JSX.Element => {
    const { windowSize } = useWindows();
    const {
        onClickCallBack,
        status,
        price,
        chargeDateTitle,
        chargeDate,
        chargeDescription,
        chargeReason,
        unit,
        buttonName,
        purchasePlanNumber,
        purchaseNumber,
        dateFormatOutput = 'YYYY/MM/DD(ddd)',
        withTime = true,
    } = useMemo(() => props.data, [props.data]);

    /**
     * Handle click back to home
     * Default to navigate to /home
     */
    const onBackToHome = useCallback(() => {
        onClickCallBack();
    }, [onClickCallBack]);

    return useMemo(
        () => (
            <div
                className="charge-complete-common-container"
                style={{
                    height: windowSize.height - 53,
                }}
            >
                {status === ResultStatus.FAILURE ? (
                    <div className="charge-complete-message text-center">
                        <Typography fontSize={20} className="ff-noto-bold title-error">
                            {Utils.t('charge.charge_complete.message_err')}
                        </Typography>
                        <Typography fontSize={20} className="ff-noto-bold title-error">
                            {Utils.t('charge.charge_complete.message_err1')}
                        </Typography>
                    </div>
                ) : (
                    <div>
                        <div className="charge-complete-icon-image ">
                            <div className="charge-complete-icon d-flex justify-content-center ">
                                <Icons.SuccessIcon width={100} height={100} />
                            </div>
                            {/* <div className="charge-complete-image d-flex justify-content-center ">
                                <img width="100%" height="151px" src={image || Images.HOME.BANNER_1} />
                            </div> */}
                        </div>
                        <div className="charge-complete-message text-center">
                            {Boolean(chargeReason) && (
                                <Typography fontSize={20} color="#222222" className="ff-noto-bold">
                                    {chargeReason}
                                </Typography>
                            )}
                            {price != undefined && Boolean(unit) && (
                                <Typography fontSize={20} color="#222222" className="ff-noto-bold">
                                    <span className="ff-roboto-bold">{Utils.formatNumber(price)}</span>
                                    <span>{unit}</span>
                                </Typography>
                            )}
                            {purchasePlanNumber && purchaseNumber && (
                                <Typography fontSize={20} color="#222222" className="ff-noto-bold">
                                    <span className="ff-roboto-bold">{`${purchaseNumber}`}</span>
                                    <span>セット</span>
                                </Typography>
                            )}

                            {Boolean(chargeDescription) && (
                                <Typography fontSize={20} color="#222222" className="ff-noto-bold">
                                    {chargeDescription}
                                </Typography>
                            )}
                        </div>
                        {Boolean(chargeDate) && (
                            <Typography className="ff-noto-bold" color="#222222" align="center" fontSize={14}>
                                {chargeDateTitle || Utils.t('charge.charge_complete.expire_date')}
                                <span className="charge-complete-time ff-roboto-bold">
                                    {Utils.getDateJapan(
                                        chargeDate,
                                        withTime,
                                        false,
                                        'YYYY/MM/DD HH:mm:ss',
                                        dateFormatOutput
                                    )}
                                </span>
                            </Typography>
                        )}
                    </div>
                )}
                <div className="btn-handle">
                    <Button
                        name={buttonName || ''}
                        padding="10px 16px"
                        className="mt-auto w-100"
                        onClick={onBackToHome}
                        fontSize={17}
                    >
                        {Utils.t('charge.charge_complete.button')}
                    </Button>
                </div>
            </div>
        ),
        [
            windowSize.height,
            status,
            chargeReason,
            price,
            unit,
            purchasePlanNumber,
            purchaseNumber,
            chargeDescription,
            chargeDate,
            chargeDateTitle,
            withTime,
            dateFormatOutput,
            buttonName,
            onBackToHome,
        ]
    );
};

export default memo(ChargeCompleteCommon);
