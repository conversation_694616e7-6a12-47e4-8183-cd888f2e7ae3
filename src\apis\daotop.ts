import API from '../constants/api';
import { BaseResponse } from '../models/common';
import { DAOTop001DaoGetDaoListModel, DAOTop001DaoGetDaoListResult } from '../models/daotop001-dao-get-daolist';
import { DAOTop002DaoGetParticipantModel, DAOTop002ParticipantItem } from '../models/daotop002-dao-get-participant';
import { DAOTop002DaoPutParticipantModel } from '../models/daotop002-dao-put-participant';
import { DAOTop003DaoExitParticipantModel } from '../models/daotop003-dao-exit-participant';
import { DAOTop003DaoGetProfileModel, DAOTop003ProfileItem } from '../models/daotop003-dao-get-profile';
import { DAOTop004DaoUpdateProfileModel } from '../models/daotop004-dao-update-profile';
import { DAOTop005DaoGetDetailModel, DAOTop005DaoGetDetailResult } from '../models/daotop005-dao-get-detail';
import { Top001InitGetEventListModel, Top001InitGetEventListResult } from '../models/top001-init-get-eventlist';
import createAPI from './baseApi';

/**
 * DaoTopApi
 */
class DaoTopApi {
    /**
     * daoTop002DaoGetParticipant
     * @param data DAOTop002DaoGetParticipantModel
     * @returns Promise<BaseResponse<DAOTop002ParticipantItem>>
     */
    static daoTop002DaoGetParticipant = (
        data: DAOTop002DaoGetParticipantModel
    ): Promise<BaseResponse<DAOTop002ParticipantItem>> => {
        return createAPI<DAOTop002ParticipantItem>({
            url: API.DAOTOP002_DAO_GET_PARTICIPANT,
            data,
        });
    };

    /**
     * daoTop002DaoPutParticipant
     * @param data DAOTop002DaoGetParticipantModel
     * @returns Promise<BaseResponse>
     */
    static daoTop002DaoPutParticipant = (data: DAOTop002DaoPutParticipantModel): Promise<BaseResponse> => {
        return createAPI<BaseResponse>({
            url: API.DAOTOP002_DAO_PUT_PARTICIPANT,
            data,
        });
    };

    /**
     * daoTop003DaoGetProfile
     * @param data DAOTop003DaoGetProfileModel
     * @returns Promise<BaseResponse<DAOTop003ProfileItem>>
     */
    static daoTop003DaoGetProfile = (
        data: DAOTop003DaoGetProfileModel
    ): Promise<BaseResponse<DAOTop003ProfileItem>> => {
        return createAPI<DAOTop003ProfileItem>({
            url: API.DAOTOP003_DAO_GET_PROFILE,
            data,
        });
    };

    /**
     * daoTop003DaoExitParticipant
     * @param data DAOTop003DaoExitParticipantModel
     * @returns Promise<BaseResponse>
     */
    static daoTop003DaoExitParticipant = (data: DAOTop003DaoExitParticipantModel): Promise<BaseResponse> => {
        return createAPI<BaseResponse>({
            url: API.DAOTOP003_DAO_EXIT_PARTICIPANT,
            data,
        });
    };

    /**
     * daoTop004DaoUpdateProfile
     * @param data DAOTop004DaoUpdateProfileModel
     * @returns Promise<BaseResponse>
     */
    static daoTop004DaoUpdateProfile = (data: DAOTop004DaoUpdateProfileModel): Promise<BaseResponse> => {
        return createAPI<DAOTop003ProfileItem>({
            url: API.DAOTOP004_DAO_UPDATE_PROFILE,
            data,
        });
    };

    /**
     * daoTop001DaoGetDaoList
     * @param data DAOTop001DaoGetDaoListModel
     * @returns Promise<BaseResponse<DAOTop001DaoGetDaoListResult>>
     */
    static daoTop001DaoGetDaoList = (
        data: DAOTop001DaoGetDaoListModel
    ): Promise<BaseResponse<DAOTop001DaoGetDaoListResult>> => {
        return createAPI<DAOTop001DaoGetDaoListResult>({
            url: API.DAOTOP001_DAO_GET_DAOLIST,
            data,
        });
    };

    /**
     * top001InitGetEventList
     * @param data Top001InitGetEventListModel
     * @returns Promise<BaseResponse<Top001InitGetEventListResult>>
     */
    static top001InitGetEventList = (
        data: Top001InitGetEventListModel
    ): Promise<BaseResponse<Top001InitGetEventListResult>> => {
        return createAPI<Top001InitGetEventListResult>({
            url: API.TOP001_INIT_GET_EVENTLIST,
            data,
        });
    };

    /**
     * daoTop005DaoGetDetail
     * @param data DAOTop005DaoGetDetailModel
     * @returns Promise<BaseResponse<DAOTop005DaoGetDetailResult>>
     */
    static daoTop005DaoGetDetail = (
        data: DAOTop005DaoGetDetailModel
    ): Promise<BaseResponse<DAOTop005DaoGetDetailResult>> => {
        return createAPI<DAOTop005DaoGetDetailResult>({
            url: API.DAOTOP005_DAO_GET_DETAIL,
            data,
        });
    };
}

export default DaoTopApi;
