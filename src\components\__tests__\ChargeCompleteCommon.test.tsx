import { Typography } from '@mui/material';
import { shallow } from 'enzyme';
import Utils from '../../utils/utils';
import Button from '../Button/Button';
import ChargeCompleteCommon, { ChargeCompleteProps, ResultStatus } from '../ChargeCompleteCommon/ChargeCompleteCommon';
import Icons from '../Icons/Icons';

// mock useNavigate function
const mockedUseNavigate = jest.fn();

/**
 * mock for react-router-dom lib
 */
jest.mock('react-router-dom', () => ({
    ...jest.requireActual('react-router-dom'),
    useSearchParams: () => null,
    useLoaderData: () => null,
    useNavigate: () => mockedUseNavigate,
}));

/**
 * Unit test for ChargeCompleteCommon
 */
describe('Unit test for ChargeCompleteCommon', () => {
    /**
     * setup element
     * @param p InputProps
     */
    const setup = (p: ChargeCompleteProps) => {
        const s = shallow(<ChargeCompleteCommon data={p.data} />);

        return {
            s: s,
        };
    };

    /**
     * should display with require data on page
     */
    it('should display with require data on page', () => {
        const { s } = setup({
            data: {
                price: 123456789,
            },
        });

        expect(s.find('div.charge-complete-common-container').length).toBe(1);
    });

    /**
     * should display button [ホームに戻る]
     */
    it('should display button [ホームに戻る]', () => {
        const { s } = setup({
            data: {
                price: 123456789,
                unit: '円',
            },
        });

        const button = s.find(Button);

        expect(button.text()).toEqual('ホームに戻る');
    });

    /**
     * should transition to Home screen when click on button [ホームに戻る]
     */
    it('should display button [ホームに戻る]', () => {
        const { s } = setup({
            data: {
                price: 123456789,
                unit: '円',
            },
        });

        const button = s.find(Button);

        button.simulate('click');

        expect(mockedUseNavigate).toHaveBeenCalledWith('/');
    });

    /**
     * should transition to Home screen when click on button [ホームに戻る]
     */
    it('should call onClickCallback when click on button [ホームに戻る]', () => {
        const onClickCallBack = jest.fn();
        const { s } = setup({
            data: {
                price: 123456789,
                unit: '円',
                onClickCallBack,
            },
        });

        const button = s.find(Button);

        button.simulate('click');

        expect(onClickCallBack).toHaveBeenCalledTimes(1);
    });

    /**
     * should display error message when status is FAILURE
     */
    it('should display error message when status is FAILURE', () => {
        const { s } = setup({
            data: {
                price: 123456789,
                unit: '円',
                status: ResultStatus.FAILURE,
            },
        });

        const messages = s.find('div.charge-complete-message').find(Typography);
        expect(messages.length).toEqual(2);

        expect(messages.at(0).text()).toEqual('チャージに失敗しました。');
        expect(messages.at(1).text()).toEqual('決済処理でエラーが発生しました。');
    });

    /**
     * when status passed is success
     */
    describe('when status passed is success', () => {
        /**
         * should display chargeReason if is passed
         */
        it('should display chargeReason if is passed', () => {
            const { s } = setup({
                data: {
                    price: 123456789,
                    status: ResultStatus.SUCCESS,
                    chargeReason: 'This is mock charge reason',
                },
            });

            const messages = s.find('div.charge-complete-message').find(Typography);
            expect(messages.length).toEqual(1);

            expect(messages.text()).toEqual('This is mock charge reason');
        });

        /**
         * should display format price with unit if passed
         */
        it('should display format price with unit if passed', () => {
            const { s } = setup({
                data: {
                    price: 123456789,
                    unit: '円',
                    status: ResultStatus.SUCCESS,
                },
            });

            const messages = s.find('div.charge-complete-message').find(Typography);
            expect(messages.length).toEqual(1);

            expect(messages.text()).toEqual('123,456,789円');
        });

        /**
         * should display chargeDescription if passed
         */
        it('should display chargeDescription if passed', () => {
            const { s } = setup({
                data: {
                    price: 123456789,
                    status: ResultStatus.SUCCESS,
                    chargeDescription: 'This is mock charge description',
                },
            });

            const messages = s.find('div.charge-complete-message').find(Typography);
            expect(messages.length).toEqual(1);

            expect(messages.text()).toEqual('This is mock charge description');
        });

        /**
         * should display expire date if chargeDate is passed
         */
        it('should display chargeDescription if passed', () => {
            const { s } = setup({
                data: {
                    price: 123456789,
                    status: ResultStatus.SUCCESS,
                    chargeDate: '2023/06/09',
                },
            });

            const messages = s.find(Typography);

            const jpDate = Utils.getDateJapan('2023/06/09', true);
            expect(jpDate).toEqual('2023年06月09日（金） 00:00');
            expect(messages.text()).toEqual('有効期限：2023年06月09日（金） 00:00');
        });

        /**
         * should display SuccessIcon
         */
        it('should display SuccessIcon', () => {
            const { s } = setup({
                data: {
                    price: 123456789,
                    status: ResultStatus.SUCCESS,
                },
            });

            expect(s.find(Icons.SuccessIcon).length).toBe(1);
        });
    });
});
