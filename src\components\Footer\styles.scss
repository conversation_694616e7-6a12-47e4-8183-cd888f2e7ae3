#AppFooter {
    max-width: var(--app-main-width);
    padding-left: 0;
    padding-right: 0;
    background-color: #fff;
    border-color: #efefef;
    border-width: 1px;
    border-style: solid;
    z-index: 999;

    .footer-container {
        padding: 8px;
        height: 72px;
        box-shadow: 0px 0px 10px 0px #999;
    }
    
    .footer-item {
        user-select: none;
        -moz-user-select: none;
        -webkit-user-select: none;
        -ms-user-select: none;
        cursor: pointer;
        border-radius: 4px;
        background-color: #fff;
        transition: all 0.5s ease;

        .icon {
            width: 24px;
            height: 24px;
        }

        .label {
            font-size: 10px;
            font-family: "Noto Sans JP Bold", Meiryo, メイリオ, Arial, Helvetica, sans-serif;
            color: #707070;
            margin-top: 4px;
            &.selected {
                color: var(--app-base-color);;
            }
        }

        &-center {
            width: 72px;
            min-width: 72px;
            height: 72px;
            min-height: 72px;
            top: -24px;
            background-color: #fff;
            border-radius: 72px;

            &-container {
                cursor: pointer;
                width: 60px;
                min-width: 60px;
                height: 60px;
                min-height: 60px;
                background: var(--app-base-color);
                border-radius: 60px;
                justify-content: center;
                align-items: center;
                z-index: 2 !important;
            }
            .label {
                font-size: 10px;
                font-family: "Noto Sans JP Bold", Meiryo, メイリオ, Arial, Helvetica, sans-serif;
                color: #fff;
                margin-top: 4px;
            }
            &-overlay {
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                width: 100%;
                height: 100%;
                border-radius: 100px;
                z-index: -1 !important;
                box-shadow: 0px 0px 10px 0px #999;
            }
        }
    }
    .notification-unread {
        right: -2px;
        top: -9px;
    }
    .item-separator {
        min-width: 8px;
    }
    .title-error {
        color: #d31e2d;
    }


}
