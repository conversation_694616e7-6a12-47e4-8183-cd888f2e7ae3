import { shallow } from 'enzyme';
import Container, { ContainerProps, ErrorFallback } from '../Container/Container';
import Header from '../Header/Header';

describe('Unit test for Container component', () => {
    /**
     * setup element
     * @param p CommonTabProps
     */
    const setup = (p?: Omit<ContainerProps, 'children' | 'screenName'>) => {
        return (
            <Container screenName="container_test" {...p}>
                <div>Container content</div>
            </Container>
        );
    };

    /**
     * should render content inside
     */
    it('should display content inside', () => {
        const wrapper = shallow(setup());

        expect(wrapper.find(<div>Container content</div>)).toBeTruthy();
    });

    /**
     * should display default header
     */
    it('should display default header', () => {
        const wrapper = shallow(
            setup({
                useHeader: true,
            })
        );

        expect(wrapper.find(Header)).toBeTruthy();
    });

    /**
     * should display custom props header
     */
    it('should display custom props header', () => {
        const wrapper = shallow(
            setup({
                useHeader: {
                    type: 'withTitle',
                    title: 'Container mock title test',
                },
            })
        );

        const header = wrapper.find(Header);
        expect(header).toBeTruthy();
        expect(header.props().type).toBe('withTitle');
        expect(header.props().title).toBe('Container mock title test');
    });

    /**
     * should display header
     */
    it('should display footer', () => {
        const wrapper = shallow(
            setup({
                useFooter: true,
            })
        );

        expect(wrapper.find(Header)).toBeTruthy();
    });

    describe('Unit test for ErrorFallback', () => {
        /**
         * should display ErrorFallback when an error occur
         */
        it('should display ErrorFallback when an error occur', () => {
            const reset = jest.fn();
            const wrapper = shallow(
                <ErrorFallback
                    error={{
                        message: 'mock error occur',
                    }}
                    resetErrorBoundary={reset}
                />
            );

            expect(wrapper.find(<div role="alert" />)).toBeTruthy();
            expect(wrapper.find(<pre>mock error occur</pre>)).toBeTruthy();
        });

        /**
         * should call resetErrorBoundary when click on Try again button
         */
        it('should call resetErrorBoundary when click on Try again button', () => {
            const reset = jest.fn();
            const wrapper = shallow(
                <ErrorFallback
                    error={{
                        message: 'mock error occur',
                    }}
                    resetErrorBoundary={reset}
                />
            );

            const button = wrapper.find('button');

            expect(button).toBeTruthy();

            button.simulate('click');

            expect(reset).toHaveBeenCalled();
        });
    });
});
