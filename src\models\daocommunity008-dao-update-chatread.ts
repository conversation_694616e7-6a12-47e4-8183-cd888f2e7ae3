import { CommonCallback } from './common';

/**
 * DAOCommunity008DaoUpdateChatReadModel
 */
export interface DAOCommunity008DaoUpdateChatReadModel extends CommonCallback {
    // DaoID
    DaoID: string;
    // チャネルID
    ChannelID: string;
    // 更新区分
    // 0：最終読込み日時を更新 - 1：タイマー読込み日時を更新 - 2：上記両方を更新
    UpdateType: number;
}

/**
 * DAOCommunity008DaoUpdateChatReadResult
 */
export interface DAOCommunity008DaoUpdateChatReadResult extends CommonCallback {
    // DaoID
    DaoID: string;
    // チャネルID
    ChannelID: string;
    // 更新区分
    // 0：最終読込み日時を更新 - 1：タイマー読込み日時を更新 - 2：上記両方を更新
    UpdateType: number;
}
