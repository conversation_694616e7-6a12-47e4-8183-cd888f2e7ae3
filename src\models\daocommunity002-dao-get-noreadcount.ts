import { CommonCallback } from './common';

/**
 * DAOCommunity002DaoGetNoReadCountModel
 */
export interface DAOCommunity002DaoGetNoReadCountModel extends CommonCallback {
    // DAOID
    DaoID: string;
}

/**
 * DAOCommunity002NoReadChannelListItem
 */
export interface DAOCommunity002NoReadChannelListItem {
    // チャネルID
    ChannelID: string;
    // 未読件数
    NoReadCount: number;
}

/**
 * DAOCommunity002NoReadMentionListItem
 */
export interface DAOCommunity002NoReadMentionListItem {
    // チャットID
    ChatID: string;
    // メンション番号（リアクションIDを含む）
    MentionNo: string;
}

/**
 * DAOCommunity002DaoGetNoReadCountResult
 */
export interface DAOCommunity002DaoGetNoReadCountResult {
    // メンションリスト
    NoReadChannelList: DAOCommunity002NoReadChannelListItem[];
    NoReadMentionList: DAOCommunity002NoReadMentionListItem[];
}
