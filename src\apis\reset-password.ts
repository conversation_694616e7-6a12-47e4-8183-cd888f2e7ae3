import API from '../constants/api';
import { BaseResponse } from '../models/common';
import {
    MainSendAuthCode001R<PERSON><PERSON>,
    MainCheckAuthCode002Model,
    MainResetPassword005Model,
    MainSendAuthCode001Model,
    MainSendAuthCode002Model,
} from '../models/reset001-main-send-authcode';
import createAPI from './baseApi';

class ResetPasswordAPI {
    /**
     * sendAuthCode001
     * @params data MainSendAuthCode001Model
     * @returns Promise<BaseResponse<MainSendAuthCode001Result>
     */
    static sendAuthCode001 = (data: MainSendAuthCode001Model): Promise<BaseResponse<MainSendAuthCode001Result>> => {
        return createAPI<MainSendAuthCode001Result>({
            url: API.RESET001_MAIN_SEND_AUTHCODE,
            data,
        });
    };

    /**
     * sendAuthCode002
     * @params data MainSendAuthCode002Model
     * @returns Promise<BaseResponse>
     */
    static sendAuthCode002 = (data: MainSendAuthCode002Model): Promise<BaseResponse> => {
        return createAPI({
            url: API.RESET002_MAIN_SEND_AUTHCODE,
            data,
        });
    };

    /**
     * checkAuthCode002
     * @params data MainCheckAuthCode002Model
     * @returns Promise<BaseResponse>
     */
    static checkAuthCode002 = (data: MainCheckAuthCode002Model): Promise<BaseResponse> => {
        return createAPI({
            url: API.RESET002_MAIN_CHECK_AUTHCODE,
            data,
        });
    };

    /**
     * resetPassword005
     * @params data MainResetPassword005Model
     * @returns Promise<BaseResult>
     */
    static resetPassword005 = (data: MainResetPassword005Model): Promise<BaseResponse> => {
        return createAPI({
            url: API.RESET005_MAIN_RESET_PASSWORD,
            data,
        });
    };
}

export default ResetPasswordAPI;
