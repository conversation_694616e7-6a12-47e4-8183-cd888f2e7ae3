.common-button-component {
    box-shadow: none !important;
    outline: none !important;
    font-family: "Noto Sans JP Bold", <PERSON><PERSON>, メイリオ, Arial, Helvetica, sans-serif !important;
    max-height: unset !important;

    &.MuiButton-containedPrimary {
        &:disabled:not(.loading) {
            pointer-events: auto;
            background-color: #cccccc !important;
            color: #fff !important;
        }
        &:not(.disabled-hover, :disabled):hover {
            background-color: var(--app-base-color) !important;
        }
    }

    &.MuiButton-outlinedPrimary {
        background-color: #fff;
        color: #222222;
        border-color: #cccccc;
        border-width: 2px;
        &:disabled {
            pointer-events: auto;
            color: #cccccc !important;
        }
        &:not(:disabled):hover {
            border-width: 2px;
        }
    }

    .button-loading-container {
        width: 20px;
        height: 20px;
        div {
            display: flex;
            justify-content: center;
            align-items: center;
            font-size: unset !important;
            height: unset !important;
            width: 100%;
        }
        svg {
            vertical-align: unset !important;
            font-size: unset !important;
        }
    }
}
