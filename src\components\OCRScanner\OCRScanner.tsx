import { Typography } from '@mui/material';
import {
    ForwardedRef,
    forwardRef,
    memo,
    useCallback,
    useEffect,
    useImperativeHandle,
    useMemo,
    useRef,
    useState,
} from 'react';
import useWindows from '../../hooks/windows';
import { CameraUtils } from '../../utils/camera';
import CommonUtils from '../../utils/common';
import Utils, { __DEV__ } from '../../utils/utils';
import { ScanResult } from '../QrCodeReader/QrCodeReader';
import './styles.scss';

/**
 * OCRScanResult
 */
export interface OCRScanResult extends ScanResult {
    fullResult?: string;
}

/**
 * OCRScannerProps
 */
export interface OCRScannerProps {
    /**
     * onScanResult
     */
    onScanResult?: (result?: OCRScanResult) => void;
    /**
     * timeout
     */
    timeout?: number;
    /**
     * onClose
     */
    onClose?: () => void;
}

/**
 * OCRScannerRef
 */
export interface OCRScannerRef {
    /**
     * reset scan function
     */
    reset?: () => void;
}

/**
 * OCRScanner Component
 */
const OCRScanner = forwardRef((props: OCRScannerProps, ref: ForwardedRef<OCRScannerRef>): React.JSX.Element => {
    const { onScanResult, onClose } = useMemo(() => props, [props]);
    const [base64Image, setBase64Image] = useState<string>();
    const { windowSize } = useWindows();
    const videoRef = useRef<HTMLVideoElement>(null);
    const [enableCaptureButton, setEnableCaptureButton] = useState(false);

    /**
     * handleLoadCamera
     */
    const handleLoadCamera = useCallback(async () => {
        try {
            // get media devices
            const mediaStream = await CameraUtils.getUserMedia({
                video: {
                    width: 600,
                    facingMode: 'environment',
                    aspectRatio: 1,
                },
                audio: false,
            });

            // cannot get media stream
            if (!mediaStream) {
                onScanResult?.({
                    status: 'FAILURE',
                    type: 'MediaStreamNotFound',
                });
                return;
            }

            // create video element
            const videoElement = document.getElementById('ocr-scanner-video-id') as HTMLVideoElement;

            // set video muted
            videoElement.muted = true;

            // set video source object is media stream
            await CameraUtils.setStream(videoElement, mediaStream);

            // after loaded metadata
            videoElement.onloadedmetadata = (): void => {
                // play video
                videoElement
                    .play()
                    .then(() => {
                        // enable capture button
                        setEnableCaptureButton(true);
                    })
                    .catch(() => {
                        // disable capture button
                        setEnableCaptureButton(false);
                        // dispatch function scan result
                        onScanResult?.({
                            status: 'FAILURE',
                            error: Utils.t('charge.charge_serial_code.scan.camera_not_ready'),
                            type: 'VideoCannotPlayError',
                        });
                    });
            };
        } catch {
            CommonUtils.showMessage({
                type: 'ERROR',
                message: (
                    <div className="d-flex flex-column">
                        <div className="title-error">{Utils.t('charge.charge_serial_code.scan.permission_error')}</div>
                        <div>{Utils.t('charge.charge_serial_code.scan.permission_error_message')}</div>
                    </div>
                ),
                onClose: onClose,
                useDialogCloseButton: { callback: onClose },
            });
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);

    useEffect(() => {
        handleLoadCamera().catch((error) => {
            __DEV__ && console.log(error?.message);
        });
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);

    /**
     * handleResetScanner
     */
    const handleResetScanner = useCallback(() => {
        // clear base64 image data
        setBase64Image('');

        // enable capture button
        setEnableCaptureButton(true);
    }, []);

    useImperativeHandle(
        ref,
        () => ({
            reset: handleResetScanner,
        }),
        [handleResetScanner]
    );

    /**
     * handleClickCaptureButton
     * handle click on [撮影] button
     */
    const handleClickCaptureButton = useCallback(() => {
        if (videoRef?.current) {
            if (!enableCaptureButton) {
                CommonUtils.showMessage({
                    message: Utils.t('charge.charge_serial_code.scan.camera_not_ready'),
                    type: 'ERROR',
                });
                return;
            }
            // get dimension of video element (return videoConstraints width, height by default)
            const { videoWidth = 600, videoHeight = 600 } = videoRef.current;

            // create new canvas element
            const canvas = document.createElement('canvas');

            // calculate crop position and size
            const sx = 16;
            const sy = videoHeight * 0.42;
            const sw = videoWidth - 32;
            const sh = videoHeight * 0.16;

            // destination canvas size (canvas size will be equal to cropped image size)
            canvas.width = sw;
            canvas.height = sh;

            // destination context
            const context = canvas.getContext('2d');

            // stop process if context if not available
            if (!context) return;

            // draw image in canvas
            context.drawImage(videoRef.current, sx, sy, sw, sh, 0, 0, sw, sh);

            // get base64 from canvas
            const base64 = canvas.toDataURL('image/jpeg');

            // get base64 data
            const base64Data = base64.split(',')[1];

            // set to show image
            setBase64Image(base64);

            // onScanResult is passed
            if (onScanResult) {
                // dispatch result to parent
                onScanResult?.({
                    status: base64Data ? 'SUCCESS' : 'FAILURE',
                    error: base64Data ? undefined : Utils.t('charge.charge_serial_code.scan.read_serial_code_failure'),
                    result: base64Data,
                    fullResult: base64,
                });
            } else {
                // check base64
                if (base64Data?.length === 0) {
                    CommonUtils.showMessage({
                        message: Utils.t('charge.charge_serial_code.scan.read_serial_code_failure'),
                    });
                    return;
                }
            }
        }
    }, [enableCaptureButton, onScanResult]);

    return useMemo(
        () => (
            <div id="common-ocd-scan-container" className="d-flex flex-column">
                <div className="d-flex flex-column flex-1">
                    <div>
                        <Typography className="ocd-scan-title">
                            {Utils.t('charge.charge_serial_code.scan.serial_code_scan_header')}
                        </Typography>
                    </div>
                    <div
                        className="ocr-camera-container"
                        style={{
                            width: Math.min(windowSize.width, 600),
                            height: Math.min(windowSize.width, 600),
                        }}
                    >
                        <video
                            ref={videoRef}
                            id="ocr-scanner-video-id"
                            controls={false}
                            contextMenu="none"
                            contentEditable={false}
                            autoPlay={true}
                            playsInline={true}
                            muted={true}
                        />

                        {enableCaptureButton && (
                            <div className="overlay-layer">
                                <div className="center" />
                                {base64Image && <img src={base64Image} />}
                                <div className="read-area" />
                                <div className="top" />
                                <div className="bottom" />
                                <div className="left" />
                                <div className="right" />
                            </div>
                        )}
                    </div>
                    <div className="d-flex align-item-center justify-content-center mt-3 mb-3">
                        <div className="btn-capture-container" onClick={handleClickCaptureButton}>
                            <div className="btn-capture">
                                <Typography className="text-capture">
                                    {Utils.t('charge.charge_serial_code.scan.btn_capture')}
                                </Typography>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        ),
        [base64Image, enableCaptureButton, handleClickCaptureButton, windowSize.width]
    );
});

OCRScanner.displayName = 'OCRScanner';

export default memo(OCRScanner);
