import clsx from 'clsx';
import { ForwardedRef, forwardRef, memo, useMemo } from 'react';
import CommonDialog, { CommonDialogProps, CommonDialogRef } from '../CommonDialog/CommonDialog';
import Container from '../Container/Container';
import { HeaderProps } from '../Header/Header';
import './styles.scss';

/**
 * CommonDialogFullscreenProps
 */
export interface CommonDialogFullscreenProps extends CommonDialogProps {
    /**
     * useHeader
     */
    useHeader?: boolean | HeaderProps;
    /**
     * ScreenName
     */
    screenName?: string;
}

/**
 * CommonDialogFullscreen
 * @param props CommonDialogFullscreenProps
 * @param ref ForwardedRef<CommonDialogRef>
 */
const CommonDialogFullscreen = forwardRef((props: CommonDialogFullscreenProps, ref: ForwardedRef<CommonDialogRef>) => {
    const {
        children,
        useDialogCloseButton,
        useBackdropDismiss,
        useHeader,
        screenName,
        childrenClassName,
        useTransaction,
        keepMounted,
    } = useMemo(() => props, [props]);

    return useMemo(
        () => (
            <CommonDialog
                ref={ref}
                xCenter
                yCenter
                useBackdropDismiss={useBackdropDismiss}
                useDialogCloseButton={useDialogCloseButton}
                childrenClassName={`p-0 ${childrenClassName}`}
                useTransaction={useTransaction}
                keepMounted={keepMounted}
            >
                <Container className="pt-0" screenName={screenName || ''} useHeader={useHeader}>
                    <div
                        className={clsx('dialog-content-fullscreen d-flex flex-column', childrenClassName)}
                        style={{ top: useHeader ? 53 : 0 }}
                    >
                        {children}
                    </div>
                </Container>
            </CommonDialog>
        ),
        // eslint-disable-next-line react-hooks/exhaustive-deps
        [
            useBackdropDismiss,
            useDialogCloseButton,
            childrenClassName,
            useTransaction,
            keepMounted,
            screenName,
            useHeader,
            children,
        ]
    );
});

// display name for CommonDialogFullscreen
CommonDialogFullscreen.displayName = 'CommonDialogFullscreen';

export default memo(CommonDialogFullscreen);
