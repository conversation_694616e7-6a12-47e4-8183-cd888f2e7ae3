import API from '../constants/api';
import { BaseResponse } from '../models/common';
import {
    News001InitGetImportantNewsListModel,
    News001InitGetImportantNewsListResult,
    News001InitGetPersonalNewsListModel,
    News001InitGetPersonalNewsListResult,
} from '../models/news001-init-news-list';
import { News002InitGetNewsDetailModel, News002InitGetNewsDetailResult } from '../models/news002-init-news-detail';
import {
    News001InitGetImportantNewsUnreadFlagModel,
    News001InitGetImportantNewsUnreadFlagResult,
} from '../models/news001-init-get-important-news-unread-flag';
import {
    News001InitGetPersonalNewsUnreadFlagModel,
    News001InitGetPersonalNewsUnreadFlagResult,
} from '../models/news001-init-get-personal-news-unread-flag';
import createAPI from './baseApi';

/**
 * NewsAPI
 */
class NewsAPI {
    /**
     * news001InitGetPersonalNewsList
     * @param data News001InitGetPersonalNewsListModel
     * @returns Promise<BaseResponse<News001InitGetPersonalNewsListResult>>
     */
    static news001InitGetPersonalNewsList = (
        data: News001InitGetPersonalNewsListModel
    ): Promise<BaseResponse<News001InitGetPersonalNewsListResult>> => {
        return createAPI<News001InitGetPersonalNewsListResult>({
            url: API.NEWS001_INIT_GET_PERSONAL_NEWS_LIST,
            data,
        });
    };

    /**
     * news001InitGetImportantNewsList
     * @param data News001InitGetImportantNewsListModel
     * @returns Promise<BaseResponse<News001InitGetImportantNewsListResult>>
     */
    static news001InitGetImportantNewsList = (
        data: News001InitGetImportantNewsListModel
    ): Promise<BaseResponse<News001InitGetImportantNewsListResult>> => {
        return createAPI<News001InitGetImportantNewsListResult>({
            url: API.NEWS001_INIT_GET_IMPORTANT_NEWS_LIST,
            data,
        });
    };

    /**
     * news002InitGetNewsDetail
     * @param data News002InitGetNewsDetailModel
     * @returns Promise<BaseResponse<News002InitGetNewsDetailResult>>
     */
    static news002InitGetNewsDetail = (
        data: News002InitGetNewsDetailModel
    ): Promise<BaseResponse<News002InitGetNewsDetailResult>> => {
        return createAPI<News002InitGetNewsDetailResult>({
            url: API.NEWS002_INIT_GET_NEWS_DETAIL,
            data,
        });
    };

    /**
     * news001InitGetImportantNewsUnreadFlag
     * @param data News001InitGetImportantNewsUnreadFlagModel
     * @returns Promise<BaseResponse<News001InitGetImportantNewsUnreadFlagResult>>
     */
    static news001InitGetImportantNewsUnreadFlag = (
        data: News001InitGetImportantNewsUnreadFlagModel
    ): Promise<BaseResponse<News001InitGetImportantNewsUnreadFlagResult>> => {
        return createAPI<News001InitGetImportantNewsUnreadFlagResult>({
            url: API.NEWS001_INIT_GET_IMPORTANT_NEWS_UNREAD_FLAG,
            data,
        });
    };

    /**
     * news001InitGetPersonalNewsUnreadFlag
     * @param data News001InitGetPersonalNewsUnreadFlagModel
     * @returns Promise<BaseResponse<News001InitGetPersonalNewsUnreadFlagResult>>
     */
    static news001InitGetPersonalNewsUnreadFlag = (
        data: News001InitGetPersonalNewsUnreadFlagModel
    ): Promise<BaseResponse<News001InitGetPersonalNewsUnreadFlagResult>> => {
        return createAPI<News001InitGetPersonalNewsUnreadFlagResult>({
            url: API.NEWS001_INIT_GET_PERSONAL_NEWS_UNREAD_FLAG,
            data,
        });
    };
}

export default NewsAPI;
