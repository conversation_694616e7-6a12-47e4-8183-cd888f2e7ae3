import { CommonCallback } from './common';

/**
 * MobileOrderHistory001MobileOrderGetOrderListModel
 */
export interface MobileOrderHistory001MobileOrderGetOrderListModel extends CommonCallback {
    // 加盟店ID
    StoreID: string;
}

/**
 * MobileOrderHistory001MobileOrderGetOrderListCartInfoList
 */
export interface MobileOrderHistory001MobileOrderGetOrderListCartInfoList {
    // 商品リスト
    ItemID: string;
    // 商品名
    ItemName: string;
    // 商品画像
    ItemImage?: string;
    // 金額
    Price: number;
    // 数量
    Quantity: number;
}

/**
 * MobileOrderHistory001MobileOrderGetOrderListCartInfo
 */
export interface MobileOrderHistory001MobileOrderGetOrderListCartInfo {
    // 売上金額
    SalesAmount: number;
    // 商品リスト
    ItemList: MobileOrderHistory001MobileOrderGetOrderListCartInfoList[];
    // 売上ID
    SalesID?: string;
}

/**
 * MobileOrderHistory001MobileOrderGetOrderListResult
 */
export interface MobileOrderHistory001MobileOrderGetOrderListResult {
    CartInfo: MobileOrderHistory001MobileOrderGetOrderListCartInfo;
}
