import { useCallback, useMemo } from 'react';
import { useDispatch } from 'react-redux';
import NewsAPI from '../apis/news';
import { BaseResponse } from '../models/common';
import {
    News001InitGetImportantNewsListModel,
    News001InitGetImportantNewsListResult,
    News001InitGetPersonalNewsListModel,
    News001InitGetPersonalNewsListResult,
} from '../models/news001-init-news-list';
import { apiCommon } from '../redux/actions/common';

interface UseNews {
    postNews001InitGetPersonalNewsList: (payload: News001InitGetPersonalNewsListModel) => void;
    postNews001InitGetImportantNewsList: (payload: News001InitGetImportantNewsListModel) => void;
}

/**
 * useNews
 */
const useNews = (): UseNews => {
    const dispatch = useDispatch();

    /**
     * postNews001InitGetPersonalNewsList
     * @param payload News001InitGetPersonalNewsListModel
     */
    const postNews001InitGetPersonalNewsList = useCallback(
        (payload: News001InitGetPersonalNewsListModel) => {
            // get payload
            const { successCallback, errorCallback } = payload || {};

            // create promiser
            const promiser = NewsAPI.news001InitGetPersonalNewsList(payload);

            /**
             * handleResponse
             * @param response BaseResponse<News001InitGetPersonalNewsListResult>
             */
            const handleResponse = (response: BaseResponse<News001InitGetPersonalNewsListResult>): void => {
                successCallback?.(response);
            };

            // dispatch apiCommon
            dispatch(
                apiCommon({
                    promiser,
                    successCallback: handleResponse,
                    errorCallback,
                })
            );
        },
        [dispatch]
    );

    /**
     * postNews001InitGetImportantNewsList
     * @param payload News001InitGetImportantNewsListModel
     */
    const postNews001InitGetImportantNewsList = useCallback(
        (payload: News001InitGetImportantNewsListModel) => {
            // get payload
            const { successCallback, errorCallback } = payload || {};

            // create promiser
            const promiser = NewsAPI.news001InitGetImportantNewsList(payload);

            /**
             * handleResponse
             * @param response BaseResponse<News001InitGetImportantNewsListResult>
             */
            const handleResponse = (response: BaseResponse<News001InitGetImportantNewsListResult>): void => {
                successCallback?.(response);
            };

            // dispatch apiCommon
            dispatch(
                apiCommon({
                    promiser,
                    successCallback: handleResponse,
                    errorCallback,
                })
            );
        },
        [dispatch]
    );

    return useMemo(
        () => ({ postNews001InitGetPersonalNewsList, postNews001InitGetImportantNewsList }),
        [postNews001InitGetImportantNewsList, postNews001InitGetPersonalNewsList]
    );
};

export default useNews;
