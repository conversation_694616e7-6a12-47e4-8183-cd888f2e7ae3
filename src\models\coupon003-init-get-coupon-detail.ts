import { CommonCallback } from './common';

/**
 * Coupon003InitGetCouponDetailModel
 */
export interface Coupon003InitGetCouponDetailModel extends CommonCallback {
    // 提示型クーポンID
    PresentationCouponID: string | undefined;
    // 1：利用 2:入手
    ProcessType?: number;
}

/**
 * Coupon003InitGetCouponDetailResult
 */
export interface Coupon003InitGetCouponDetailResult {
    Title: string;
    Address: string;
    Image: string;
    Content: string;
    StoreName: string;
    UsedFlag: boolean;
    EndDateTime: string;
    Notice: string;
    UsedDate?: string;
    PresentationCouponID?: string;
    GotFlag?: boolean;
}
