import { mount, shallow } from 'enzyme';
import Stepper, { StepperItem, StepperProps } from '../Stepper/Stepper';

/**
 * Unit test for Stepper component
 */
describe('Unit test for Stepper component', () => {
    /**
     * setup element
     * @param props StepperProps
     */
    const setup = (props: StepperProps) => {
        const s = shallow(<Stepper {...props} />);
        const m = mount(<Stepper {...props} />);

        return {
            shallow: s,
            mount: m,
            props,
        };
    };

    /**
     * display enough elements
     */
    describe('display enough elements', () => {
        /**
         * display enough stepper items with content of each step
         */
        it('display enough stepper items with content of each step', () => {
            const { mount } = setup({
                configs: [{ text: 'step 1' }, { text: 'step 2' }, { text: 'step 3' }, { text: 'step 4' }],
                step: 2,
            });
            expect(mount.find(StepperItem).length).toEqual(4);
            expect(mount.find(StepperItem).at(0).text()).toEqual('step 1');
            expect(mount.find(StepperItem).at(1).text()).toEqual('step 2');
            expect(mount.find(StepperItem).at(2).text()).toEqual('step 3');
            expect(mount.find(StepperItem).at(3).text()).toEqual('step 4');
        });

        /**
         * display stepper items with disablePreviousStep
         */
        it('display stepper items with disablePreviousStep', () => {
            const { mount } = setup({
                configs: [{ text: 'step 1' }, { text: 'step 2' }, { text: 'step 4' }, { text: 'step 4' }],
                step: 3,
                disablePreviousStep: true,
            });
            expect(mount.find(StepperItem).length).toEqual(4);
            expect(mount.find(StepperItem).find('div.stepper-item-main.disabled').length).toEqual(2);
        });

        /**
         * display stepper items with disableAllStep
         */
        it('display stepper items with disableAllStep', () => {
            const { mount } = setup({
                configs: [{ text: 'step 1' }, { text: 'step 2' }, { text: 'step 4' }, { text: 'step 4' }],
                step: 3,
                disableAllStep: true,
            });
            expect(mount.find(StepperItem).length).toEqual(4);
            expect(mount.find(StepperItem).find('div.stepper-item-main.disabled').length).toEqual(4);
        });
    });
});
