import { CommonCallback } from './common';

/**
 * ECCart001ECGetCartModel
 */
export type ECCart001ECGetCartModel = CommonCallback;

/**
 * ECCart001ECGetCartItem
 */
export interface ECCart001ECGetCartItem {
    // 加盟店ID
    StoreID: string;
    // 加盟店名
    StoreName: string;
    // 更新日時
    UpdateDateTime: string;
    // 決済状態
    SettlementStatus: string;
    // 商品リスト
    ItemList: ItemList[];
}

/**
 * ItemList
 */
export interface ItemList {
    // 商品ID
    ItemID: string;
    // 商品名
    ItemName: string;
    // 商品画像
    ItemImage: string;
    // 金額
    Price: number;
    // 数量
    Quantity: number;
    // 販売可能在庫数
    AvailableStock: number;
    // 商品削除フラグ
    IsItemDelete: boolean;
    // 商品更新フラグ
    IsItemUpdate: boolean;
    // IsStockOver
    IsStockOver: boolean;
    // 署名付きURL - 0:正常 1:商品削除 2:購入不可 3:販売開始前 4:販売終了
    ItemStatus: string;
}

/**
 * ECCart001ECGetCartResult
 */
export interface ECCart001ECGetCartResult {
    // 買い物かごリスト
    CartList: ECCart001ECGetCartItem[];
}
