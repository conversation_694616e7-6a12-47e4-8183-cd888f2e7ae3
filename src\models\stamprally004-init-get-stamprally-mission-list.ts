import { CommonCallback } from './common';

/**
 * StampRally004InitGetStampRallyMissionListModel
 */
export interface StampRally004InitGetStampRallyMissionListModel extends CommonCallback {
    // スタンプラリーID
    StampRallyID: string;
    // ミッションID
    MissionID: string;
    // 報酬ID
    RewardID?: string;
}

/**
 * StampRally004InitGetStampRallyMissionListResult
 */
export interface StampRally004InitGetStampRallyMissionListResult {
    // 終了日
    EndDateTime: string;
    // ミッション数
    MissionCount: number;
    // 順序制約フラグ
    OrderFlag: boolean;
    // 開催ステータスフラグ
    UrgingFlag: boolean;
    // ミッション画像
    MissionImage: string;
    // ミッション名称
    MissionName: string;
    // ミッション説明文
    MissionDescription: string;
    // 問い合わせ先
    InquireTo: string;
    // 住所
    Address: string;
    // 注意事項
    Notes: string;
    // ミッションクリア条件
    ClearRule: number;
    // ミッション区分
    MissionCategory?: number;
    // 必要クリアミッション数
    NeedClearMissionCount: number;
    // 達成報酬画像
    IncentiveImage?: string;
    // 達成報酬説明文
    IncentiveDescription?: string;
    // スタンプラリークリアフラグ
    CompleteStatus?: boolean;
    // クリアミッション数
    ClearMissionCount: number;
    // 達成日
    CompleteDateTime?: string;
    // 利用済フラグ
    UsedFlag: boolean;
    // 前回ミッションクリアフラグ
    BeforeMissionClearFlag: boolean;
    // 報酬取得日
    GetRewardDateTime?: string;
    // 達成報酬種別
    IncentiveType?: number;
    // 達成報酬ID
    IncentiveID?: string;
    // GetRewardID
    GetRewardID: string;
}
