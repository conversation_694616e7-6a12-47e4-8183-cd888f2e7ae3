import { CommonCallback } from './common';

/**
 * NftCollection002InitGetNftUserModel
 */
export interface NftCollection002InitGetNftUserModel extends CommonCallback {
    // NFTコンテンツID
    NFTContentID: string;
    // シリアルナンバー
    SerialNumber: number;
}

/**
 * AttributesItem
 */
export interface AttributesItem {
    // 発行日
    PublishDate: string;
    // 発行者
    Publisher: string;
    // カードID
    CardID: string;
    // カード名
    CardName: string;
    // キャラクター名
    CharacterName: string;
    // 声優名
    VoiceActorName: string;
    // レアリティ
    Rarity: string;
    // IP名
    IntellectualProperty: string;
    // 地域名
    AreaName: string;
    // コンテンツＵＲＩ（映像）
    ContentMovie: string;
    // コンテンツＵＲＩ（音声）
    ContentSound: string;
    // シリアル番号
    SerialNumber: string;
    // 版権元
    CopyrightHolder: string;
    // 制作者
    Creator: string;
}
/**
 * CertificateItem
 */
export interface CertificateItem {
    // スマートコントラクトアドレス
    SmartContractAddress: string;
    // TokenID
    TokenID: number;
    // トークン規格
    TokenStandard: string;
    // ブロックチェーン名
    BlockchainName: string;
    // 応援者名
    CheerName: string;
    // NFT名
    name: string;
    // NFT詳細
    description: string;
    // 画像URI
    image: string;
    // 外部URI
    external_url: string;
    // コントラクトアドレス
    NFTAddress: string;
    // ルーラコインattribute変数
    attributes: AttributesItem;
}

/**
 * NftCollection002InitGetNftUserResult
 */
export interface NftCollection002InitGetNftUserResult {
    // コンテンツ名
    ContentName: string;
    // コンテンツ画像
    ContentImage: string;
    // コンテンツ画像（サンプル）
    ContentImageSample: string;
    // コンテンツ音声
    ContentSound: string;
    // コンテンツ動画
    ContentMovie: string;
    // コンテンツタグ
    ContentTag: string[];
    // 総数
    Total: number;
    // 在庫数
    Stock: number;
    // 販売数
    SaledCount: number;
    // 加盟店名
    StoreName: string;
    // 加盟店住所
    StoreAddress: string;
    // 購入ステータス
    PurchasedStatus: number;
    // 認定証
    Certificate: CertificateItem;
    // カード情報
    NFTCardInfo: string;
    // 特典チケットフラグ(true：特典チケットあり、false：特典チケットなし)
    TicketFlg: boolean;
    // 先行販売中フラグ(true：先行販売中、false：先行販売期間外)
    PreSaleSalesFlg: boolean;
    // IPIDフラグ(true：IPID設定あり、false：IPID設定なし)
    IPIDFlg: boolean;
    // 二次流通チケット再配布フラグ(true：移転時にチケットを新たに配布する、false：移転時にチケットを配布しない)
    ResaleTicketFlg: boolean;
    // 所有状況区分
    Ownership: number;
    // コンテンツ画像（アクセス制限）
    ContentImageSigned: string;
    // コンテンツ音声（アクセス制限）
    ContentSoundSigned: string;
    // コンテンツ動画（アクセス制限）
    ContentMovieSigned: string;
    // 投稿メッセージ
    PostMessage: string;
    // 販売ページリンク
    NFTStoreUrl: string;
    // ハッシュタグ
    HashTag: string;
    // コンテンツ画像 X共有用
    ContentImageOGPTwitter: string;
    // SerialNumber add share X
    SerialNumber?: number;
}
