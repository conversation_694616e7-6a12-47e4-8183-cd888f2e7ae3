import { CommonCallback } from './common';

/**
 * Coupon001InitCouponListItem
 */
export interface Coupon001InitCouponListItem {
    // タイトル
    Title: string;
    // 画像
    Image: string;
    // 加盟店名
    StoreName: string;
    // 都道府県
    StorePrefectures: string;
    // 配付開始日時
    StartDateTime: string;
    // 利用開始日時
    UsedStartDateTime: string;
    // 有効期限
    EndDateTime: string;
    // 利用終了日時
    UsedEndDateTime: string;
    // 作成日時
    CreateDateTime: string;
    // クーポンID
    PresentationCouponID: string;
    // CouponTerm
    CouponTerm?: string;
    // 利用状況
    UsedFlag?: boolean;
}

/**
 * Coupon001InitCouponListModel
 */
export interface Coupon001InitCouponListModel extends CommonCallback {
    // 1ページあたりの取得アイテム数
    PageSize: number;
    // 排他開始キー
    // ※直前に実行したこのAPIのレスポンスPclLastEvaluatedKeyが含まれていた場合
    PclExclusiveStartKey?: string | null;
    // 排他開始キー
    // ※直前に実行したこのAPIのレスポンスOclLastEvaluatedKeyが含まれていた場合
    OclExclusiveStartKey?: string | null;
    // 利用状況
    UsedFlag?: boolean;
    // 利用状況
    GotFlag?: boolean;
}

/**
 * Coupon001InitCouponListResult
 */
export interface Coupon001InitCouponListResult {
    // クーポン一覧
    CouponList: Coupon001InitCouponListItem[];
    // クローズクーポン用のLastEvaluatedKey
    PclLastEvaluatedKey?: string;
    // オープンクーポン用のLastEvaluatedKey
    OclLastEvaluatedKey?: string;
}
