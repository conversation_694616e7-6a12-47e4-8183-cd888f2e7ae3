import Environments from '../../constants/environments';
import { AreaOCRModel } from '../../models/area-ocr';
import { AreaWalletCardModel } from '../../models/area-wallet-card';
import { AreaWalletCashReceiveRealModel } from '../../models/area-wallet-cash-receive-real';
import { AreaWalletCouponModel } from '../../models/area-wallet-coupon';
import { AreaWalletPaymentConfigGetModel } from '../../models/area-wallet-payment-config-get';
import { AreaWalletProductListResponse } from '../../models/area-wallet-productlist';
import { APIResponse } from '../../models/common';
import AreaAPI from '../area';

describe('Area APIs test', () => {
    it('areaWalletProductList should be called.', async () => {
        const spy = jest.spyOn(AreaAPI, 'areaWalletProductList');

        const response: APIResponse<AreaWalletProductListResponse> = {
            isSuccess: true,
            status: 0,
            statusCode: 200,
            result: {
                MaximumChargePrice: 100000,
                MaximumPrice: 10000,
                PopClientKey: 'this is mock pop client key',
                ProductItemList: [
                    {
                        ProductID: 0,
                        Price: 1000,
                        ProductRate: 100,
                        AreaCouponID: 0,
                    },
                ],
            },
        };

        spy.mockResolvedValue(response);

        const res = await AreaAPI.areaWalletProductList();

        expect(spy).toHaveBeenCalledTimes(1);
        expect(res.isSuccess).toBeTruthy();
    });

    it('areaWalletPaymentConfigGet should be called with enough params', () => {
        const params: AreaWalletPaymentConfigGetModel = {
            CheerID: 943,
        };

        const spy = jest.spyOn(AreaAPI, 'areaWalletPaymentConfigGet');

        AreaAPI.areaWalletPaymentConfigGet(params);

        expect(spy).toHaveBeenCalledTimes(1);

        expect(spy).toHaveBeenCalledWith(params);
    });

    it('areaWalletCashReceiveReal should be called with enough params', () => {
        const params: AreaWalletCashReceiveRealModel = {
            BrandID: Environments.brandID,
            ToCheerID: 943,
            WalletType: 'LimitAmountCheck',
            ProductID: 0,
            Price: 1000,
            AreaCouponID: 0,
            useLock: true,
        };

        const spy = jest.spyOn(AreaAPI, 'areaWalletCashReceiveReal');

        AreaAPI.areaWalletCashReceiveReal(params);

        expect(spy).toHaveBeenCalledTimes(1);

        expect(spy).toHaveBeenCalledWith(params);
    });

    it('areaWalletCard should be called with enough params', () => {
        const params: AreaWalletCardModel = {
            BrandID: Environments.brandID,
            CheerID: 943,
            ProductID: 0,
            Price: 1000,
            TotalAmount: 1100,
            AreaCouponID: 0,
            PaymentType: 'card',
        };

        const spy = jest.spyOn(AreaAPI, 'areaWalletCard');

        AreaAPI.areaWalletCard(params);

        expect(spy).toHaveBeenCalledTimes(1);

        expect(spy).toHaveBeenCalledWith(params);
    });

    it('areaOcr should ben called with enough params', () => {
        const params: AreaOCRModel = {
            SdkBytes: 'data:this_is_test_data',
        };
        const spy = jest.spyOn(AreaAPI, 'areaOCR');

        AreaAPI.areaOCR(params);

        expect(spy).toHaveBeenCalledTimes(1);

        expect(spy).toHaveBeenCalledWith(params);
    });

    it('areaWalletCoupon should be called with enough params', () => {
        const params: AreaWalletCouponModel = {
            BrandID: Environments.brandID,
            CheerID: 943,
            ProcessType: 0,
            AreaID: 1,
            StoreID: 1,
        };

        const spy = jest.spyOn(AreaAPI, 'areaWalletCoupon');

        AreaAPI.areaWalletCoupon(params);

        expect(spy).toHaveBeenCalledTimes(1);

        expect(spy).toHaveBeenCalledWith(params);
    });
});
