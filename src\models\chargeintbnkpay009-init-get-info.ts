import { CommonCallback } from './common';

/**
 * ChargeIntBnkPay009InitGetInfoModel
 */
export interface ChargeIntBnkPay009InitGetInfoModel extends CommonCallback {
    BankCode: number | string;
}

/**
 * BankList009Item
 */
export interface BankList009Item {
    // 支店コード
    BranchCode?: string | number;
    // 支店名
    BranchName?: string;
    // セイ
    LastName?: string;
    // メイ
    FirstName?: string;
}

/**
 * ChargeIntBnkPay009InitGetInfoResult
 */
export interface ChargeIntBnkPay009InitGetInfoResult {
    BankList: BankList009Item[];
    // セイ
    LastName?: string;
    // メイ
    FirstName?: string;
}
