import { CommonCallback } from './common';

/**
 * DAOCommunity002DaoGetMentionListModel
 */
export interface DAOCommunity002DaoGetMentionListModel extends CommonCallback {
    // DAOID
    DaoID: string;
}

/**
 * DAOCommunity002DaoGetMentionListItem
 */
export interface DAOCommunity002DaoGetMentionListItem {
    // 送信者名
    FromCheerName: string;
    // チャットID
    ChatID: string;
    // メンション番号（リアクションIDを含む）
    MentionNo: string;
    // リンクURL
    LinkUrl: string;
    // 送信日時
    PostDateTime: string;
    // 通知コード
    NotificationCode: number;
}

/**
 * DAOCommunity002DaoGetMentionListResult
 */
export interface DAOCommunity002DaoGetMentionListResult {
    // メンションリスト
    MentionList: DAOCommunity002DaoGetMentionListItem[];
}
