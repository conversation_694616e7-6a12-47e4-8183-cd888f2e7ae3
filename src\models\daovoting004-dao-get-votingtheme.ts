import { CommonCallback } from './common';

/**
 * DAOVoting004DaoGetVotingThemeModel
 */
export interface DAOVoting004DaoGetVotingThemeModel extends CommonCallback {
    // DAOID
    DaoID: string;
    // 投票テーマID
    ThemeID: string;
}

/**
 * DAOVoting004VotingCandidateItem
 */
export interface DAOVoting004VotingCandidateItem {
    // 投票候補内容
    CandidateContent: string;
    // 画像ファイル名
    ImageFileName?: string;
}

/**
 * DAOVoting004DaoGetVotingThemeResult
 */
export interface DAOVoting004DaoGetVotingThemeResult {
    DaoID: string;
    ThemeID: string;
    // タイトル
    Title: string;
    // 内容説明
    Explanation: string;
    // 投票開始日時
    VotingStartDateTime: string;
    // 投票終了日時
    VotingEndDateTime: string;
    // 投票形式コード
    VotingFormatCode: number;
    // 投票成立数
    VotingSuccessCount: number;
    // 1票換算GT
    ConversionGT: number;
    // 通常投票画像ファイル名
    NormalVotingImageFileName?: string;
    // 投票候補リスト
    VotingCandidateList: DAOVoting004VotingCandidateItem[];
}
