import { CommonCallback } from './common';

/**
 * EventFormType
 */
export interface EventFormType {
    FormID: string;
    // タイトル
    FormTitle: string;
    // 開始日時
    StartDateTime: string;
    // 終了日時
    EndDateTime: string;
    // 回答フラグ
    AnswerStatus: boolean;
}

/**
 * EventForm001InitGetEventFormListModel
 */
export interface EventForm001InitGetEventFormListModel extends CommonCallback {
    // ページサイズ
    PageSize: number;
    // 開催ステータス
    HeldStatus?: number;
    // 排他開始キー
    ExclusiveStartKey?: string | null | undefined;
}

/**
 * EventForm001InitGetEventFormListResult
 */
export interface EventForm001InitGetEventFormListResult {
    EventFormList: EventFormType[];
    LastEvaluatedKey?: string;
}
