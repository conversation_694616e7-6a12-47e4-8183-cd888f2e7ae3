import API from '../constants/api';
import { BaseResponse } from '../models/common';
import { Contact001InitGetEmailModel, Contact001InitGetEmailResult } from '../models/contact001-init-get-email';
import { Contact001MainSendInquiryModel } from '../models/contact001-main-send-inquiry';
import createAPI from './baseApi';

/**
 * ContactAPI
 */
class ContactAPI {
    /**
     * contact001MainSendInquiry
     * @param data Contact001MainSendInquiryModel
     * @returns Promise<BaseResponse>
     */
    static contact001MainSendInquiry = (data: Contact001MainSendInquiryModel): Promise<BaseResponse> => {
        return createAPI<BaseResponse>({
            url: API.CONTACT001_MAIN_SEND_INQUIRY,
            data,
        });
    };

    /**
     * contact001InitGetEmail
     * @param data Contact001InitGetEmailModel
     * @returns Promise<BaseResponse<Contact001InitGetEmailResult>>
     */
    static contact001InitGetEmail = (
        data: Contact001InitGetEmailModel
    ): Promise<BaseResponse<Contact001InitGetEmailResult>> => {
        return createAPI<Contact001InitGetEmailResult>({
            url: API.CONTACT001_INIT_GET_EMAIL,
            data,
        });
    };
}

export default ContactAPI;
