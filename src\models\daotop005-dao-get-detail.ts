import { CommonCallback } from './common';

/**
 * DAOTop005DaoGetDetailModel
 */
export interface DAOTop005DaoGetDetailModel extends CommonCallback {
    // DAOID
    DaoID: string;
    // BrandID
    BrandID: string;
}

/**
 * DAOTop005DaoGetDetailResult
 */
export interface DAOTop005DaoGetDetailResult {
    // DAO名
    DaoName: string;
    // 概要
    OverView: string;
    // 活動内容
    Content: string;
    // 参加者数
    ParticipantCount: number;
    // 発行GT
    IssueGTCount: number;
    // 自動割り当てGT
    AllocationGT: number;
    // Dao画像
    DaoImageFileName: string;
    // 目標
    Purpose: string;
    // ブロックチェーン利用フラグ
    BlockchainFlag: boolean;
}
