import { CommonCallback } from './common';

/**
 * History001InitGetHistoryListModel
 */
export interface History001InitGetHistoryListModel extends CommonCallback {
    MedalServiceID: string;
}

/**
 * MedalHistoryType
 */
export interface MedalHistoryType {
    ExpirationDay: string;
    HistoryAmount: number;
    HistoryDate: string;
    HistoryDetail: string;
    HistoryUnit: string;
    BalanceSeqID: string;
}

/**
 * MedalExpirationType
 */
export interface MedalExpirationType {
    ExpirationDate: string;
    LossMedal: number;
    AcquisitionDate: string;
}

/**
 * History001InitGetHistoryListResult
 */
export interface History001InitGetHistoryListResult {
    MedalHistory: MedalHistoryType[];
    MedalExpiration: MedalExpirationType[];
    MedalServiceID?: string;
}
