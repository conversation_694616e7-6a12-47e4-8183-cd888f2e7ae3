import API from '../constants/api';
import { BaseResponse } from '../models/common';
import { IdeaDao010MainCreateTaskModel, IdeaDao010MainCreateTaskResult } from '../models/ideadao010-main-create-task';

import {
    IdeaDao009InitGetTaskListModel,
    IdeaDao009InitGetTaskListResult,
} from '../models/ideadao009-init-get-task-list';
import createAPI from './baseApi';

/**
 * IdeaDaoAPI
 */
class IdeaDaoAPI {
    /**
     * ideaDao010MainCreateTask
     * @param data IdeaDao010MainCreateTaskModel
     * @returns Promise<BaseResponse<IdeaDao010MainCreateTaskResult>>
     */
    static ideaDao010MainCreateTask = (
        data: IdeaDao010MainCreateTaskModel
    ): Promise<BaseResponse<IdeaDao010MainCreateTaskResult>> => {
        return createAPI<IdeaDao010MainCreateTaskResult>({
            url: API.IDEADAO010_MAIN_CREATE_TASK,
            data,
        });
    };

    /**
     * ideaDao009InitGetTaskList
     * @param data IdeaDao009InitGetTaskListModel
     * @returns Promise<BaseResponse<IdeaDao009InitGetTaskListResult>>
     */
    static ideaDao009InitGetTaskList = (
        data: IdeaDao009InitGetTaskListModel
    ): Promise<BaseResponse<IdeaDao009InitGetTaskListResult>> => {
        return createAPI<IdeaDao009InitGetTaskListResult>({
            url: API.IDEADAO009_INIT_GET_TASK_LIST,
            data,
        });
    };
}

export default IdeaDaoAPI;
