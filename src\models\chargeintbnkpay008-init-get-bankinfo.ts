import { CommonCallback } from './common';

/**
 * ChargeIntBnkPay008InitGetBankInfo
 */
export type ChargeIntBnkPay008InitGetBankInfo = CommonCallback;

/**
 * ChargeIntBnkPay008InitGetBankInfoResult
 */
export interface ChargeIntBnkPay008InitGetBankInfoResult {
    // BankInfo
    BankList: BankListItem[];
}

/**
 * BankListModel
 */
export interface BankListItem {
    // 銀行コード
    BankCode?: string;
    // 銀行名
    BankName?: string;
    // 銀行画像ファイルパス
    BankImagePath?: string;
    // 支店コード
    BranchCode?: string;
    // 支店名
    BranchName?: string;
    // 預金種別（1:普通口座, 2:口座口座）
    DepositType?: number;
    // 口座番号（マスクした値）
    MaskedAccountNum?: string;
}
