import ColorThief from 'colorthief';
import { SyntheticEvent, useCallback, useMemo, useState } from 'react';
import Utils from '../../utils/utils';
import './styles.scss';

/**
 * ImageDominantProps
 */
interface ImageDominantProps {
    src?: string;
    height: number | string;
}

/**
 * ImageDominant
 * @param props ImageDominantProps
 * @returns JSX.Element
 */
const ImageDominant = (props: ImageDominantProps): JSX.Element => {
    const src = useMemo(() => props?.src, [props?.src]);
    const height = useMemo(() => props.height, [props.height]);

    const [bgColor, setBgColor] = useState<string>('#fff');

    /**
     * handleLoadImage
     * @param event SyntheticEvent<HTMLImageElement>
     */
    const handleLoadImage = useCallback((event: SyntheticEvent<HTMLImageElement>) => {
        const img = event?.target;

        if (img) {
            const colorThief = new ColorThief();
            const dominantColor = colorThief.getColor(img);
            const bg = Utils.rgbToHex(dominantColor?.[0], dominantColor?.[1], dominantColor?.[2]);
            setBgColor(bg);
        }
    }, []);

    return useMemo(
        () => (
            <div
                className="dominant-image-element"
                style={{
                    backgroundImage: bgColor ? `linear-gradient(rgb(255,255,255), ${bgColor}40)` : '#fff',
                    minHeight: height,
                    maxHeight: height,
                }}
            >
                {src && <img id="dominant-image" src={src} alt="" crossOrigin="anonymous" onLoad={handleLoadImage} />}
            </div>
        ),
        [bgColor, handleLoadImage, height, src]
    );
};

export default ImageDominant;
