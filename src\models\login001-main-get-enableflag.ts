import { CommonCallback } from './common';

/**
 * Login001MainGetLoginEnableFlagModel
 */
export interface Login001MainGetLoginEnableFlagModel extends CommonCallback {
    // ブランドID
    BrandID?: string;
}

/**
 * Login001MainGetLoginEnableFlagResult
 */
export interface Login001MainGetLoginEnableFlagResult {
    // ログイン可否
    LoginButtonEnableFlag: boolean;
    // ログイン開始日時
    LoginButtonReleaseDay: string;
    // ログイン終了日時
    LoginButtonCloseDay: string;
}
