import React, { useMemo, useRef, ReactNode, useEffect, useState, memo, useImperativeHandle } from 'react';
import Utils from '../../utils/utils';
import _ from 'lodash';

/**
 * FrameProps
 */
export interface FrameProps {
    /**
     * children
     */
    children?: ReactNode;
    /**
     * aspectRatio
     */
    aspectRatio?: {
        w: number;
        h: number;
    };
    frameWidth?: number;
    frameHeight?: number;
    className?: string;
}

/**
 * FrameForwardedRef
 */
export interface FrameForwardedRef {
    frameId: string;
    frameSize?: {
        width: number;
        height: number;
    };
}

/**
 * Frame component
 * @param props FrameProps
 * @returns React.JSX.Element
 */
const Frame = React.forwardRef((props: FrameProps, ref: React.ForwardedRef<FrameForwardedRef>): React.JSX.Element => {
    const { aspectRatio, children, frameWidth, frameHeight, className } = useMemo(() => props, [props]);

    // create frame id
    const frameId = useRef(Utils.uId());

    // frame ref
    const frameRef = useRef<HTMLDivElement>(null);

    // frame size
    const [frameSize, setFrameSize] = useState<DOMRectReadOnly>();

    useEffect(() => {
        if (frameRef?.current) {
            Utils.createAppObserver((entries: ResizeObserverEntry[]) => {
                const { contentRect } = _.last(entries) as ResizeObserverEntry;
                setFrameSize(contentRect);
            }).observe(frameRef.current);
        }
    }, []);

    // frame height
    const autoHeight = useMemo((): number | undefined => {
        if (aspectRatio) {
            const { w, h } = aspectRatio;
            return (frameWidth || frameSize?.width || 0) / (w / h);
        }
        return undefined;
    }, [aspectRatio, frameSize?.width, frameWidth]);

    useImperativeHandle(
        ref,
        () => ({
            frameId: frameId.current,
            frameSize,
        }),
        [frameSize]
    );

    return useMemo(
        () => (
            <div
                id={frameId.current}
                ref={frameRef}
                className={className}
                style={{
                    height: autoHeight || frameHeight,
                    width: frameWidth,
                    overflow: 'hidden',
                }}
            >
                {children}
            </div>
        ),
        [className, autoHeight, frameHeight, frameWidth, children]
    );
});

Frame.displayName = 'Frame';

export default memo(Frame);
