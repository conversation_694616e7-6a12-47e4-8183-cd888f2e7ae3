import { LinearProgress as LinearProgressMui, linearProgressClasses, LinearProgressProps } from '@mui/material';
import { styled } from '@mui/material/styles';

/**
 * LinearProgress
 */
const LinearProgress = styled((props: LinearProgressProps) => <LinearProgressMui {...props} />)(({ theme }) => ({
    height: 10,
    width: '100%',
    borderRadius: 5,
    [`&.${linearProgressClasses.colorPrimary}`]: {
        backgroundColor: theme.palette.grey[200],
        ...theme.applyStyles('dark', {
            backgroundColor: theme.palette.grey[800],
        }),
    },
    [`& .${linearProgressClasses.bar}`]: {
        borderRadius: 5,
        backgroundColor: '#1a90ff',
        ...theme.applyStyles('dark', {
            backgroundColor: '#308fe8',
        }),
    },
}));

export default LinearProgress;
