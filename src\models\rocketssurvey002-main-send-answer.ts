import { CommonCallback } from './common';

/**
 * Survey002AnswerItem
 */
export interface Survey002AnswerItem {
    // 質問ID
    QuestionID: string;
    // 選択回答ID
    ChoiceID: string[];
    // 自由記述回答内容
    FreeInputText: string;
}

/**
 * RocketsSurvey002MainSendAnswerModel
 */
export interface RocketsSurvey002MainSendAnswerModel extends CommonCallback {
    // アンケートID
    SurveyID: string;
    // 回答結果
    AnswerList: Survey002AnswerItem[];
}
