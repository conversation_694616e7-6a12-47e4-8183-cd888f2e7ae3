import { CommonCallback } from './common';

/**
 * EventForm002AnswerItem
 */
export interface EventForm002AnswerItem {
    // 質問ID
    QuestionID: string;
    // 選択回答ID
    ChoiceID: string[];
    // 自由記述回答内容
    FreeInputText: string;
}

/**
 * AnswerListType
 */
export interface AnswerListType {
    // 選択回答ID
    ChoiceID: string;
    // 選択回答内容
    Text: string;
    // 自由記述回答フラグ
    FreeInputFlag: boolean;
    // 次質問マッピング
    NextQuestionMapping?: string;
}

/**
 * EventForm002QuestionItem
 */
export interface EventForm002QuestionItem {
    // 質問ID
    QuestionID: string;
    // 質問タイプ
    QuestionType: string;
    // 必須フラグ
    RequireFlag: boolean;
    // 質問文
    Text: string;
    // フォーム選択回答内容
    AnswerList: AnswerListType[];
    // 優先順位フラグ
    PriorityRequiredFlag?: boolean;
    // 最大選択数オプション
    SelectableChoicesOperator?: string;
    // 最大選択数
    MaxSelectableChoices?: number;
}

/**
 * EventForm002InitGetEventFormDetailModel
 */
export interface EventForm002InitGetEventFormDetailModel extends CommonCallback {
    // フォームID
    FormID: string;
}

/**
 * EventForm002InitGetEventFormDetailResult
 */
export interface EventForm002InitGetEventFormDetailResult {
    // フォームID
    FormID: string;
    // タイトル
    FormTitle: string;
    // 説明
    FormDetail: string;
    // 開始日時
    StartDateTime: string;
    // 終了日時
    EndDateTime: string;
    // フォーム質問内容
    QuestionList: EventForm002QuestionItem[];
}
