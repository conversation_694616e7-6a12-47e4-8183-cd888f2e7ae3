import clsx from 'clsx';
import _ from 'lodash';
import React, {
    Dispatch,
    ForwardedRef,
    SetStateAction,
    forwardRef,
    useCallback,
    useImperativeHandle,
    useMemo,
    useRef,
    useState,
} from 'react';
import { Images } from '../../constants/resources';
import Button from '../Button';
import './styles.scss';

/**
 * ZodiacItem
 */
interface ZodiacItem {
    id: number;
    zodiacImage: string;
}

// Init DATA for PictureInfo
const ZODIAC_STEP_1: ZodiacItem[] = [
    { id: 1, zodiacImage: Images.ZODIAC.PIC_1_01 },
    { id: 2, zodiacImage: Images.ZODIAC.PIC_1_02 },
    { id: 3, zodiacImage: Images.ZODIAC.PIC_1_03 },
    { id: 4, zodiacImage: Images.ZODIAC.PIC_1_04 },
    { id: 5, zodiacImage: Images.ZODIAC.PIC_1_05 },
    { id: 6, zodiacImage: Images.ZODIAC.PIC_1_06 },
    { id: 7, zodiacImage: Images.ZODIAC.PIC_1_07 },
    { id: 8, zodiacImage: Images.ZODIAC.PIC_1_08 },
    { id: 9, zodiacImage: Images.ZODIAC.PIC_1_09 },
    { id: 10, zodiacImage: Images.ZODIAC.PIC_1_10 },
    { id: 11, zodiacImage: Images.ZODIAC.PIC_1_11 },
    { id: 12, zodiacImage: Images.ZODIAC.PIC_1_12 },
];

// Init DATA for PictureInfo
const ZODIAC_STEP_2: ZodiacItem[] = [
    { id: 1, zodiacImage: Images.ZODIAC.PIC_2_01 },
    { id: 2, zodiacImage: Images.ZODIAC.PIC_2_02 },
    { id: 3, zodiacImage: Images.ZODIAC.PIC_2_03 },
    { id: 4, zodiacImage: Images.ZODIAC.PIC_2_04 },
    { id: 5, zodiacImage: Images.ZODIAC.PIC_2_05 },
    { id: 6, zodiacImage: Images.ZODIAC.PIC_2_06 },
    { id: 7, zodiacImage: Images.ZODIAC.PIC_2_07 },
    { id: 8, zodiacImage: Images.ZODIAC.PIC_2_08 },
    { id: 9, zodiacImage: Images.ZODIAC.PIC_2_09 },
    { id: 10, zodiacImage: Images.ZODIAC.PIC_2_10 },
    { id: 11, zodiacImage: Images.ZODIAC.PIC_2_11 },
    { id: 12, zodiacImage: Images.ZODIAC.PIC_2_12 },
];

export type ZodiacSelectedValue = { [key: string]: number };

/**
 * ZodiacCurrentState
 */
export interface ZodiacCurrentState {
    /**
     * step
     */
    step: number;
    /**
     * value
     */
    value: ZodiacSelectedValue;
}

/**
 * ZodiacProps
 */
export interface ZodiacProps {
    /**
     * autoNextStep
     * If `true` footer button will be hide,
     * If `false`, display footer button
     */
    autoNextStep?: boolean;
    /**
     * autoNextDelay
     * delay time when autoNextDelay is true, default `500ms`
     * When step is 2, delay time is 100ms
     */
    autoNextDelay?: number;
    /**
     * buttonTitles
     * Custom button text for each step, available when autoNextStep is false
     * Default `text` for each step is `次へ`
     */
    buttonText?: string[];
    /**
     * useLoadingOnSubmit
     * show loading indicator when submit function is fired.
     */
    useLoadingOnSubmit?: boolean;
    /**
     * onSubmit
     * Action for footer button, fire when select enough two zodiac.
     */
    onSubmit?: (data: ZodiacSelectedValue) => void;
    /**
     * zodiacValues
     */
    zodiacValues?: [
        ZodiacCurrentState | undefined,
        React.Dispatch<React.SetStateAction<ZodiacCurrentState | undefined>>
    ];
}

export interface ZodiacForwardRef {
    /**
     * initState
     */
    initState: () => void;
    /**
     * getCurrentState
     */
    getCurrentState: () => ZodiacCurrentState;
    /**
     * setLoadingState
     * For autoNextStep = false
     */
    setLoadingState: Dispatch<SetStateAction<boolean>>;
}

/**
 * Common Zodiac component
 */
const Zodiac = forwardRef((props: ZodiacProps, ref: ForwardedRef<ZodiacForwardRef>): React.JSX.Element => {
    const {
        autoNextStep,
        onSubmit,
        buttonText,
        autoNextDelay = 500,
        useLoadingOnSubmit,
        zodiacValues,
    } = useMemo(() => props, [props]);

    // step
    const [step, setStep] = useState(1);

    // loading state
    const [isLoading, setLoading] = useState(false);

    // zodiac selected value
    const [zodiacSelectedValue, setZodiacSelectedValue] = useState<ZodiacSelectedValue>({});

    const zodiacSelectedValueRef = useRef<ZodiacSelectedValue>({});

    // zodiac display data
    const zodiacStep1 = useMemo(() => {
        return _.shuffle(ZODIAC_STEP_1);
    }, []);

    const zodiacStep2 = useMemo(() => {
        return _.shuffle(ZODIAC_STEP_2);
    }, []);

    // isDisabledButton
    const isDisabledButton = useMemo(() => {
        if (autoNextStep) {
            return true;
        }
        const key = `step${step}`;
        // true is value for current step is null/undefined
        return !zodiacSelectedValue?.[key];
    }, [autoNextStep, step, zodiacSelectedValue]);

    /**
     * nextStepHandler
     */
    const nextStepHandler = useCallback(() => {
        switch (step) {
            case 1: {
                // zodiacValue at 1 => dispatch function
                const dispatch = zodiacValues?.[1];
                // get current step
                const state: ZodiacCurrentState = {
                    step: 2,
                    value: zodiacSelectedValueRef.current,
                };
                dispatch?.(state);
                setStep(2);
                break;
            }
            case 2: {
                // set loading for button
                setLoading(true);
                // fire submit
                onSubmit?.(zodiacSelectedValueRef.current);
                break;
            }
            default:
                break;
        }
    }, [step, zodiacValues, onSubmit]);

    /**
     * onZodiacItemClickHandler
     * @param id number
     */
    const onZodiacItemClickHandler = useCallback(
        (id: number) => () => {
            // prevent re-select when auto next step is true
            if (autoNextStep && zodiacSelectedValueRef.current[`step${step}`]) {
                return;
            }
            // update value for current step for refer variable
            zodiacSelectedValueRef.current[`step${step}`] = id;

            // update value for state
            setZodiacSelectedValue((prev) => ({
                ...prev,
                [`step${step}`]: id,
            }));

            // auto next step
            if (autoNextStep) {
                setTimeout(() => {
                    nextStepHandler();
                }, autoNextDelay);
            }
        },
        [autoNextDelay, autoNextStep, nextStepHandler, step]
    );

    /**
     * renderZodiacItem
     * @params ZodiacItem
     */
    const renderZodiacItem = useCallback(
        (item: ZodiacItem) => {
            const stepValue = zodiacSelectedValue?.[`step${step}`];
            return (
                <div
                    className={clsx('zodiac-item', {
                        opacity: stepValue && stepValue !== item.id,
                    })}
                    key={item.id}
                    onClick={onZodiacItemClickHandler(item.id)}
                >
                    <img src={item.zodiacImage} width={60} />
                </div>
            );
        },
        [onZodiacItemClickHandler, step, zodiacSelectedValue]
    );

    /**
     * initStep
     * reset step to 1
     */
    const initState = useCallback(() => {
        // reset loading state
        setLoading(false);
        // clear value
        setZodiacSelectedValue({});
        zodiacSelectedValueRef.current = {};

        // reset step
        setStep(1);
    }, []);

    /**
     * getCurrentState
     * @returns ZodiacCurrentState
     */
    const getCurrentState = useCallback(() => {
        // get current step
        const state: ZodiacCurrentState = {
            step,
            value: zodiacSelectedValueRef.current,
        };
        return state;
    }, [step]);

    useImperativeHandle(
        ref,
        () => ({
            initState,
            getCurrentState,
            setLoadingState: setLoading,
        }),
        [getCurrentState, initState]
    );

    return useMemo(
        () => (
            <div className="common-zodiac-component">
                <div className="container">
                    <div className="d-flex justify-content-center align-items-center zodiac-container">
                        <div hidden={step !== 1}>
                            <div
                                className="d-flex flex-row flex-wrap justify-content-between"
                                style={{
                                    maxWidth: (60 + 32) * 3,
                                }}
                            >
                                {zodiacStep1.map(renderZodiacItem)}
                            </div>
                        </div>
                        <div hidden={step !== 2}>
                            <div
                                className="d-flex flex-row flex-wrap justify-content-between"
                                style={{
                                    maxWidth: (60 + 32) * 3,
                                }}
                            >
                                {zodiacStep2.map(renderZodiacItem)}
                            </div>
                        </div>
                    </div>
                    {!autoNextStep && (
                        <Button
                            onClick={nextStepHandler}
                            disabled={isDisabledButton}
                            className="w-100"
                            variant="contained"
                            isLoading={useLoadingOnSubmit && isLoading}
                            loadingProps={{
                                color: 'var(--app-base-color)',
                            }}
                        >
                            {buttonText?.[step - 1] || '次へ'}
                        </Button>
                    )}
                </div>
            </div>
        ),
        [
            autoNextStep,
            buttonText,
            isDisabledButton,
            isLoading,
            nextStepHandler,
            renderZodiacItem,
            step,
            useLoadingOnSubmit,
            zodiacStep1,
            zodiacStep2,
        ]
    );
});

Zodiac.displayName = 'Zodiac';

export default React.memo(Zodiac);
