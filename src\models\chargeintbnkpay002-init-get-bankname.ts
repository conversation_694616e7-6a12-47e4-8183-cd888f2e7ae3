import { CommonCallback } from './common';

export interface BankNameItem {
    // 五十音
    SortString: string;
    // 銀行コード
    BankCode: string;
    // 銀行名
    BankName: string;
}

/**
 * ChargeIntBnkPay002InitGetBankNameResult
 */
export interface ChargeIntBnkPay002InitGetBankNameResult {
    // 銀行リスト
    BankNameList: { [key: string]: BankNameItem[] };
}

/**
 * ChargeIntBnkPay002InitGetBankNameModel
 */
export type ChargeIntBnkPay002InitGetBankNameModel = CommonCallback;
