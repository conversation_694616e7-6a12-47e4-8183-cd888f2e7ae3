import { CommonCallback } from './common';

/**
 * DAOVoting002DaoGetVotingInfoModel
 */
export interface DAOVoting002DaoGetVotingInfoModel extends CommonCallback {
    // DAOID
    DaoID: string;
    // 投票テーマID
    ThemeID: string;
}

/**
 * DAOVoting002VotingCandidateItem
 */
export interface DAOVoting002VotingCandidateItem {
    // 投票候補番号
    CandidateNo: number;
    // 投票候補内容
    CandidateContent: string;
    // 画像ファイル名
    ImageFileName: string;
}

/**
 * DAOVoting002VotingCheerDestinationItem
 */
export interface DAOVoting002VotingCheerDestinationItem {
    // 投票候補番号
    CandidateNo: number;
}

/**
 * DAOVoting002DaoGetVotingInfoResult
 */
export interface DAOVoting002DaoGetVotingInfoResult {
    // タイトル
    Title: string;
    // 内容説明
    Explanation: string;
    // 投票開始日時（yyyy/MM/dd HH:mm）
    VotingStartDateTime: string;
    // 投票終了日時（yyyy/MM/dd HH:mm）
    VotingEndDateTime: string;
    // 投票形式コード
    VotingFormatCode: number;
    // 投票形式名
    VotingFormatName: string;
    // 投票成立数
    VotingSuccessCount: number;
    // 作成者名
    CreateCheerName: string;
    // 作成日時（yyyy/MM/dd HH:mm）
    CreateDateTime: string;
    // 投票数
    VotingCount: number;
    // 通常画像ファイル名
    NormalVotingImageFileName: string;
    // 投票候補リスト
    VotingCandidateList: DAOVoting002VotingCandidateItem[];
    // 投票者投票先リスト
    VotingCheerDestinationList: DAOVoting002VotingCheerDestinationItem[];
    // 投票済みフラグ
    IsVoted: boolean;
    // イベント状態コード
    EventStatusCode: number;
    // コントラクトアドレス
    ContractAddress: string;
    // トランザクションハッシュ
    TransactionHash: string;
    // 投票情報登録日時（yyyy/MM/dd HH:mm:ss）
    VotingResultWriteDateTime: string;
    // Dao名
    DaoName: string;
    // ブロックチェーンフラグ
    BlockchainFlag?: boolean;
}
