import { mount, shallow } from 'enzyme';
import Carousel from '../Carousel/Carousel';
import React from 'react';

const windowSpy = jest.spyOn(window, 'open');

/**
 * Unit test for Carousel component
 */
describe('Unit test for Carousel component', () => {
    /**
     * display enough elements
     */
    describe('display enough elements', () => {
        /**
         * should display 2 banner
         */
        it('should display 2 carosel items', () => {
            const wrapper = mount(
                <Carousel
                    data={[
                        {
                            FileName: 'https://via.placeholder.com/375x250',
                            LinkURL: 'https://google.com',
                            KeyVisualID: '1',
                        },
                        {
                            FileName: 'https://via.placeholder.com/375x250',
                            LinkURL: 'https://google.com',
                            KeyVisualID: '2',
                        },
                    ]}
                />
            );
            expect(wrapper.find('img.image-carousel').length).toEqual(2);
        });

        /**
         * should not display banner
         */
        it('should not display banner', () => {
            const wrapper = mount(<Carousel data={[]} />);
            expect(wrapper.find('img.image-carousel').length).toBe(0);
        });
    });

    /**
     * Check click image banner
     */
    describe('on click banner image', () => {
        beforeEach(() => {
            windowSpy.mockReset();
        });

        /**
         * should transition to LinkURL if available
         */
        it('should transition to LinkURL if available', () => {
            const wrapper = mount(
                <Carousel
                    data={[
                        {
                            FileName: 'https://via.placeholder.com/375x250',
                            LinkURL: 'https://google.com',
                            KeyVisualID: '1',
                        },
                        {
                            FileName: 'https://via.placeholder.com/375x250',
                            LinkURL: 'https://google.com',
                            KeyVisualID: '2',
                        },
                    ]}
                />
            );

            const imageBanner = wrapper.find('img.image-carousel').at(0);
            imageBanner.simulate('click');
            expect(windowSpy).toHaveBeenCalledWith('https://google.com', '_blank');
        });

        /**
         * should not transition if LinkURL is not available
         */
        it('should not transition if LinkURL is not available', () => {
            const wrapper = mount(
                <Carousel
                    data={[
                        {
                            FileName: 'https://via.placeholder.com/375x250',
                            KeyVisualID: '1',
                        },
                        {
                            FileName: 'https://via.placeholder.com/375x250',
                            KeyVisualID: '2',
                        },
                    ]}
                />
            );

            const imageBanner = wrapper.find('img.image-carousel').at(0);
            imageBanner.simulate('click');
            expect(windowSpy).not.toHaveBeenCalled();
        });
    });

    /**
     * should slide to prev item when click on ArrowLeft icon
     */
    it('should slide to prev item when click on ArrowLeft icon', () => {
        const slidePrev = jest.fn();

        jest.spyOn(React, 'useRef').mockReturnValueOnce({
            current: {
                slidePrev,
            },
        });

        const wrapper = shallow(
            <Carousel
                data={[
                    {
                        FileName: 'https://via.placeholder.com/375x250',
                        LinkURL: 'https://google.com',
                        KeyVisualID: '1',
                    },
                    {
                        FileName: 'https://via.placeholder.com/375x250',
                        LinkURL: 'https://google.com',
                        KeyVisualID: '2',
                    },
                ]}
            />
        );
        const button = wrapper.find('div.left').at(0);
        button.simulate('click');
        expect(slidePrev).toHaveBeenCalled();
    });

    /**
     * should slide to prev item when click on ArrowRight icon
     */
    it('should slide to prev item when click on ArrowRight icon', () => {
        const slideNext = jest.fn();

        jest.spyOn(React, 'useRef').mockReturnValueOnce({
            current: {
                slideNext,
            },
        });

        const wrapper = shallow(
            <Carousel
                data={[
                    {
                        FileName: 'https://via.placeholder.com/375x250',
                        LinkURL: 'https://google.com',
                        KeyVisualID: '1',
                    },
                    {
                        FileName: 'https://via.placeholder.com/375x250',
                        LinkURL: 'https://google.com',
                        KeyVisualID: '2',
                    },
                ]}
            />
        );
        const button = wrapper.find('div.right').at(0);
        button.simulate('click');
        expect(slideNext).toHaveBeenCalled();
    });

    /**
     * should not slide to prev item if swiperRef not available
     */
    it('should not slide to prev item if swiperRef not available', () => {
        const slidePrev = jest.fn();

        jest.spyOn(React, 'useRef').mockReturnValueOnce({
            current: null,
        });

        const wrapper = shallow(
            <Carousel
                data={[
                    {
                        FileName: 'https://via.placeholder.com/375x250',
                        LinkURL: 'https://google.com',
                        KeyVisualID: '1',
                    },
                    {
                        FileName: 'https://via.placeholder.com/375x250',
                        LinkURL: 'https://google.com',
                        KeyVisualID: '2',
                    },
                ]}
            />
        );
        const button = wrapper.find('div.left').at(0);
        button.simulate('click');
        expect(slidePrev).not.toHaveBeenCalled();
    });

    /**
     * should not slide to next item if swiperRef not available
     */
    it('should not slide to next item if swiperRef not available', () => {
        const slideNext = jest.fn();

        jest.spyOn(React, 'useRef').mockReturnValueOnce({
            current: null,
        });

        const wrapper = shallow(
            <Carousel
                data={[
                    {
                        FileName: 'https://via.placeholder.com/375x250',
                        LinkURL: 'https://google.com',
                        KeyVisualID: '1',
                    },
                    {
                        FileName: 'https://via.placeholder.com/375x250',
                        LinkURL: 'https://google.com',
                        KeyVisualID: '2',
                    },
                ]}
            />
        );
        const button = wrapper.find('div.right').at(0);
        button.simulate('click');
        expect(slideNext).not.toHaveBeenCalled();
    });

    /**
     * should hide prev button when have 1 item
     */
    it('should hide prev button when have 1 item', () => {
        const wrapper = shallow(
            <Carousel
                data={[
                    {
                        FileName: 'https://via.placeholder.com/375x250',
                        LinkURL: 'https://google.com',
                        KeyVisualID: '1',
                    },
                ]}
            />
        );
        const button = wrapper.find('div.left');
        expect(button).toHaveLength(0);
    });

    /**
     * should hide next button when have 1 item
     */
    it('should hide next button when have 1 item', () => {
        const wrapper = shallow(
            <Carousel
                data={[
                    {
                        FileName: 'https://via.placeholder.com/375x250',
                        LinkURL: 'https://google.com',
                        KeyVisualID: '1',
                    },
                ]}
            />
        );

        const button = wrapper.find('div.right');
        expect(button).toHaveLength(0);
    });
});
