import { CommonCallback } from './common';

/**
 * MainExecuteChargeModel
 */
export interface MainExecuteChargeModel extends CommonCallback {
    // メダルサービスID
    MedalServiceID: string;
    // チャージ金額
    Price: number;
    // チャージ種別
    PaymentType: string;
    // ProductID
    ProductID: number;
}

/**
 * ChargeCredit002MainExecuteChargeModel
 */
export type ChargeCredit002MainExecuteChargeModel = MainExecuteChargeModel;

/**
 * ChargeCnveni002MainExecuteChargeModel
 */
export type ChargeCnveni002MainExecuteChargeModel = MainExecuteChargeModel;

/**
 * ChargeIntBnk002MainExecuteChargeModel
 */
export type ChargeIntBnk002MainExecuteChargeModel = MainExecuteChargeModel;

/**
 * MainExecuteChargeResult
 */
export interface MainExecuteChargeResult {
    // ResultCode
    ResultCode: string;
    // Status
    Status: string;
    // Message
    Message: string;
    // OrderID
    OrderID: string;
    // PaymentKey
    PaymentKey: string;
    // PopClientKey
    PopClientKey: string;
}

/**
 * ChargeCredit002MainExecuteChargeResult
 */
export type ChargeCredit002MainExecuteChargeResult = MainExecuteChargeResult;

/**
 * ChargeCnveni002MainExecuteChargeResult
 */
export type ChargeCnveni002MainExecuteChargeResult = MainExecuteChargeResult;

/**
 * ChargeIntBnk002MainExecuteChargeResult
 */
export type ChargeIntBnk002MainExecuteChargeResult = MainExecuteChargeResult;
