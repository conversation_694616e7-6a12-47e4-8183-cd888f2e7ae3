import { Typography } from '@mui/material';
import { memo, useCallback, useMemo } from 'react';
import { useBlocker, useNavigate } from 'react-router-dom';
import Container from '../../../../components/Container';
import Icons from '../../../../components/Icons';
import Utils from '../../../../utils/utils';
import Button from '../../../../components/Button';
import Screens from '../../../../constants/screens';
import CommonAPI from '../../../../apis/common';
import { isBrowser, isTablet } from 'react-device-detect';

/**
 * ID: CHARGEINTBNKPAY004
 * Name: BankPay決済 (口座登録完了)
 * 口座登録完了画面
 * @returns React.JSX.Element
 */
const ChargeIntBnkPay004 = (): React.JSX.Element => {
    const navigate = useNavigate();

    /**
     * handleClickChargePageButton
     */
    const handleClickChargePageButton = useCallback(() => {
        // CHARGEINTBNKPAY005_BankPay決済 (金額入力) に遷移する
        CommonAPI.commonUserTransitionHistory({
            OriginScreenName: window.location.pathname,
            DestinationScreenName: Screens.CHARGEINT_BNKPAY005,
            ElementName: Utils.t('charge.bnk_pay.bnk_pay004.charge_page_btn')?.replaceAll('\n', ''),
            DeviceID: isBrowser ? 'PC' : isTablet ? 'Tablet' : 'mobile',
        });
        navigate(Screens.CHARGEINT_BNKPAY005);
    }, [navigate]);

    useBlocker((tx) => {
        if (tx.historyAction === 'POP') {
            navigate(Screens.WALLET001);
            return true;
        }
        return false;
    });

    return useMemo(
        () => (
            <Container
                screenName="ChargeIntBnkPay004"
                useHeader={{
                    title: Utils.t('charge.bnk_pay.bnk_pay004.title'),
                    type: 'withTitle',
                    isHiddenMenuNotification: true,
                }}
            >
                <div className="mt-4 charge-int-bnk-pay004">
                    <div className="d-flex justify-content-center mb-4">
                        <Icons.SuccessIcon width={100} height={100} />
                    </div>
                    <Typography
                        className="d-flex justify-content-center ff-noto-bold fs-20 text-center"
                        whiteSpace="pre"
                        color="#222"
                    >
                        {Utils.t('charge.bnk_pay.bnk_pay004.account_register_complete_info')}
                    </Typography>
                    <div className="app-fixed-element bottom">
                        <div className="p-3">
                            <Button
                                className="w-100"
                                variant="contained"
                                type="button"
                                onClick={handleClickChargePageButton}
                                name="chargePageButton"
                            >
                                {Utils.t('charge.bnk_pay.bnk_pay004.charge_page_btn')}
                            </Button>
                        </div>
                    </div>
                </div>
            </Container>
        ),
        [handleClickChargePageButton]
    );
};

export default memo(ChargeIntBnkPay004);
