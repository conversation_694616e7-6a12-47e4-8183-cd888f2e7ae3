import API from '../constants/api';
import { BaseResponse } from '../models/common';
import {
    MyNumber002InitGetEnvConfigIdModel,
    MyNumber002InitGetEnvConfigIdResult,
} from '../models/mynumber002-init-get-envconfigid';
import {
    MyNumber002InitGetUserCertStubModel,
    MyNumber002InitGetUserCertStubResult,
} from '../models/mynumber002-init-get-usercert-stub';
import {
    MyNumber002InitGetUserCertModel,
    MyNumber002InitGetUserCertResult,
} from '../models/mynumber002-init-get-usercert-test';
import {
    MyNumber003InitGetServiceScreenCallModel,
    MyNumber003InitGetServiceScreenCallResult,
} from '../models/mynumber003-init-get-servicescreencall-test';
import {
    MyNumber005InitGetCardInfoModel,
    MyNumber005InitGetCardInfoResult,
} from '../models/mynumber005-init-get-cardinfo-test';
import {
    MyNumber006InitGetStartScreenCallModel,
    MyNumber006InitGetStartScreenCallResult,
} from '../models/mynumber006-init-get-startscreencall-test';
import { MyNumber008InitGetMcasModel, MyNumber008InitGetMcasResult } from '../models/mynumber008-init-get-mcas-test';
import { MyNumber008MainUpdateMcasModel } from '../models/mynumber008-main-update-mcas-test';

import createAPI from './baseApi';

class MyNumberApiTest {
    /**
     * myNumber002InitGetUserCert
     * @param data MyNumber002InitGetUserCertModel
     * @returns Promise<BaseResponse<MyNumber002InitGetUserCertResult>>
     */
    static myNumber002InitGetUserCert = (
        data: MyNumber002InitGetUserCertModel
    ): Promise<BaseResponse<MyNumber002InitGetUserCertResult>> => {
        return createAPI({
            url: API.MYNUMBER002_INIT_GET_USERCERT_TEST,
            data,
        });
    };

    /**
     * myNumber002InitGetUserCertStub
     * @param data MyNumber002InitGetUserCertStubModel
     * @returns Promise<BaseResponse<MyNumber002InitGetUserCertStubResult>>
     */
    static myNumber002InitGetUserCertStub = (
        data: MyNumber002InitGetUserCertStubModel
    ): Promise<BaseResponse<MyNumber002InitGetUserCertStubResult>> => {
        return createAPI({
            url: API.MYNUMBER002_INIT_GET_USERCERT_STUB_TEST,
            data,
        });
    };

    /**
     * myNumber002InitGetEnvConfigId
     * @param data MyNumber002InitGetEnvConfigIdModel
     * @returns Promise<BaseResponse<MyNumber002InitGetEnvConfigIdResult>>
     */
    static myNumber002InitGetEnvConfigId = (
        data: MyNumber002InitGetEnvConfigIdModel
    ): Promise<BaseResponse<MyNumber002InitGetEnvConfigIdResult>> => {
        return createAPI({
            url: API.MYNUMBER002_INIT_GET_ENVCONFIGID_TEST,
            data,
        });
    };

    /**
     * myNumber003InitGetServiceScreenCall
     * @param data MyNumber003InitGetServiceScreenCallModel
     * @returns Promise<BaseResponse<MyNumber003InitGetServiceScreenCallResult>>
     */
    static myNumber003InitGetServiceScreenCall = (
        data: MyNumber003InitGetServiceScreenCallModel
    ): Promise<BaseResponse<MyNumber003InitGetServiceScreenCallResult>> => {
        return createAPI({
            url: API.MYNUMBER003_INIT_GET_SERVICESCREENCALL_TEST,
            data,
        });
    };

    /**
     * myNumber005InitGetCardInfo
     * @param data MyNumber005InitGetCardInfoModel
     * @returns Promise<BaseResponse<MyNumber005InitGetCardInfoResult>>
     */
    static myNumber005InitGetCardInfo = (
        data: MyNumber005InitGetCardInfoModel
    ): Promise<BaseResponse<MyNumber005InitGetCardInfoResult>> => {
        return createAPI({
            url: API.MYNUMBER005_INIT_GET_CARDINFO_TEST,
            data,
        });
    };

    /**
     * myNumber006InitGetStartScreenCall
     * @param data MyNumber006InitGetStartScreenCallModel
     * @returns Promise<BaseResponse<MyNumber006InitGetStartScreenCallResult>>
     */
    static myNumber006InitGetStartScreenCall = (
        data: MyNumber006InitGetStartScreenCallModel
    ): Promise<BaseResponse<MyNumber006InitGetStartScreenCallResult>> => {
        return createAPI({
            url: API.MYNUMBER006_INIT_GET_STARTSCREENCALL_TEST,
            data,
        });
    };

    /**
     * myNumber008InitGetMcas
     * @param data MyNumber008InitGetMcasModel
     * @returns Promise<BaseResponse<MyNumber008InitGetMcasResult>>
     */
    static myNumber008InitGetMcas = (
        data: MyNumber008InitGetMcasModel
    ): Promise<BaseResponse<MyNumber008InitGetMcasResult>> => {
        return createAPI({
            url: API.MYNUMBER008_INIT_GET_MCAS_TEST,
            data,
        });
    };

    /**
     * myNumber008MainUpdateMcas
     * @param data MyNumber008MainUpdateMcasModel
     * @returns Promise<BaseResponse>
     */
    static myNumber008MainUpdateMcas = (data: MyNumber008MainUpdateMcasModel): Promise<BaseResponse> => {
        return createAPI({
            url: API.MYNUMBER008_MAIN_UPDATE_MCAS_TEST,
            data,
        });
    };
}

export default MyNumberApiTest;
