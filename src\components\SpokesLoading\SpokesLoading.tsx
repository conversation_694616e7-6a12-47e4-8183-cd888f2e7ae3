import React, { useMemo } from 'react';
import './styles.scss';

/**
 * SpokesLoadingProps
 */
interface SpokesLoadingProps {
    color?: string;
}

/**
 * SpokesLoading
 * @param props SpokesLoadingProps
 * @returns React.JSX.Element
 */
const SpokesLoading = (props: SpokesLoadingProps): React.JSX.Element => {
    const color = useMemo(() => props?.color || 'var(--app-base-color)', [props?.color]);

    return useMemo(
        () => (
            <div className="loader-wheel">
                <span className="loader-wheel__spoke" style={{ background: color }} />
                <span className="loader-wheel__spoke" style={{ background: color }} />
                <span className="loader-wheel__spoke" style={{ background: color }} />
                <span className="loader-wheel__spoke" style={{ background: color }} />
                <span className="loader-wheel__spoke" style={{ background: color }} />
                <span className="loader-wheel__spoke" style={{ background: color }} />
                <span className="loader-wheel__spoke" style={{ background: color }} />
                <span className="loader-wheel__spoke" style={{ background: color }} />
            </div>
        ),
        [color]
    );
};

export default SpokesLoading;
