import { useCallback, useMemo } from 'react';
import { useDispatch } from 'react-redux';
import WalletAPI from '../apis/wallet';
import { BaseResponse } from '../models/common';
import {
    Wallet001InitGetBannerListModel,
    Wallet001InitGetBannerListResult,
} from '../models/wallet001-init-get-banner-list';
import { Wallet001InitGetCoinItem, Wallet001InitGetCoinModel } from '../models/wallet001-init-get-coin';
import {
    Wallet001InitGetCouponListModel,
    Wallet001InitGetCouponListResult,
} from '../models/wallet001-init-get-coupon-list';
import {
    Wallet001InitGetLimitedStoreListItem,
    Wallet001InitGetLimitedStoreListModel,
} from '../models/wallet001-init-get-limited-store-list';
import {
    Wallet001InitGetNewsUnreadLatestModel,
    Wallet001InitGetNewsUnreadLatestResult,
} from '../models/wallet001-init-get-news-unread-latest';
import {
    Wallet001InitGetRecommendationPopupDetailModel,
    Wallet001InitGetRecommendationPopupDetailResult,
} from '../models/wallet001-init-get-recommendation-popup-detail';
import {
    Wallet001InitGetUserTicketListModel,
    Wallet001InitGetUserTicketListResult,
} from '../models/wallet001-init-get-user-ticket-list';
import { Wallet001InitGetUserInfoModel, Wallet001InitGetUserInfoResult } from '../models/wallet001-init-get-userinfo';
import {
    Wallet001MainGetChargeAbleFlagModel,
    Wallet001MainGetChargeAbleFlagResult,
} from '../models/wallet001-main-get-chargeableflag';
import {
    Wallet001MainGetPayableFlagModel,
    Wallet001MainGetPayableFlagResult,
} from '../models/wallet001-main-get-payableflag';
import { apiCommon } from '../redux/actions/common';

interface UseWallet {
    postWallet001InitGetUserInfo: (payload: Wallet001InitGetUserInfoModel) => void;
    postWallet001InitGetNewsUnreadLatest: (payload: Wallet001InitGetNewsUnreadLatestModel) => void;
    postWallet001InitGetCoin: (payload: Wallet001InitGetCoinModel) => void;
    postWallet001InitGetUserTicketList: (payload: Wallet001InitGetUserTicketListModel) => void;
    postWallet001InitGetCouponList: (payload: Wallet001InitGetCouponListModel) => void;
    postWallet001InitGetLimitedStoreList: (payload: Wallet001InitGetLimitedStoreListModel) => void;
    postWallet001InitGetBannerList: (payload: Wallet001InitGetBannerListModel) => void;
    postWallet001MainGetPayableFlag: (payload: Wallet001MainGetPayableFlagModel) => void;
    postWallet001MainGetchargeAbleFlag: (payload: Wallet001MainGetChargeAbleFlagModel) => void;
    postWallet001InitGetRecommendationPopupDetail: (payload: Wallet001InitGetRecommendationPopupDetailModel) => void;
}

/**
 * UseWallet
 */
const useWallet = (): UseWallet => {
    const dispatch = useDispatch();

    /**
     * postWallet001InitGetUserInfo
     * @param payload Wallet001InitGetUserInfoModel
     */
    const postWallet001InitGetUserInfo = useCallback(
        (payload: Wallet001InitGetUserInfoModel) => {
            // get payload
            const { successCallback, errorCallback } = payload || {};

            // create promiser
            const promiser = WalletAPI.wallet001InitGetUserInfo(payload);

            /**
             * handleResponse
             * @param response BaseResponse<Wallet001InitGetUserInfoResult>
             */
            const handleResponse = (response: BaseResponse<Wallet001InitGetUserInfoResult>): void => {
                successCallback?.(response);
            };

            // dispatch apiCommon
            dispatch(
                apiCommon({
                    promiser,
                    successCallback: handleResponse,
                    errorCallback,
                })
            );
        },
        [dispatch]
    );

    /**
     * postWallet001InitGetNewsUnreadLatest
     * @param payload Wallet001InitGetNewsUnreadLatestModel
     */
    const postWallet001InitGetNewsUnreadLatest = useCallback(
        (payload: Wallet001InitGetNewsUnreadLatestModel) => {
            // get payload
            const { successCallback, errorCallback } = payload || {};

            // create promiser
            const promiser = WalletAPI.wallet001InitGetNewsUnreadLatest(payload);

            /**
             * handleResponse
             * @param response BaseResponse<Wallet001InitGetNewsUnreadLatestResult>
             */
            const handleResponse = (response: BaseResponse<Wallet001InitGetNewsUnreadLatestResult>): void => {
                successCallback?.(response);
            };

            // dispatch apiCommon
            dispatch(
                apiCommon({
                    promiser,
                    successCallback: handleResponse,
                    errorCallback,
                })
            );
        },
        [dispatch]
    );

    /**
     * postWallet001InitGetCoin
     * @param payload Wallet001InitGetCoinModel
     */
    const postWallet001InitGetCoin = useCallback(
        (payload: Wallet001InitGetCoinModel) => {
            // get payload
            const { successCallback, errorCallback } = payload || {};

            // create promiser
            const promiser = WalletAPI.wallet001InitGetCoin(payload);

            /**
             * handleResponse
             * @param response BaseResponse<Wallet001InitGetCoinItem[]>
             */
            const handleResponse = (response: BaseResponse<Wallet001InitGetCoinItem[]>): void => {
                successCallback?.(response);
            };

            // dispatch apiCommon
            dispatch(
                apiCommon({
                    promiser,
                    successCallback: handleResponse,
                    errorCallback,
                })
            );
        },
        [dispatch]
    );

    /**
     * postWallet001InitGetUserTicketList
     * @param payload Home001InitGetEventTicketListModel
     */
    const postWallet001InitGetUserTicketList = useCallback(
        (payload: Wallet001InitGetUserTicketListModel) => {
            // get payload
            const { successCallback, errorCallback } = payload || {};

            // create promiser
            const promiser = WalletAPI.wallet001InitGetUserTicketList(payload);

            /**
             * handleResponse
             * @param response BaseResponse<Wallet001InitGetUserTicketListResult>
             */
            const handleResponse = (response: BaseResponse<Wallet001InitGetUserTicketListResult>): void => {
                successCallback?.(response);
            };

            // dispatch apiCommon
            dispatch(
                apiCommon({
                    promiser,
                    successCallback: handleResponse,
                    errorCallback,
                })
            );
        },
        [dispatch]
    );

    /**
     * postWallet001InitGetCouponList
     * @param payload Wallet001InitGetCouponListModel
     */
    const postWallet001InitGetCouponList = useCallback(
        (payload: Wallet001InitGetCouponListModel) => {
            // get payload
            const { successCallback, errorCallback } = payload || {};

            // create promiser
            const promiser = WalletAPI.wallet001InitGetCouponList(payload);

            /**
             * handleResponse
             * @param response BaseResponse<Wallet001InitGetCouponListResult>
             */
            const handleResponse = (response: BaseResponse<Wallet001InitGetCouponListResult>): void => {
                successCallback?.(response);
            };

            // dispatch apiCommon
            dispatch(
                apiCommon({
                    promiser,
                    successCallback: handleResponse,
                    errorCallback,
                })
            );
        },
        [dispatch]
    );

    /**
     * postWallet001InitGetLimitedStoreList
     * @param payload Wallet001InitGetLimitedStoreListModel
     */
    const postWallet001InitGetLimitedStoreList = useCallback(
        (payload: Wallet001InitGetLimitedStoreListModel) => {
            // get payload
            const { successCallback, errorCallback } = payload || {};

            // create promiser
            const promiser = WalletAPI.wallet001InitGetLimitedStoreList(payload);

            /**
             * handleResponse
             * @param response BaseResponse<Home001InitGetBannerListResult>
             */
            const handleResponse = (response: BaseResponse<Wallet001InitGetLimitedStoreListItem[]>): void => {
                successCallback?.(response);
            };

            // dispatch apiCommon
            dispatch(
                apiCommon({
                    promiser,
                    successCallback: handleResponse,
                    errorCallback,
                })
            );
        },
        [dispatch]
    );

    /**
     * postWallet001InitGetBannerList
     * @param payload Wallet001InitGetBannerListModel
     */
    const postWallet001InitGetBannerList = useCallback(
        (payload: Wallet001InitGetBannerListModel) => {
            // get payload
            const { successCallback, errorCallback } = payload || {};

            // create promiser
            const promiser = WalletAPI.wallet001InitGetBannerList(payload);

            /**
             * handleResponse
             * @param response BaseResponse<Home001InitGetBannerListResult>
             */
            const handleResponse = (response: BaseResponse<Wallet001InitGetBannerListResult>): void => {
                successCallback?.(response);
            };

            // dispatch apiCommon
            dispatch(
                apiCommon({
                    promiser,
                    successCallback: handleResponse,
                    errorCallback,
                })
            );
        },
        [dispatch]
    );

    /**
     * postWallet001MainGetPayableFlag
     * @param payload Wallet001MainGetPayableFlagModel
     */
    const postWallet001MainGetPayableFlag = useCallback(
        (payload: Wallet001MainGetPayableFlagModel) => {
            // get payload
            const { successCallback, errorCallback } = payload || {};

            // create promiser
            const promiser = WalletAPI.wallet001MainGetPayableFlag(payload);

            /**
             * handleResponse
             * @param response BaseResponse<Wallet001MainGetPayableFlagResult>
             */
            const handleResponse = (response: BaseResponse<Wallet001MainGetPayableFlagResult>): void => {
                successCallback?.(response);
            };

            // dispatch apiCommon
            dispatch(
                apiCommon({
                    promiser,
                    successCallback: handleResponse,
                    errorCallback,
                })
            );
        },
        [dispatch]
    );

    /**
     * postWallet001MainGetchargeAbleFlag
     * @param payload Wallet001MainGetChargeAbleFlagModel
     */
    const postWallet001MainGetchargeAbleFlag = useCallback(
        (payload: Wallet001MainGetChargeAbleFlagModel) => {
            // get payload
            const { successCallback, errorCallback } = payload || {};

            // create promiser
            const promiser = WalletAPI.wallet001MainGetChargeAbleFlag(payload);

            /**
             * handleResponse
             * @param response BaseResponse<Wallet001MainGetChargeAbleFlagResult>
             */
            const handleResponse = (response: BaseResponse<Wallet001MainGetChargeAbleFlagResult>): void => {
                successCallback?.(response);
            };

            // dispatch apiCommon
            dispatch(
                apiCommon({
                    promiser,
                    successCallback: handleResponse,
                    errorCallback,
                })
            );
        },
        [dispatch]
    );

    /**
     * postWallet001InitGetRecommendationPopupDetail
     * @param payload Wallet001InitGetRecommendationPopupDetailModel
     */
    const postWallet001InitGetRecommendationPopupDetail = useCallback(
        (payload: Wallet001InitGetRecommendationPopupDetailModel) => {
            // get payload
            const { successCallback, errorCallback } = payload || {};

            // create promiser
            const promiser = WalletAPI.wallet001InitGetRecommendationPopupDetail(payload);

            /**
             * handleResponse
             * @param response BaseResponse<Wallet001InitGetRecommendationPopupDetailResult>
             */
            const handleResponse = (response: BaseResponse<Wallet001InitGetRecommendationPopupDetailResult>): void => {
                successCallback?.(response);
            };

            // dispatch apiCommon
            dispatch(
                apiCommon({
                    promiser,
                    successCallback: handleResponse,
                    errorCallback,
                })
            );
        },
        [dispatch]
    );

    return useMemo(
        () => ({
            postWallet001InitGetUserInfo,
            postWallet001InitGetNewsUnreadLatest,
            postWallet001InitGetCoin,
            postWallet001InitGetUserTicketList,
            postWallet001InitGetCouponList,
            postWallet001InitGetBannerList,
            postWallet001InitGetLimitedStoreList,
            postWallet001MainGetPayableFlag,
            postWallet001MainGetchargeAbleFlag,
            postWallet001InitGetRecommendationPopupDetail,
        }),
        [
            postWallet001InitGetUserInfo,
            postWallet001InitGetNewsUnreadLatest,
            postWallet001InitGetCoin,
            postWallet001InitGetUserTicketList,
            postWallet001InitGetCouponList,
            postWallet001InitGetBannerList,
            postWallet001InitGetLimitedStoreList,
            postWallet001MainGetPayableFlag,
            postWallet001MainGetchargeAbleFlag,
            postWallet001InitGetRecommendationPopupDetail,
        ]
    );
};

export default useWallet;
