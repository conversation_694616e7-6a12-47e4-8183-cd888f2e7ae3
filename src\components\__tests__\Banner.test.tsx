/* eslint-disable @typescript-eslint/explicit-function-return-type */
import { mount } from 'enzyme';
import Banner from '../Banner/Banner';

const windowSpy = jest.spyOn(window, 'open');

/**
 * Unit test for Banner component
 */
describe('Unit test for Banner component', () => {
    /**
     * display enough elements
     */
    describe('display enough elements', () => {
        /**
         * should display 2 banner
         */
        it('should display 2 banner', () => {
            const wrapper = mount(
                <Banner
                    data={[
                        {
                            FileName: 'https://via.placeholder.com/375x250',
                            LinkURL: 'https://google.com',
                            KeyVisualID: '1',
                        },
                        {
                            FileName: 'https://via.placeholder.com/375x250',
                            LinkURL: 'https://google.com',
                            KeyVisualID: '2',
                        },
                    ]}
                />
            );
            expect(wrapper.find('img.image-banner').length).toEqual(2);
        });

        /**
         * should not display banner
         */
        it('should not display banner', () => {
            const wrapper = mount(<Banner data={[]} />);
            expect(wrapper.find('img.image-banner').length).toBe(0);
        });

        /**
         * should display with custom with height
         */
        it('should display with custom with height', () => {
            const wrapper = mount(
                <Banner
                    data={[
                        {
                            FileName: 'https://via.placeholder.com/375x250',
                            LinkURL: 'https://google.com',
                            KeyVisualID: '1',
                        },
                        {
                            FileName: 'https://via.placeholder.com/375x250',
                            LinkURL: 'https://google.com',
                            KeyVisualID: '2',
                        },
                    ]}
                    height={50}
                    width={100}
                />
            );
            expect(wrapper).toBeTruthy();
            expect(wrapper.props().height).toBe(50);
            expect(wrapper.props().width).toBe(100);
        });
    });

    /**
     * Check click image banner
     */
    describe('on click banner image', () => {
        beforeEach(() => {
            windowSpy.mockReset();
        });

        /**
         * should transition to LinkURL if available
         */
        it('should transition to LinkURL if available', () => {
            const wrapper = mount(
                <Banner
                    data={[
                        {
                            FileName: 'https://via.placeholder.com/375x250',
                            LinkURL: 'https://google.com',
                            KeyVisualID: '1',
                        },
                        {
                            FileName: 'https://via.placeholder.com/375x250',
                            LinkURL: 'https://google.com',
                            KeyVisualID: '2',
                        },
                    ]}
                />
            );

            const imageBanner = wrapper.find('img.image-banner').at(0);
            imageBanner.simulate('click');
            expect(windowSpy).toHaveBeenCalledWith('https://google.com', '_blank');
        });

        /**
         * should not transition if LinkURL is not available
         */
        it('should not transition if LinkURL is not available', () => {
            const wrapper = mount(
                <Banner
                    data={[
                        {
                            FileName: 'https://via.placeholder.com/375x250',
                            KeyVisualID: '1',
                        },
                        {
                            FileName: 'https://via.placeholder.com/375x250',
                            KeyVisualID: '2',
                        },
                    ]}
                />
            );

            const imageBanner = wrapper.find('img.image-banner').at(0);
            imageBanner.simulate('click');
            expect(windowSpy).not.toHaveBeenCalled();
        });
    });
});
