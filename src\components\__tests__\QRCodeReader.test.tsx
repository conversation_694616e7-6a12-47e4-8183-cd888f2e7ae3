import { mount, shallow } from 'enzyme';
import React from 'react';
import QrCodeReader, { QrCodeReaderProps } from '../QrCodeReader/QrCodeReader';

/**
 * Unit test for QRCodeReader component
 */
describe('Unit test for QRCodeReader component', () => {
    /**
     * setup element
     * @param props QrCodeReaderProps
     */
    const setup = (props: QrCodeReaderProps) => {
        const r = React.createRef<HTMLDivElement>();
        const s = shallow(<QrCodeReader {...props} />);
        const m = mount(<QrCodeReader {...props} />);

        return {
            ref: r,
            shallow: s,
            mount: m,
            props,
        };
    };

    /**
     * display enough elements
     */
    describe('display enough elements', () => {
        const onScanResult = jest.fn();
        const { mount } = setup({ onScanResult, ViewFinder: <div className="qr-red-center-box" />, scanTimeout: 3000 });

        /**
         * display qr code reader wrapper
         */
        it('display qr code reader wrapper', () => {
            expect(mount.find('div#qr-code-reader-wrapper').length).toEqual(1);
        });

        /**
         * display video
         */
        it('display video', () => {
            expect(mount.find('video#qr-code-reader-video').length).toEqual(1);
        });

        /**
         * display view finder
         */
        it('display view finder', () => {
            expect(mount.find('div.qr-red-center-box').length).toEqual(1);
        });
    });

    /**
     * Actions
     */
    describe('Actions', () => {
        const onScanResult = jest.fn();
        setup({
            onScanResult,
            ViewFinder: <div className="qr-red-center-box" />,
            scanTimeout: 3000,
        });

        /**
         * read qr successfully
         */
        it('read qr successfully', () => {
            expect(onScanResult).toBeCalledTimes(1);
        });
    });
});
