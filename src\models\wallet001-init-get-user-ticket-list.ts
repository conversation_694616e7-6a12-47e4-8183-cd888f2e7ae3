import { CommonCallback } from './common';

/**
 * Wallet001InitGetUserTicketListModel
 */
export interface Wallet001InitGetUserTicketListModel extends CommonCallback {
    // 1ページあたりの取得アイテム数
    PageSize: number;
}

/**
 * Wallet001InitGetUserTicketListItem
 */
export interface Wallet001InitGetUserTicketListItem {
    // ユーザーイベントチケットID
    UserEventTicketID: string;
    // イベントチケット名
    EventTicketName: string;
    // イベントチケットトップ画像
    EventTicketTopImage: string;
    // イベントチケット入場時間
    EventTicketEntryTime: string;
    // イベントチケット退場時間
    EventTicketExitTime: string;
    // イベントチケット開催日
    EventTicketDateTime: string;
}

/**
 * Wallet001InitGetUserTicketListResult
 */
export interface Wallet001InitGetUserTicketListResult {
    // 所有チケット一覧
    UserTicketList: Wallet001InitGetUserTicketListItem[];
}
