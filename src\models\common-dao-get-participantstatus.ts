import { CommonCallback } from './common';

/**
 * CommonDaoGetParticipantStatusModel
 */
export interface CommonDaoGetParticipantStatusModel extends CommonCallback {
    // DaoID
    DaoID?: string;
    // 妄想DAOフラグ
    IdeaDaoFlag?: boolean;
}

/**
 * CommonDaoGetParticipantStatusResult
 */
export interface CommonDaoGetParticipantStatusResult {
    // 参加状態 0:参加済み 1:不参加（退出）2:参加取消
    ParticipantStatus: number;
    // メッセージ
    Message: string;
}
