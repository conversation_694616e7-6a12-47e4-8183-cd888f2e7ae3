import Switch, { SwitchProps } from '@mui/material/Switch';
import { styled } from '@mui/material/styles';

/**
 * SwitchIOS
 */
const SwitchIOS = styled((props: SwitchProps) => (
    <Switch focusVisibleClassName=".Mui-focusVisible" disableRipple {...props} />
))(({ theme }) => ({
    width: 77,
    height: 42,
    padding: 0,
    '& .MuiSwitch-switchBase': {
        padding: 0,
        margin: 5,
        transitionDuration: '300ms',
        '&.Mui-checked': {
            transform: 'translateX(35px)',
            color: '#fff',
            '& + .MuiSwitch-track': {
                backgroundColor: '#2B8272',
                opacity: 1,
                border: 0,
            },
            '&.Mui-disabled + .MuiSwitch-track': {
                opacity: 0.5,
            },
        },
        '&.Mui-focusVisible .MuiSwitch-thumb': {
            color: '#2B8272',
            border: '6px solid #fff',
        },
        '&.Mui-disabled .MuiSwitch-thumb': {
            color: theme.palette.mode === 'light' ? theme.palette.grey[100] : theme.palette.grey[600],
        },
        '&.Mui-disabled + .MuiSwitch-track': {
            opacity: theme.palette.mode === 'light' ? 0.7 : 0.3,
        },
    },
    '& .MuiSwitch-thumb': {
        boxSizing: 'border-box',
        width: 32,
        height: 32,
    },
    '& .MuiSwitch-track': {
        borderRadius: 42 / 2,
        backgroundColor: theme.palette.mode === 'light' ? '#E9E9EA' : '#39393D',
        opacity: 1,
        transition: theme.transitions.create(['background-color'], {
            duration: 500,
        }),
    },
}));

export default SwitchIOS;
