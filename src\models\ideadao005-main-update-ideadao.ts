import { CommonCallback } from './common';

/**
 * RevenueSharingRatioItem
 */
export interface RevenueSharingRatioItem {
    // 収益分配割合（発案者・応援者）
    InvatorAndCheers: number;
    // 収益分配割合（リーダー）
    Leaders: number;
    // 収益分配割合（メンバー・投資家（タスク））
    MembersAndInvestors: number;
}

/**
 * IdeaDao005MainUpdateIdeaDaoModel
 */
export interface IdeaDao005MainUpdateIdeaDaoModel extends CommonCallback {
    // DAOID
    DaoID: string;
    // DAO名
    DaoName: string;
    // 概要
    OverView: string;
    // DAO画像ファイル名
    DaoImageFileName: string;
    // 解決したい課題・叶えたい未来
    OurMission: string;
    // 求めるスキル
    RequiredSkill: string;
    // 収益分配割合
    RevenueSharingRatio: RevenueSharingRatioItem;
}
