import API from '../constants/api';
import { BaseResponse } from '../models/common';
import {
    DAOCommunity002DaoGetCategoryChannelModel,
    DAOCommunity002DaoGetCategoryChannelResult,
} from '../models/daocommunity002-dao-get-categorychannel';
import {
    DAOCommunity002DaoGetMentionListModel,
    DAOCommunity002DaoGetMentionListResult,
} from '../models/daocommunity002-dao-get-mentionlist';
import {
    DAOCommunity002DaoGetNoReadCountModel,
    DAOCommunity002DaoGetNoReadCountResult,
} from '../models/daocommunity002-dao-get-noreadcount';
import { DAOCommunity004DaoDeleteCategory } from '../models/daocommunity004-dao-delete-category';
import { DAOCommunity004DaoUpdateCategory } from '../models/daocommunity004-dao-update-category';
import { DAOCommunity005DaoPutChannel } from '../models/daocommunity005-dao-put-channel';
import { DAOCommunity006DaoDeleteChannel } from '../models/daocommunity006-dao-delete-channel';
import { DAOCommunity006DaoUpdateChannel } from '../models/daocommunity006-dao-update-channel';
import {
    DAOCommunity007DaoPutChatModel,
    DAOCommunity007DaoPutChatResult,
} from '../models/daocommunity007-dao-put-chat';
import { DAOCommunity008DaoDeleteChatModel } from '../models/daocommunity008-dao-delete-chat';
import { DAOCommunity008DaoDeleteReactionModel } from '../models/daocommunity008-dao-delete-reaction';
import {
    DAOCommunity008DaoGetChatModel,
    DAOCommunity008DaoGetChatResult,
} from '../models/daocommunity008-dao-get-chat';
import {
    DAOCommunity008DaoGetChatReadModel,
    DAOCommunity008DaoGetChatReadResult,
} from '../models/daocommunity008-dao-get-chatread';
import {
    DAOCommunity008DaoGetParticipantModel,
    DAOCommunity008DaoGetParticipantResult,
} from '../models/daocommunity008-dao-get-participant';
import {
    DAOCommunity008DaoGetReactionImageModel,
    DAOCommunity008DaoGetReactionImageResult,
} from '../models/daocommunity008-dao-get-reactionimage';
import {
    DAOCommunity008DaoGetThreadModel,
    DAOCommunity008DaoGetThreadResult,
} from '../models/daocommunity008-dao-get-thread';
import { DAOCommunity008DaoPutReactionModel } from '../models/daocommunity008-dao-put-reaction';
import {
    DAOCommunity008DaoUpdateChatModel,
    DAOCommunity008DaoUpdateChatResult,
} from '../models/daocommunity008-dao-update-chat';
import { DAOCommunity008DaoUpdateChatReadModel } from '../models/daocommunity008-dao-update-chatread';
import createAPI from './baseApi';

class DaoCommunityApi {
    /**
     * daoCommunity002DaoGetCategoryChannel
     * @param data DAOCommunity002DaoGetCategoryChannelModel
     * @returns Promise<BaseResponse<DAOCommunity002DaoGetCategoryChannelResult>>
     */
    static daoCommunity002DaoGetCategoryChannel = (
        data: DAOCommunity002DaoGetCategoryChannelModel
    ): Promise<BaseResponse<DAOCommunity002DaoGetCategoryChannelResult>> => {
        return createAPI<DAOCommunity002DaoGetCategoryChannelResult>({
            url: API.DAOCOMMUNITY002_DAO_GET_CATEGORY_CHANNEL,
            data,
        });
    };

    /**
     * daoCommunity002DaoGetMentionList
     * @param data DAOCommunity002DaoGetMentionListModel
     * @returns Promise<BaseResponse<DAOCommunity002DaoGetMentionListResult>>
     */
    static daoCommunity002DaoGetMentionList = (
        data: DAOCommunity002DaoGetMentionListModel
    ): Promise<BaseResponse<DAOCommunity002DaoGetMentionListResult>> => {
        return createAPI<DAOCommunity002DaoGetMentionListResult>({
            url: API.DAOCOMMUNITY002_DAO_GET_MENTION_LIST,
            data,
        });
    };

    /**
     * daoCommunity002DaoGetNoReadCount
     * @param data DAOCommunity002DaoGetNoReadCountModel
     * @returns Promise<BaseResponse<DAOCommunity002DaoGetNoReadCountResult>>
     */
    static daoCommunity002DaoGetNoReadCount = (
        data: DAOCommunity002DaoGetNoReadCountModel
    ): Promise<BaseResponse<DAOCommunity002DaoGetNoReadCountResult>> => {
        return createAPI<DAOCommunity002DaoGetNoReadCountResult>({
            url: API.DAOCOMMUNITY002_DAO_GET_NO_READ_COUNT,
            data,
        });
    };

    /**
     * daoCommunity004DaoUpdateCategory
     * @param data DAOCommunity004DaoUpdateCategory
     * @returns Promise<BaseResponse>
     */
    static daoCommunity004DaoUpdateCategory = (data: DAOCommunity004DaoUpdateCategory): Promise<BaseResponse> => {
        return createAPI<BaseResponse>({
            url: API.DAO_COMMUNITY004_DAO_UPDATE_CATEGORY,
            data,
        });
    };

    /**
     * daoCommunity004DaoDeleteCategory
     * @param data DAOCommunity004DaoDeleteCategory
     * @returns Promise<BaseResponse>
     */
    static daoCommunity004DaoDeleteCategory = (data: DAOCommunity004DaoDeleteCategory): Promise<BaseResponse> => {
        return createAPI<BaseResponse>({
            url: API.DAO_COMMUNITY004_DAO_DELETE_CATEGORY,
            data,
        });
    };

    /**
     * daoCommunity005DaoPutChannel
     * @param data DAOCommunity005DaoPutChannel
     * @returns Promise<BaseResponse>
     */
    static daoCommunity005DaoPutChannel = (data: DAOCommunity005DaoPutChannel): Promise<BaseResponse> => {
        return createAPI<BaseResponse>({
            url: API.DAO_COMMUNITY005_DAO_PUT_CHANNEL,
            data,
        });
    };

    /**
     * daoCommunity006DaoUpdateChannel
     * @param data DAOCommunity006DaoUpdateChannel
     * @returns Promise<BaseResponse>
     */
    static daoCommunity006DaoUpdateChannel = (data: DAOCommunity006DaoUpdateChannel): Promise<BaseResponse> => {
        return createAPI<BaseResponse>({
            url: API.DAO_COMMUNITY006_DAO_UPDATE_CHANNEL,
            data,
        });
    };

    /**
     * daoCommunity006DaoDeleteChannel
     * @param data DAOCommunity006DaoDeleteChannel
     * @returns Promise<BaseResponse>
     */
    static daoCommunity006DaoDeleteChannel = (data: DAOCommunity006DaoDeleteChannel): Promise<BaseResponse> => {
        return createAPI<BaseResponse>({
            url: API.DAO_COMMUNITY006_DAO_DELETE_CHANNEL,
            data,
        });
    };

    /**
     * daoCommunity008DaoGetThread
     * @param data DAOCommunity008DaoGetThreadModel
     * @returns Promise<BaseResponse<DAOCommunity008DaoGetThreadResult>>
     */
    static daoCommunity008DaoGetThread = (
        data: DAOCommunity008DaoGetThreadModel
    ): Promise<BaseResponse<DAOCommunity008DaoGetThreadResult>> => {
        return createAPI<DAOCommunity008DaoGetThreadResult>({
            url: API.DAO_COMMUNITY008_DAO_GET_THREAD,
            data,
        });
    };

    /**
     * daoCommunity008DaoGetChat
     * @param data DAOCommunity008DaoGetChatModel
     * @returns Promise<BaseResponse<DAOCommunity008DaoGetChatResult>>
     */
    static daoCommunity008DaoGetChat = (
        data: DAOCommunity008DaoGetChatModel
    ): Promise<BaseResponse<DAOCommunity008DaoGetChatResult>> => {
        return createAPI<DAOCommunity008DaoGetChatResult>({
            url: API.DAO_COMMUNITY008_DAO_GET_CHAT,
            data,
        });
    };

    /**
     * daoCommunity008DaoGetReactionImage
     * @param data DAOCommunity008DaoGetReactionImageModel
     * @returns Promise<BaseResponse<DAOCommunity008DaoGetReactionImageResult>>
     */
    static daoCommunity008DaoGetReactionImage = (
        data: DAOCommunity008DaoGetReactionImageModel
    ): Promise<BaseResponse<DAOCommunity008DaoGetReactionImageResult>> => {
        return createAPI<DAOCommunity008DaoGetReactionImageResult>({
            url: API.DAO_COMMUNITY008_DAO_GET_REACT_IMAGE,
            data,
        });
    };

    /**
     * daoCommunity008DaoGetChatRead
     * @param data DAOCommunity008DaoGetChatReadModel
     * @returns Promise<BaseResponse<DAOCommunity008DaoGetChatReadResult>>
     */
    static daoCommunity008DaoGetChatRead = (
        data: DAOCommunity008DaoGetChatReadModel
    ): Promise<BaseResponse<DAOCommunity008DaoGetChatReadResult>> => {
        return createAPI<DAOCommunity008DaoGetChatReadResult>({
            url: API.DAO_COMMUNITY008_DAO_GET_CHAT_READ,
            data,
        });
    };

    /**
     * daoCommunity008DaoUpdateChatRead
     * @param data DAOCommunity008DaoUpdateChatReadModel
     * @returns Promise<BaseResponse>
     */
    static daoCommunity008DaoUpdateChatRead = (data: DAOCommunity008DaoUpdateChatReadModel): Promise<BaseResponse> => {
        return createAPI({
            url: API.DAO_COMMUNITY008_DAO_UPDATE_CHAT_READ,
            data,
        });
    };

    /**
     * daoCommunity007DaoPutChat
     * @param data DAOCommunity007DaoPutChatModel
     * @returns Promise<BaseResponse<DAOCommunity007DaoPutChatResult>>
     */
    static daoCommunity007DaoPutChat = (
        data: DAOCommunity007DaoPutChatModel
    ): Promise<BaseResponse<DAOCommunity007DaoPutChatResult>> => {
        return createAPI<DAOCommunity007DaoPutChatResult>({
            url: API.DAO_COMMUNITY007_DAO_PUT_CHAT,
            data,
        });
    };

    /**
     * daoCommunity008DaoUpdateChat
     * @param data DAOCommunity008DaoUpdateChatModel
     * @returns Promise<BaseResponse<DAOCommunity008DaoUpdateChatResult>>
     */
    static daoCommunity008DaoUpdateChat = (
        data: DAOCommunity008DaoUpdateChatModel
    ): Promise<BaseResponse<DAOCommunity008DaoUpdateChatResult>> => {
        return createAPI<DAOCommunity008DaoUpdateChatResult>({
            url: API.DAO_COMMUNITY008_DAO_UPDATE_CHAT,
            data,
        });
    };

    /**
     * daoCommunity008DaoDeleteChat
     * @param data DAOCommunity008DaoDeleteChatModel
     * @returns Promise<BaseResponse>
     */
    static daoCommunity008DaoDeleteChat = (data: DAOCommunity008DaoDeleteChatModel): Promise<BaseResponse> => {
        return createAPI({
            url: API.DAO_COMMUNITY008_DAO_DELETE_CHAT,
            data,
        });
    };

    /**
     * daoCommunity008DaoDeleteReaction
     * @param data DAOCommunity008DaoDeleteReactionModel
     * @returns Promise<BaseResponse>
     */
    static daoCommunity008DaoDeleteReaction = (data: DAOCommunity008DaoDeleteReactionModel): Promise<BaseResponse> => {
        return createAPI({
            url: API.DAO_COMMUNITY008_DAO_DELETE_REACTION,
            data,
        });
    };

    /**
     * daoCommunity008DaoPutReaction
     * @param data DAOCommunity008DaoPutReactionModel
     * @returns Promise<BaseResponse>
     */
    static daoCommunity008DaoPutReaction = (data: DAOCommunity008DaoPutReactionModel): Promise<BaseResponse> => {
        return createAPI({
            url: API.DAO_COMMUNITY008_DAO_PUT_REACTION,
            data,
        });
    };

    /**
     * daoCommunity008DaoGetParticipant
     * @param data DAOCommunity008DaoGetParticipantModel
     * @returns Promise<BaseResponse<DAOCommunity008DaoGetParticipantResult>>
     */
    static daoCommunity008DaoGetParticipant = (
        data: DAOCommunity008DaoGetParticipantModel
    ): Promise<BaseResponse<DAOCommunity008DaoGetParticipantResult>> => {
        return createAPI<DAOCommunity008DaoGetParticipantResult>({
            url: API.DAO_COMMUNITY008_DAO_GET_PARTICIPANT,
            data,
        });
    };
}

export default DaoCommunityApi;
