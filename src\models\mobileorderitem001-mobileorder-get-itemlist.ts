import { CommonCallback } from './common';

/**
 * MobileOrderItem001MobileOrderGetItemListModel
 */
export interface MobileOrderItem001MobileOrderGetItemListModel extends CommonCallback {
    StoreID: string;
    LastSortNo: number;
    SearchText?: string;
    LimitCount: number;
    BrandID?: string;
}

/**
 * MobileOrderItem001MobileOrderItem
 */
export interface MobileOrderItem001MobileOrderItem {
    ItemID: string;
    ItemName: string;
    ImageFileName: string;
    TaxPrice: number;
    SortNo: number;
}

/**
 * MobileOrderItem001MobileOrderGetItemListResult
 */
export interface MobileOrderItem001MobileOrderGetItemListResult {
    ItemList: MobileOrderItem001MobileOrderItem[];
    IsNextDataExists: boolean;
}
