import { MenuItem, Select } from '@mui/material';
import { mount, shallow } from 'enzyme';
import { CommonError, RequiredLabel } from '../Common';
import Dropdown, { DropdownIcon, DropdownProps } from '../Dropdown/Dropdown';
import Icons from '../Icons/Icons';

const dataDropDown = [
    {
        label: '1',
        value: 1,
    },
    {
        label: '2',
        value: 2,
    },
    {
        label: '3',
        value: 3,
    },
];

/**
 * setup element
 * @param p DropdownProps
 */
const setup = (p?: Omit<DropdownProps, 'data' | 'keyValue' | 'keyDisplay'>): React.JSX.Element => {
    return <Dropdown {...p} data={dataDropDown} keyDisplay="label" keyValue="value" />;
};

/**
 * Unit test for Dropdown component
 */
describe('Unit test for Dropdown component', () => {
    /**
     * should render with default props
     */
    it('should render with default props', () => {
        const wrapper = shallow(setup());

        expect(wrapper.find(Select)).toBeTruthy();
    });

    /**
     * should render with initial value
     */
    it('should render with initial value', () => {
        const wrapper = shallow(
            setup({
                value: '2',
            })
        );

        expect(wrapper.find(Select)).toBeTruthy();
        expect(wrapper.find(Select).props().value).toBe('2');
    });

    /**
     * should render with custom placeholder
     */
    it('should render with custom placeholder', () => {
        const wrapper = shallow(
            setup({
                placeholder: 'this is mock placeholder',
            })
        );

        const menuItem = wrapper.find(MenuItem);
        expect(menuItem.at(0).text()).toBe('this is mock placeholder');
    });

    /**
     * should display with label
     */
    it('should display with label', () => {
        const wrapper = shallow(
            setup({
                label: 'this is mock label',
            })
        );

        const label = wrapper.find(RequiredLabel);
        expect(label).toBeTruthy();
    });

    /**
     * should display DropdownIcon
     */
    it('should display DropdownIcon', () => {
        const wrapper = shallow(setup());

        expect(wrapper.find(DropdownIcon)).toBeTruthy();
    });

    /**
     * should render DropdownIcon
     */
    it('should render DropdownIcon', () => {
        const wrapper = shallow(<DropdownIcon />);

        expect(wrapper.find(Icons.ArrowDownIcon)).toBeTruthy();
    });

    /**
     * should display with empty data
     */
    it('should display with empty data', () => {
        const data = undefined as any;
        const wrapper = shallow(<Dropdown data={data} keyDisplay="label" keyValue="value" />);

        expect(wrapper.find(Select)).toBeTruthy();
    });

    /**
     * should display with error
     */
    it('should display with error', () => {
        const wrapper = mount(
            setup({
                error: 'this is mock error',
            })
        );

        expect(wrapper.find(CommonError).text()).toBe('this is mock error');
    });

    /**
     * should display new value after select new item
     */
    it('should display new value after select new item', () => {
        const wrapper = shallow(
            setup({
                name: 'UT_dropdown',
            })
        );

        const select = wrapper.find(Select);
        select.simulate('change', {
            target: {
                name: 'UT_dropdown',
                value: '3',
            },
        });

        expect(wrapper.find(Select).props().value).toBe('3');
    });

    /**
     * should fire onChange event
     */
    it('should fire onChange event', () => {
        const onChange = jest.fn();
        const wrapper = shallow(
            setup({
                name: 'UT_dropdown',
                onChange,
            })
        );

        const select = wrapper.find(Select);
        select.simulate('change', {
            target: {
                name: 'UT_dropdown',
                value: '3',
            },
        });

        expect(onChange).toHaveBeenCalled();
    });
});
