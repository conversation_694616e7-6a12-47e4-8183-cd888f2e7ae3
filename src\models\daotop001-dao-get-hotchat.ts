import { CommonCallback } from './common';

/**
 * DAOTop001DaoGetHotChatModel
 */
export interface DAOTop001DaoGetHotChatModel extends CommonCallback {
    // DAOID
    DaoID: string;
}

/**
 * DAOTop001DaoGetHotChatItem
 */
export interface DAOTop001DaoGetHotChatItem {
    // チャットID
    ChatID: string;
    // 投稿内容
    Content: string;
    // 投稿者名
    PostCheerName: string;
    // 投稿日時
    PostDateTime: string;
    // プロフィール画像ファイル名
    ProfileImageFileName: string;
    // リンクURL
    LinkUrl: string;
    // リアクションリスト
    ReactionList: ReactionItem[];
}

/**
 * ReactionItem
 */
export interface ReactionItem {
    // チャットID
    ChatID: string;
    // リアクション画像ID
    ReactionImageID: string;
    // リアクション応援者ID
    ReactionCheerID: string;
}

/**
 * DAOTop001DaoGetHotChatResult
 */
export interface DAOTop001DaoGetHotChatResult {
    // チャットリスト
    ChatList: DAOTop001DaoGetHotChatItem[];
}
