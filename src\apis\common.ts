import API from '../constants/api';
import Environments from '../constants/environments';
import { BaseResponse, CommonCallback } from '../models/common';
import { CommonECGetCartExistsModel, CommonECGetCartExistsResult } from '../models/common-ec-get-cart-exists';
import { CommonECGetUrlModel, CommonECGetUrlResult } from '../models/common-ec-get-url';
import { CommonECUpdateCartModel, CommonECUpdateCartResult } from '../models/common-ec-update-cart';
import { CommonECUpdatePaymentExpiredModel } from '../models/common-ec-update-paymentexpired';
import { CommonGetMedalInfoModel, CommonGetMedalInfoResult } from '../models/common-get-medalinfo';
import { CommonInitGetOIDCModel, CommonInitGetOIDCResult } from '../models/common-init-get-oidc';
import { CommonInitGetUserInfoModel, CommonInitGetUserInfoResult } from '../models/common-init-get-userinfo';
import {
    CommonMobileOrderGetCartExistsModel,
    CommonMobileOrderGetCartExistsResult,
} from '../models/common-mobileorder-get-cartexists';
import { CommonMobileOrderGetUrlModel, CommonMobileOrderGetUrlResult } from '../models/common-mobileorder-get-url';
import {
    CommonMobileOrderUpdateCartModel,
    CommonMobileOrderUpdateCartResult,
} from '../models/common-mobileorder-update-cart';
import {
    CommonMyNumberInitGetMynaSettingNoauthModel,
    CommonMyNumberInitGetMynaSettingNoauthResult,
} from '../models/common-mynumber-init-get-mynasetting-noauth';
import { CommonRefreshTokenModel, CommonRefreshTokenResult } from '../models/common-refresh-token';
import { CommonTransferMainExecutePaymentModel } from '../models/common-transfer-main-execute-payment';
import {
    CommonTransferMainGetOrderIdModel,
    CommonTransferMainGetOrderIdResult,
} from '../models/common-transfer-main-get-orderid';
import {
    CommonTransferMainGetUserInfoModel,
    CommonTransferMainGetUserInfoResult,
} from '../models/common-transfer-main-get-userinfo';
import { CommonUserTransitionHistoryModel } from '../models/common-transition-history';
import { CommonWalletGetPaymentModel, CommonWalletGetPaymentResult } from '../models/common-wallet-get-payment';
import {
    CommonHeader001InitGetNewsUnreadFlagModel,
    CommonHeader001InitGetNewsUnreadFlagResult,
} from '../models/commonheader001-init-get-news-unread-flag';
import createAPI from './baseApi';

/**
 * CommonAPI
 */
class CommonAPI {
    /**
     * commonRefreshToken
     * @param data CommonRefreshTokenModel
     * @returns Promise<BaseResponse<CommonRefreshTokenResult>>
     */
    static commonRefreshToken = (data: CommonRefreshTokenModel): Promise<BaseResponse<CommonRefreshTokenResult>> => {
        return createAPI({
            url: API.COMMON_REFRESH_TOKEN,
            data,
        });
    };

    /**
     * commonUserTransitionHistory
     * @param data CommonUserTransitionHistoryModel
     * @returns Promise<BaseResponse<CommonUserTransitionHistoryResult>
     */
    static commonUserTransitionHistory = (data: CommonUserTransitionHistoryModel): void => {
        // return createAPI({
        //     url: API.COMMON_USER_TRANSITION_HISTORY,
        //     data: {
        //         // set default brandID
        //         BrandID: Environments.brandID,
        //         CheerID: Utils.getCheerID() || undefined,
        //         ...data,
        //     },
        // });
    };

    /**
     * commonECGetCartExists
     * @param data CommonECGetCartExistsModel
     * @returns Promise<BaseResponse<CommonECGetCartExistsResult>>
     */
    static commonECGetCartExists = (
        data?: CommonECGetCartExistsModel
    ): Promise<BaseResponse<CommonECGetCartExistsResult>> => {
        return createAPI({
            url: API.COMMON_EC_GET_CARTEXISTS,
            data,
        });
    };

    /**
     * commonECGetUrl
     * @param data CommonCallback
     * @returns Promise<BaseResponse<CommonECGetUrlResult>>
     */
    static commonECGetUrl = (data: CommonECGetUrlModel): Promise<BaseResponse<CommonECGetUrlResult>> => {
        return createAPI({
            url: API.COMMON_EC_GET_URL,
            data: {
                // set default brandID
                BrandID: Environments.brandID,
                ...data,
            },
        });
    };

    /**
     * commonECUpdateCart
     * @param data CommonECUpdateCartModel
     * @returns Promise<BaseResponse<CommonECUpdateCartResult>>
     */
    static commonECUpdateCart = (data: CommonECUpdateCartModel): Promise<BaseResponse<CommonECUpdateCartResult>> => {
        return createAPI({
            url: API.COMMON_EC_UPDATE_CART,
            data,
        });
    };

    /**
     * commonECUpdatePaymentExpired
     * @param data CommonECUpdatePaymentExpiredModel
     * @returns Promise<BaseResponse>
     */
    static commonECUpdatePaymentExpired = (data: CommonECUpdatePaymentExpiredModel): Promise<BaseResponse> => {
        return createAPI({
            url: API.COMMON_EC_UPDATE_PAYMENTEXPIRED,
            data,
        });
    };

    /**
     * commonWalletGetPayment
     * @param data CommonWalletGetPaymentModel
     * @returns Promise<BaseResponse>
     */
    static commonWalletGetPayment = (
        data: CommonWalletGetPaymentModel
    ): Promise<BaseResponse<CommonWalletGetPaymentResult>> => {
        return createAPI({
            url: API.COMMON_WALLET_GET_PAYMENT,
            data,
        });
    };

    /**
     * commonMobileOrderGetUrl
     * @param data CommonMobileOrderGetUrlModel
     * @returns Promise<BaseResponse<CommonMobileOrderGetUrlResult>>
     */
    static commonMobileOrderGetUrl = (
        data: CommonMobileOrderGetUrlModel
    ): Promise<BaseResponse<CommonMobileOrderGetUrlResult>> => {
        return createAPI<CommonMobileOrderGetUrlResult>({
            url: API.COMMON_MOBILE_ORDER_GET_URL,
            data: {
                BrandID: Environments.brandID,
                ...data,
            },
        });
    };

    /**
     * commonMobileOrderGetCartExists
     * @param data CommonMobileOrderGetCartExistsModel
     * @returns Promise<BaseResponse<CommonMobileOrderGetCartExistsResult>>
     */
    static commonMobileOrderGetCartExists = (
        data: CommonMobileOrderGetCartExistsModel
    ): Promise<BaseResponse<CommonMobileOrderGetCartExistsResult>> => {
        return createAPI({
            url: API.COMMON_MOBILEORDER_GET_CARTEXISTS,
            data,
        });
    };

    /**
     * commonMobileOrderUpdateCart
     * @param data CommonMobileOrderUpdateCartModel
     * @returns Promise<BaseResponse<CommonMobileOrderUpdateCartResult>>
     */
    static commonMobileOrderUpdateCart = (
        data: CommonMobileOrderUpdateCartModel
    ): Promise<BaseResponse<CommonMobileOrderUpdateCartResult>> => {
        return createAPI({
            url: API.COMMON_MOBILEORDER_UPDATE_CART,
            data,
        });
    };

    /**
     * commonHeader001InitGetNewsUnreadFlag
     * @param data CommonHeader001InitGetNewsUnreadFlagModel
     * @returns Promise<BaseResponse<CommonHeader001InitGetNewsUnreadFlagResult>>
     */
    static commonHeader001InitGetNewsUnreadFlag = (
        data: CommonHeader001InitGetNewsUnreadFlagModel
    ): Promise<BaseResponse<CommonHeader001InitGetNewsUnreadFlagResult>> => {
        return createAPI({
            url: API.COMMONHEADER001_INIT_GET_NEWS_UNREAD_FLAG,
            data,
        });
    };

    /**
     * commonInitGetUserInfo
     * @param data CommonInitGetUserInfoModel
     * @returns Promise<BaseResponse<CommonInitGetUserInfoResult>>
     */
    static commonInitGetUserInfo = (
        data: CommonInitGetUserInfoModel
    ): Promise<BaseResponse<CommonInitGetUserInfoResult>> => {
        return createAPI<CommonInitGetUserInfoResult>({
            url: API.COMMON_INIT_GET_USERINFO,
            data,
        });
    };

    /**
     * commonGetClientKey
     * @param data CommonCallback & { BrandID: string }
     * @returns Promise<BaseResponse<{ PopClientKey: string }>>
     */
    static commonGetClientKey = (
        data: CommonCallback & { BrandID: string }
    ): Promise<BaseResponse<{ PopClientKey: string }>> => {
        return createAPI({
            url: API.COMMON_INIT_GET_CLIENTKEY,
            data,
        });
    };

    /**
     * commonGetMedalInfo
     * @param data CommonInitGetUserInfoModel
     * @returns Promise<BaseResponse<CommonInitGetUserInfoResult>>
     */
    static commonGetMedalInfo = (data: CommonGetMedalInfoModel): Promise<BaseResponse<CommonGetMedalInfoResult>> => {
        return createAPI<CommonGetMedalInfoResult>({
            url: API.COMMON_GET_MEDALINFO,
            data,
        });
    };

    /**
     * commonTransferMainExecutePayment
     * @param data CommonTransferMainExecutePaymentModel
     * @returns Promise<BaseResponse>
     */
    static commonTransferMainExecutePayment = (data: CommonTransferMainExecutePaymentModel): Promise<BaseResponse> => {
        return createAPI({
            url: API.COMMON_TRANSFER_MAIN_EXECUTE_PAYMENT,
            data,
        });
    };

    /**
     * commonTransferMainGetUserInfo
     * @param data CommonTransferMainGetUserInfoModel
     * @returns Promise<BaseResponse<CommonTransferMainGetUserInfoResult>>
     */
    static commonTransferMainGetUserInfo = (
        data: CommonTransferMainGetUserInfoModel
    ): Promise<BaseResponse<CommonTransferMainGetUserInfoResult>> => {
        return createAPI<CommonTransferMainGetUserInfoResult>({
            url: API.COMMON_TRANSFER_MAIN_GET_USERINFO,
            data,
        });
    };

    /**
     * commonTransferMainGetOrderId
     * @param data CommonTransferMainGetOrderIdModel
     * @returns Promise<BaseResponse<CommonTransferMainGetOrderIdResult>>
     */
    static commonTransferMainGetOrderId = (
        data: CommonTransferMainGetOrderIdModel
    ): Promise<BaseResponse<CommonTransferMainGetOrderIdResult>> => {
        return createAPI<CommonTransferMainGetOrderIdResult>({
            url: API.COMMON_TRANSFER_MAIN_GET_ORDER_ID,
            data,
        });
    };

    /**
     * commonMyNumberInitGetMynaSettingNoauth
     * @param data CommonMyNumberInitGetMynaSettingNoauthModel
     * @returns Promise<BaseResponse<CommonMyNumberInitGetMynaSettingNoauthResult>>
     */
    static commonMyNumberInitGetMynaSettingNoauth = (
        data: CommonMyNumberInitGetMynaSettingNoauthModel
    ): Promise<BaseResponse<CommonMyNumberInitGetMynaSettingNoauthResult>> => {
        return createAPI<CommonMyNumberInitGetMynaSettingNoauthResult>({
            url: API.COMMON_MYNUMBER_INIT_GET_MYNASETTING_NOAUTH,
            data,
        });
    };

    /**
     * commonInitGetOIDC
     * @param data CommonInitGetOIDCModel
     * @returns Promise<BaseResponse<CommonInitGetOIDCResult>>
     */
    static commonInitGetOIDC = (data: CommonInitGetOIDCModel): Promise<BaseResponse<CommonInitGetOIDCResult>> => {
        return createAPI<CommonInitGetOIDCResult>({
            url: API.COMMON_INIT_GET_OIDC,
            data,
        });
    };
}

export default CommonAPI;
