import API from '../constants/api';
import { Brand001InitGetBrandListModel, Brand001InitGetBrandListResult } from '../models/brand001-init-get-brand-list';
import { Brand002MainUpdateBrandModel } from '../models/brand002-main-update-brand';
import { BaseResponse } from '../models/common';
import createAPI from './baseApi';

/**
 * BrandAPI
 */
class BrandAPI {
    /**
     * brand001InitGetBrandList
     * @param data Brand001InitGetBrandListModel
     * @returns Promise<BaseResponse<Brand001InitGetBrandListResult>>
     */
    static brand001InitGetBrandList = (
        data: Brand001InitGetBrandListModel
    ): Promise<BaseResponse<Brand001InitGetBrandListResult>> => {
        return createAPI<Brand001InitGetBrandListResult>({
            url: API.BRAND001_INIT_GET_BRAND_LIST,
            data,
        });
    };

    /**
     * brand002MainUpdateBrand
     * @param data Brand002MainUpdateBrandModel
     * @returns Promise<BaseResponse>
     */
    static brand002MainUpdateBrand = (data: Brand002MainUpdateBrandModel): Promise<BaseResponse> => {
        return createAPI<BaseResponse>({
            url: API.BRAND002_MAIN_UPDATE_BRAND,
            data,
        });
    };
}

export default BrandAPI;
