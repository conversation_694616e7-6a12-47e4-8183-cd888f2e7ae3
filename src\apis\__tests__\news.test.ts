import { APIResponse } from '../../models/common';
import {
    News001InitImportantNewsListModel,
    News001InitImportantNewsListResult,
    News001InitPersonalNewsListModel,
    News001InitPersonalNewsListResult,
} from '../../models/news001-init-news-list';
import { News002InitNewsDetailModel, News002InitNewsDetailResult } from '../../models/news002-init-news-detail';
import NewsAPI from '../news';

/**
 * Unit test for News APIs
 */
describe('Unit test for News APIs', () => {
    /**
     * news001InitPersonalNewsList should be called with enough params
     */
    it('news001InitPersonalNewsList should be called with enough params', async () => {
        const params: News001InitPersonalNewsListModel = { PageSize: 5, ExclusiveStartKey: 'pml0000000000000006' };

        const spy = jest.spyOn(NewsAPI, 'news001InitPersonalNewsList');

        NewsAPI.news001InitPersonalNewsList(params);

        expect(spy).toHaveBeenCalledTimes(1);

        expect(spy).toHaveBeenCalledWith(params);
    });

    /**
     * news001InitPersonalNewsList should be called successfully
     */
    it('news001InitPersonalNewsList should be called successfully', async () => {
        const params: News001InitPersonalNewsListModel = { PageSize: 5, ExclusiveStartKey: 'pml0000000000000006' };

        const response: APIResponse<News001InitPersonalNewsListResult> = {
            isSuccess: true,
            status: 0,
            statusCode: 200,
            result: {
                NewsList: [
                    {
                        PublishDateTime: '2023/01/01 00:10',
                        Subject: '2023年1月1にメンテナンスを行います',
                        ReadFlag: true,
                        MessageID: 'pml0000000000000010',
                    },
                    {
                        PublishDateTime: '2023/01/01 00:09',
                        Subject: '2023年1月1にメンテナンスを行います',
                        ReadFlag: true,
                        MessageID: 'pml0000000000000009',
                    },
                    {
                        PublishDateTime: '2023/01/01 00:08',
                        Subject: '2023年1月1にメンテナンスを行います',
                        ReadFlag: false,
                        MessageID: 'pml0000000000000008',
                    },
                    {
                        PublishDateTime: '2023/01/01 00:07',
                        Subject: '2023年1月1にメンテナンスを行います',
                        ReadFlag: true,
                        MessageID: 'pml0000000000000007',
                    },
                    {
                        PublishDateTime: '2023/01/01 00:06',
                        Subject: '2023年1月1にメンテナンスを行います',
                        ReadFlag: false,
                        MessageID: 'pml0000000000000006',
                    },
                ],
                LastEvaluatedKey: 'pml0000000000000006',
            },
        };

        const spy = jest.spyOn(NewsAPI, 'news001InitPersonalNewsList');

        spy.mockResolvedValue(response);

        const res = await NewsAPI.news001InitPersonalNewsList(params);

        expect(spy).toHaveBeenCalledTimes(2);

        expect(spy).toHaveBeenCalledWith(params);

        expect(res.isSuccess).toBeTruthy();
    });

    /**
     * news001InitImportantNewsList should be called with enough params
     */
    it('news001InitImportantNewsList should be called with enough params', async () => {
        const params: News001InitImportantNewsListModel = { PageSize: 5, ExclusiveStartKey: 'iml0000000000000005' };

        const spy = jest.spyOn(NewsAPI, 'news001InitImportantNewsList');

        NewsAPI.news001InitImportantNewsList(params);

        expect(spy).toHaveBeenCalledTimes(1);

        expect(spy).toHaveBeenCalledWith(params);
    });

    /**
     * news001InitImportantNewsList should be called successfully
     */
    it('news001InitImportantNewsList should be called successfully', async () => {
        const params: News001InitImportantNewsListModel = { PageSize: 5, ExclusiveStartKey: 'iml0000000000000005' };

        const response: APIResponse<News001InitImportantNewsListResult> = {
            isSuccess: true,
            status: 0,
            statusCode: 200,
            result: {
                NewsList: [
                    {
                        PublishDateTime: '2023/01/01 00:10',
                        Subject: '2023年1月1にメンテナンスを行います',
                        ReadFlag: true,
                        MessageID: 'iml0000000000000010',
                    },
                    {
                        PublishDateTime: '2023/01/01 00:09',
                        Subject: '2023年1月1にメンテナンスを行います',
                        ReadFlag: true,
                        MessageID: 'iml0000000000000009',
                    },
                    {
                        PublishDateTime: '2023/01/01 00:08',
                        Subject: '2023年1月1にメンテナンスを行います',
                        ReadFlag: false,
                        MessageID: 'iml0000000000000008',
                    },
                    {
                        PublishDateTime: '2023/01/01 00:07',
                        Subject: '2023年1月1にメンテナンスを行います',
                        ReadFlag: false,
                        MessageID: 'iml0000000000000007',
                    },
                    {
                        PublishDateTime: '2023/01/01 00:06',
                        Subject: '2023年1月1にメンテナンスを行います',
                        ReadFlag: false,
                        MessageID: 'iml0000000000000006',
                    },
                ],
                LastEvaluatedKey: 'iml0000000000000006',
            },
        };

        const spy = jest.spyOn(NewsAPI, 'news001InitImportantNewsList');

        spy.mockResolvedValue(response);

        const res = await NewsAPI.news001InitImportantNewsList(params);

        expect(spy).toHaveBeenCalledTimes(2);

        expect(spy).toHaveBeenCalledWith(params);

        expect(res.isSuccess).toBeTruthy();
    });

    /**
     * news002InitNewsDetail should be called with enough params
     */
    it('news002InitNewsDetail should be called with enough params', async () => {
        const params: News002InitNewsDetailModel = { MessageID: 'message-id' };

        const spy = jest.spyOn(NewsAPI, 'news002InitNewsDetail');

        NewsAPI.news002InitNewsDetail(params);

        expect(spy).toHaveBeenCalledTimes(1);

        expect(spy).toHaveBeenCalledWith(params);
    });

    /**
     * news002InitNewsDetail should be called successfully
     */
    it('news002InitNewsDetail should be called successfully', async () => {
        const params: News002InitNewsDetailModel = { MessageID: 'message-id' };

        const response: APIResponse<News002InitNewsDetailResult> = {
            isSuccess: true,
            status: 0,
            statusCode: 200,
            result: {
                PublishDateTime: '2022/05/14 15:34',
                Subject: '2022年5月20日にメンテナンスを行います',
                Body: `# 2022年5月20にメンテナンスを行います
                ## コンテンツ見出し 16px
                2020年11月21日と22日に開催されるVリーグの先行チケットの販売が開始しました。 #先行販売
                ![sampleImage](https://bucket.cheermedal.com/20220520/sampleImage.png)`,
            },
        };

        const spy = jest.spyOn(NewsAPI, 'news002InitNewsDetail');

        spy.mockResolvedValue(response);

        const res = await NewsAPI.news002InitNewsDetail(params);

        expect(spy).toHaveBeenCalledTimes(2);

        expect(spy).toHaveBeenCalledWith(params);

        expect(res.isSuccess).toBeTruthy();
    });
});
