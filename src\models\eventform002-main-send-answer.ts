import { CommonCallback } from './common';

/**
 * ChoiceItem
 */
export type ChoiceItem = {
    // AnswerRank
    AnswerRank?: number;
    // 選択回答ID
    ChoiceID?: string;
    // 自由記述回答内容
    FreeInputText?: string;
};

/**
 * MainSendAnswerItem
 */
export interface MainSendAnswerItem {
    // 質問ID
    QuestionID: string;
    // ChoiceList
    ChoiceList: ChoiceItem[];
}

/**
 * EventForm002MainSendAnswerModel
 */
export interface EventForm002MainSendAnswerModel extends CommonCallback {
    // フォームID
    FormID: string;
    // 回答結果
    AnswerList: MainSendAnswerItem[];
}
