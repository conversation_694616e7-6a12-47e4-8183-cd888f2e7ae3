import { CommonCallback } from './common';

/**
 * Ticket001InitGetEventTicketListModel
 */
export interface Ticket001InitGetEventTicketListModel extends CommonCallback {
    // 1ページあたりの取得イベント数
    PageSize?: number;
    // 排他開始キー
    ExclusiveStartKey?: string;
    // 利用済フラグ
    UsedFlag: boolean;
}

/**
 * EventTicketItem
 */
export interface EventTicketItem {
    // ユーザーイベントチケットID
    UserEventTicketID: string;
    // イベントチケットID
    EventTicketID: string;
    // イベントチケット詳細ID
    EventTicketDetailID: string;
    // イベントチケット入場時間
    EventTicketEntryTime: string;
    // イベントチケット退場時間
    EventTicketExitTime: string;
    // イベントチケット価格名
    EventTicketPriceName: string;
    // イベントチケット価格
    EventTicketPrice: number;
    // イベントチケット利用済フラグ
    EventTicketUsedFlag: boolean;
    // イベントチケットトップ画像
    EventTicketTopImage: string;
    // イベントチケット名
    EventTicketName: string;
    // イベントチケット開催日
    EventTicketDateTime: string;
    // イベント開催日付/利用時間状態
    EventDateTimeStatus: number;
}

/**
 * Ticket001InitGetEventTicketListResult
 */
export interface Ticket001InitGetEventTicketListResult {
    // LastEvaluatedKey
    LastEvaluatedKey: string;
    // ユーザーイベントチケット一覧
    UserEventTicketList: EventTicketItem[];
}
