.input-quantity {
    display: flex;
    align-items: center;
    height: 32px;
    border: 1px solid #ccc;
    border-radius: 6px;

    .quantity-number {
        height: 100%;
        border-left: 1px solid #ccc;
        border-right: 1px solid #ccc;
        display: flex;
        justify-content: center;
        align-items: center;

        input {
            font-family: '<PERSON><PERSON>', <PERSON><PERSON>, メイリオ, Aria<PERSON>, Helvetica, sans-serif;
            width: 64px;
            border: none;
            text-align: center;
            font-size: 20px;
            padding: 0;

            &:hover {
                border: none !important;
                outline: none !important;
            }
            &:focus {
                border: none !important;
                outline: none !important;
            }
            &:active {
                border: none !important;
                outline: none !important;
            }
        }
    }

    div.subtract,
    div.add {
        width: 24px;
        display: flex;
        justify-content: center;
        align-items: center;
        cursor: pointer;
    }

    div.subtract.disabled,
    div.add.disabled {
        opacity: 0.5;
        background-color: rgba(239, 239, 239, 0.3);
        pointer-events: none;
        cursor: default;
    }
}
