import { CommonCallback } from './common';

/**
 * 基本4情報
 */
export interface Basic4Info {
    // 氏名
    Name?: string;
    // 住所
    Address?: string;
    // 生年月日
    Birthday?: string;
    // 性別
    Gender?: number;
}

/**
 * MCAS応援者詳細
 */
export interface MCASCheerDetails {
    // 本人確認済フラグ
    IdentityVerifiedFlag?: boolean;
    // 基本4情報
    Basic4Info?: Basic4Info;
}

/**
 * Entry007MainRegisterAccountModel
 */
export interface Entry007MainRegisterAccountModel extends CommonCallback {
    // ブランドID
    BrandID: string;
    // ユーザーID
    UserID: string;
    // 電話番号
    TelephoneNumber: string;
    // メールアドレス
    MailAddress: string;
    // パスワード
    Password: string;
    // 性別
    Gender: string;
    // 年齢
    Age?: string;
    // 居住地
    Residence?: string;
    // ニックネーム
    NickName: string;
    // 性
    LastName: string;
    // 名
    FirstName: string;
    // 生年月日
    DateOfBirth: string;
    // 郵便番号
    PostCode?: string;
    // 都道府県
    Prefectures?: string;
    // 市区町村
    Municipalities: string;
    // 町域
    TownArea?: string;
    // 番地
    HouseNumber?: string;
    // 建物名
    BuildingName?: string;
    // メールマガジン
    MailMagazine?: boolean;
    // 必須項目リスト
    RequiredFieldList: Array<keyof Omit<Entry007MainRegisterAccountModel, 'RequiredFieldList' | 'BuildingName'>>;
    // LINEユーザーID
    LineUserID?: string;
    // Username
    Username?: string;
    // 同意した利用規約のバージョン
    AgreementVersion?: number;
    // MCASエンドユーザーID
    MCASEnduserID?: string;
    // MCAS応援者詳細
    MCASCheerDetails?: MCASCheerDetails;
}
