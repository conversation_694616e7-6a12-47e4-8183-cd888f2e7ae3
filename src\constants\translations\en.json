{"error_messages": {"not_valid_japanese": "{0} is not valid.", "incorrect_email": "The email address is incorrect.", "incorrect_phonenumber": "The phone number is incorrect.", "main_required_jp": "This field is required.", "required_field": "This field is required.", "half_width_number": "Please enter only half-width numbers.", "equal_length_japanese": "{0} must be {1} characters long in half-width alphanumeric.", "min_length_japanese": "{0} must be at least {1} characters long in half-width alphanumeric.", "max_length_japanese": "{0} must be no more than {1} characters long in half-width alphanumeric.", "password_not_match": "The passwords do not match.", "send_pin_fail": "Failed to send the PIN code.", "enter_half_with_number": "Please enter {0} digits in half-width numbers.", "incorrect": "{0} is incorrect.", "exceeded_maximum_characters": "The maximum character limit has been exceeded.", "exceed_file_size": "The file size exceeds the maximum limit.", "system_error": "A system error has occurred. Please try again later.ase try again later.", "exceed_file_size_10": "ファイルの登録に失敗しました。ファイルサイズを10MB以下にしてください。", "network_error": "現在接続できません。\nネットワークを確認して、後でもう一度お試しください。"}, "api": {"common": {"unknown_error": "An error has occurred.", "something_went_error": "An unexpected issue has occurred.", "timeout": "A connection timeout has occurred."}}, "ERR_UNKNOWN": "An error has occurred. (ERR_UNKNOWN)", "ERR_NETWORK": "A network error has occurred. Please try again later.", "ECONNABORTED": "An error has occurred. (ECONNABORTED)", "ERR_BAD_REQUEST": "An error has occurred. (ERR_BAD_REQUEST)", "ERR_BAD_RESPONSE": "An error has occurred. (ERR_BAD_RESPONSE)", "ERR_UNAUTHORIZED": "An authentication error has occurred. Please log in again.", "contact001-main-send-inquiry-001-ERROR": "Invalid request parameters.", "contact001-main-send-inquiry-002-ERROR": "A system error has occurred. Please try again later.ase try again later.", "contact001-init-get-email-001-ERROR": "Invalid request parameters.", "contact001-init-get-email-002-ERROR": "A system error has occurred.", "login001-main-get-token-002-ERROR": "Invalid request parameters.", "login001-main-get-token-003-ERROR": "Failed to retrieve data.", "login001-main-get-token-004-ERROR": "Incorrect user ID or password.", "login001-main-get-token-004-INFO": "Incorrect user ID or password.", "login001-main-get-token-005-ERROR": "A system error has occurred. Please try again later.ase try again later.", "login001-main-get-token-005-WARNING": "This account is currently unavailable for login.", "login001-main-get-token-006-ERROR": "Failed to retrieve data.", "login001-main-get-token-007-ERROR": "Failed to retrieve data.", "login001-main-get-token-008-INFO": "Incorrect user ID or password.", "login001-main-get-token-009-WARNING": "Incorrect user ID or password. If you enter the wrong password one more time, your account will be locked.", "login001-main-get-token-010-ERROR": "Failed to retrieve data.", "login001-main-get-token-011-WARNING": "Incorrect user ID or password. Your account has been locked. If you forgot your password, please reset it.", "login001-main-get-token-012-ERROR": "Failed to update data.", "login001-main-get-token-013-INFO": "Incorrect user ID or password.", "login001-main-get-enableflag-001-INFO": "Request parameter", "login001-main-get-enableflag-002-ERROR": "Invalid request parameters.", "login001-main-get-enableflag-003-ERROR": "A system error has occurred. Please try again later.ase try again later.", "login000-main-get-entryenableflag-001-INFO": "Request parameter", "login000-main-get-entryenableflag-002-ERROR": "Invalid request parameters.", "login000-main-get-entryenableflag-003-ERROR": "A system error has occurred. Please try again later.ase try again later.", "news001-init-get-personal-news-list-002-ERROR": "A system error has occurred.", "news001-init-get-personal-news-list-003-ERROR": "A system error has occurred.", "news001-init-get-personal-news-list-004-ERROR": "A system error has occurred.", "news001-init-get-personal-news-list-005-ERROR": "A system error has occurred.", "news001-init-get-important-news-list-002-ERROR": "A system error has occurred.", "news001-init-get-important-news-list-003-ERROR": "A system error has occurred.", "news001-init-get-important-news-list-004-ERROR": "A system error has occurred.", "news001-init-get-important-news-list-005-ERROR": "A system error has occurred.", "news002-init-get-news-detail-002-ERROR": "A system error has occurred.", "news002-init-get-news-detail-003-ERROR": "A system error has occurred.", "news002-init-get-news-detail-004-ERROR": "A system error has occurred.", "news002-init-get-news-detail-005-WARNING": "Failed to retrieve announcement information.", "news002-init-get-news-detail-006-ERROR": "A system error has occurred.", "news002-init-get-news-detail-007-WARNING": "Failed to retrieve announcement information.", "coupon001-init-get-coupon-list-002-ERROR": "A system error has occurred.", "coupon001-init-get-coupon-list-003-ERROR": "A system error has occurred.", "coupon001-init-get-coupon-list-004-ERROR": "A system error has occurred.", "coupon001-init-get-coupon-list-005-ERROR": "A system error has occurred.", "coupon001-init-get-coupon-list-006-ERROR": "A system error has occurred.", "coupon004-main-use-coupon-002-ERROR": "A system error has occurred.", "coupon004-main-use-coupon-003-ERROR": "A system error has occurred.", "coupon004-main-use-coupon-004-WARNING": "This coupon has already been used.", "coupon004-main-use-coupon-005-ERROR": "A system error has occurred.", "coupon003-init-get-coupon-detail-002-ERROR": "A system error has occurred.", "coupon003-init-get-coupon-detail-003-ERROR": "A system error has occurred.", "coupon003-init-get-coupon-detail-004-ERROR": "A system error has occurred.", "coupon003-init-get-coupon-detail-005-ERROR": "A system error has occurred.", "login001-init-get-banner-list-001-INFO": "", "login001-init-get-banner-list-002-ERROR": "A system error has occurred.", "login001-init-get-keyvisual-list-001-ERROR": "Invalid request parameters.", "login001-init-get-keyvisual-list-002-ERROR": "A system error has occurred. Please try again later.ase try again later.", "home001-init-get-banner-list-002-ERROR": "A system error has occurred.", "home001-init-get-news-unread-latest-001-ERROR": "Invalid request parameters.", "home001-init-get-news-unread-latest-002-ERROR": "A system error has occurred. Please try again later.ase try again later.", "home001-init-get-news-unread-latest-003-ERROR": "A system error has occurred. Please try again later.ase try again later.", "home001-init-get-news-unread-latest-004-ERROR": "A system error has occurred. Please try again later.ase try again later.", "home001-init-get-news-unread-latest-005-ERROR": "A system error has occurred. Please try again later.ase try again later.", "home001-init-get-coin-001-ERROR": "A system error has occurred. Please try again later.ase try again later.", "home001-init-get-coin-002-ERROR": "A system error has occurred. Please try again later.ase try again later.", "home001-init-get-coin-003-ERROR": "A system error has occurred. Please try again later.ase try again later.", "home001-init-get-coin-004-ERROR": "A system error has occurred. Please try again later.ase try again later.", "home001-init-get-coin-005-ERROR": "A system error has occurred. Please try again later.ase try again later.", "home001-init-get-coin-006-ERROR": "A system error has occurred. Please try again later.ase try again later.", "home001-init-get-coin-007-ERROR": "A system error has occurred. Please try again later.ase try again later.", "home001-init-get-coupon-list-002-ERROR": "A system error has occurred.", "home001-init-get-coupon-list-003-ERROR": "A system error has occurred.", "home001-init-get-coupon-list-004-ERROR": "A system error has occurred.", "home001-init-get-coupon-list-005-ERROR": "A system error has occurred.", "home001-init-get-coupon-list-006-ERROR": "A system error has occurred.", "home001-init-get-news-unread-flag-002-ERROR": "A system error has occurred.", "home001-init-get-news-unread-flag-003-ERROR": "A system error has occurred.", "home001-init-get-news-unread-flag-004-ERROR": "A system error has occurred.", "home001-init-get-news-unread-flag-005-ERROR": "A system error has occurred.", "home001-init-get-userinfo-001-ERROR": "Invalid request parameters.", "home001-init-get-userinfo-002-ERROR": "A system error has occurred. Please try again later.ase try again later.", "settingpass002-main-update-password-001-ERROR": "Invalid request parameters.", "settingpass002-main-update-password-002-ERROR": "A system error has occurred. Please try again later.ase try again later.", "settingpass002-main-update-password-003-ERROR": "The password is incorrect.", "settingpass002-main-update-password-004-ERROR": "Please set a password different from the previous one.", "settingpass002-main-update-password-005-ERROR": "A system error has occurred. Please try again later.ase try again later.", "settingpass001-main-send-authcode-001-ERROR": "Invalid request parameters.", "settingpass001-main-send-authcode-002-ERROR": "A system error has occurred. Please try again later.ase try again later.", "settingpass001-main-send-authcode-003-ERROR": "A system error has occurred. Please try again later.ase try again later.", "settingpass001-main-send-authcode-004-ERROR": "A system error has occurred. Please try again later.ase try again later.", "settingpass001-main-send-authcode-005-ERROR": "A system error has occurred. Please try again later.ase try again later.", "settingpass001-main-send-authcode-006-ERROR": "A system error has occurred. Please try again later.ase try again later.", "settingpass001-main-send-authcode-007-ERROR": "A system error has occurred. Please try again later.ase try again later.", "settingpass001-main-send-authcode-008-ERROR": "A system error has occurred. Please try again later.ase try again later.", "settingpass001-main-check-authcode-001-ERROR": "Invalid request parameters.", "settingpass001-main-check-authcode-002-ERROR": "A system error has occurred. Please try again later.ase try again later.", "settingpass001-main-check-authcode-003-ERROR": "A system error has occurred. Please try again later.ase try again later.", "settingpass001-main-check-authcode-004-ERROR": "A system error has occurred. Please try again later.ase try again later.", "settingpass001-main-check-authcode-005-ERROR": "A system error has occurred. Please try again later.ase try again later.", "settingpass001-main-check-authcode-006-ERROR": "A system error has occurred. Please try again later.ase try again later.", "settingpass001-main-check-authcode-007-ERROR": "This code is invalid. Please request a new code.", "settingpass001-main-check-authcode-008-ERROR": "The authentication code is incorrect. Please check again.", "deleteaccount001-main-check-authcode-001-ERROR": "Invalid request parameters.", "deleteaccount001-main-check-authcode-007-ERROR": "This code is invalid. Please request a new code.", "deleteaccount001-main-check-authcode-008-ERROR": "The authentication code is incorrect. Please check again.", "entry002-init-get-brandinfo-001-ERROR": "Invalid request parameters.", "entry002-init-get-brandinfo-002-ERROR": "A system error has occurred. Please try again later.ase try again later.", "entry002-init-get-brandinfo-003-ERROR": "A system error has occurred. Please try again later.ase try again later.", "entry002-main-send-authcode-001-ERROR": "Invalid request parameters.", "entry002-main-send-authcode-002-ERROR": "A system error has occurred. Please try again later.ase try again later.", "entry002-main-send-authcode-003-ERROR": "A system error has occurred. Please try again later.ase try again later.", "entry002-main-send-authcode-004-ERROR": "A system error has occurred. Please try again later.ase try again later.", "entry002-main-send-authcode-005-ERROR": "A system error has occurred. Please try again later.ase try again later.", "entry002-main-send-authcode-006-ERROR": "This account has already been registered.", "entry002-main-send-authcode-007-ERROR": "A system error has occurred. Please try again later.ase try again later.", "entry002-main-send-authcode-008-ERROR": "A system error has occurred. Please try again later.ase try again later.", "entry002-main-send-authcode-009-ERROR": "A system error has occurred. Please try again later.ase try again later.", "entry003-main-check-authcode-002-ERROR": "Invalid request parameters.", "entry003-main-check-authcode-003-ERROR": "A system error has occurred. Please try again later.ase try again later.", "entry003-main-check-authcode-004-ERROR": "This code is invalid. Please request a new code.", "entry003-main-check-authcode-005-ERROR": "The code is incorrect. Please try again.", "entry006-main-get-token-002-ERROR": "Invalid request parameters.", "entry006-main-get-token-003-ERROR": "A system error has occurred. Please try again later.ase try again later.", "entry006-main-get-token-004-ERROR": "Authentication failed. Please try again later.", "entry006-main-register-account-001-ERROR": "Invalid request parameters.", "entry006-main-register-account-002-ERROR": "A system error has occurred. Please try again later.ase try again later.", "entry006-main-register-account-003-ERROR": "A system error has occurred. Please try again later.ase try again later.", "entry006-main-register-account-004-ERROR": "Account registration failed. Please try again later.", "entry006-main-register-account-005-ERROR": "A system error has occurred. Please try again later.ase try again later.", "entry006-main-register-account-006-ERROR": "A system error has occurred. Please try again later.ase try again later.", "reset001-main-send-authcode-001-ERROR": "Invalid request parameters.", "reset001-main-send-authcode-002-ERROR": "A system error has occurred. Please try again later.ase try again later.", "reset001-main-send-authcode-003-ERROR": "A system error has occurred. Please try again later.ase try again later.", "reset001-main-send-authcode-004-ERROR": "A system error has occurred. Please try again later.ase try again later.", "reset001-main-send-authcode-005-ERROR": "User ID does not exist.", "reset001-main-send-authcode-006-ERROR": "A system error has occurred. Please try again later.ase try again later.", "reset001-main-send-authcode-007-ERROR": "A system error has occurred. Please try again later.ase try again later.", "reset001-main-send-authcode-008-ERROR": "A system error has occurred. Please try again later.ase try again later.", "reset001-main-send-authcode-009-ERROR": "Failed to send email.", "reset001-main-send-authcode-010-ERROR": "This email address cannot be used.", "reset002-main-check-authcode-001-INFO": "Request parameter: [${0}]", "reset002-main-check-authcode-002-ERROR": "Invalid request parameters.", "reset002-main-check-authcode-003-ERROR": "A system error has occurred. Please try again later.ase try again later.", "reset002-main-check-authcode-004-ERROR": "A system error has occurred. Please try again later.ase try again later.", "reset002-main-check-authcode-005-ERROR": "A system error has occurred. Please try again later.ase try again later.", "reset002-main-check-authcode-006-ERROR": "A system error has occurred. Please try again later.ase try again later.", "reset002-main-check-authcode-007-ERROR": "A system error has occurred. Please try again later.ase try again later.", "reset002-main-check-authcode-008-ERROR": "This code is invalid. Please request a new code.", "reset002-main-check-authcode-009-ERROR": "The code is incorrect. Please try again.", "reset002-main-send-authcode-001-ERROR": "Invalid request parameters.", "reset002-main-send-authcode-002-ERROR": "A system error has occurred. Please try again later.ase try again later.", "reset002-main-send-authcode-003-ERROR": "A system error has occurred. Please try again later.ase try again later.", "reset002-main-send-authcode-004-ERROR": "A system error has occurred. Please try again later.ase try again later.", "reset002-main-send-authcode-005-ERROR": "A system error has occurred. Please try again later.ase try again later.", "reset002-main-send-authcode-006-ERROR": "A system error has occurred. Please try again later.ase try again later.", "reset002-main-send-authcode-007-ERROR": "A system error has occurred. Please try again later.ase try again later.", "reset002-main-send-authcode-008-ERROR": "A system error has occurred. Please try again later.ase try again later.", "reset002-main-send-authcode-009-ERROR": "Failed to send email.", "reset002-main-send-authcode-010-ERROR": "This email address cannot be used.", "reset005-main-reset-password-001-ERROR": "Invalid request parameters.", "reset005-main-reset-password-002-ERROR": "A system error has occurred. Please try again later.ase try again later.", "reset005-main-reset-password-003-ERROR": "A system error has occurred. Please try again later.ase try again later.", "reset005-main-reset-password-004-ERROR": "A system error has occurred. Please try again later.ase try again later.", "reset005-main-reset-password-005-ERROR": "Failed to retrieve data.", "reset005-main-reset-password-006-ERROR": "Failed to retrieve data.", "settingmailmagazine001-main-update-mailmagazine-001-ERROR": "Invalid request parameters.", "settingmailmagazine001-main-update-mailmagazine-002-ERROR": "A system error has occurred. Please try again later.ase try again later.", "settingtel001-main-check-authcode-001-ERROR": "Invalid request parameters.", "settingtel001-main-check-authcode-002-ERROR": "A system error has occurred. Please try again later.ase try again later.", "settingtel001-main-check-authcode-003-ERROR": "A system error has occurred. Please try again later.ase try again later.", "settingtel001-main-check-authcode-004-ERROR": "A system error has occurred. Please try again later.ase try again later.", "settingtel001-main-check-authcode-005-ERROR": "A system error has occurred. Please try again later.ase try again later.", "settingtel001-main-check-authcode-006-ERROR": "A system error has occurred. Please try again later.ase try again later.", "settingtel001-main-check-authcode-007-ERROR": "This code is invalid. Please request a new code.", "settingtel001-main-check-authcode-008-ERROR": "The authentication code is incorrect. Please check again.", "settingtel001-main-send-authcode-001-ERROR": "Invalid request parameters.", "settingtel001-main-send-authcode-002-ERROR": "A system error has occurred. Please try again later.ase try again later.", "settingtel001-main-send-authcode-003-ERROR": "A system error has occurred. Please try again later.ase try again later.", "settingtel001-main-send-authcode-004-ERROR": "A system error has occurred. Please try again later.ase try again later.", "settingtel001-main-send-authcode-005-ERROR": "A system error has occurred. Please try again later.ase try again later.", "settingtel001-main-send-authcode-006-ERROR": "A system error has occurred. Please try again later.ase try again later.", "settingtel001-main-send-authcode-007-ERROR": "A system error has occurred. Please try again later.ase try again later.", "settingtel001-main-send-authcode-008-ERROR": "A system error has occurred. Please try again later.ase try again later.", "settingtel002-main-send-authcode-001-ERROR": "Invalid request parameters.", "settingtel002-main-send-authcode-002-ERROR": "A system error has occurred. Please try again later.ase try again later.", "settingtel002-main-send-authcode-003-ERROR": "A system error has occurred. Please try again later.ase try again later.", "settingtel002-main-send-authcode-004-ERROR": "A system error has occurred. Please try again later.ase try again later.", "settingtel002-main-send-authcode-005-ERROR": "A system error has occurred. Please try again later.ase try again later.", "settingtel002-main-send-authcode-006-ERROR": "A system error has occurred. Please try again later.ase try again later.", "settingtel003-main-check-authcode-001-ERROR": "Invalid request parameters.", "settingtel003-main-check-authcode-002-ERROR": "A system error has occurred. Please try again later.ase try again later.", "settingtel003-main-check-authcode-003-ERROR": "This code is invalid. Please request a new code.", "settingtel003-main-check-authcode-004-ERROR": "The authentication code is incorrect. Please check again.", "settingtel003-main-send-authcode-001-ERROR": "Invalid request parameters.", "settingtel003-main-send-authcode-002-ERROR": "A system error has occurred. Please try again later.ase try again later.", "settingtel003-main-send-authcode-003-ERROR": "A system error has occurred. Please try again later.ase try again later.", "settingtel003-main-send-authcode-004-ERROR": "A system error has occurred. Please try again later.ase try again later.", "settingtel003-main-send-authcode-005-ERROR": "A system error has occurred. Please try again later.ase try again later.", "settingtel003-main-send-authcode-006-ERROR": "A system error has occurred. Please try again later.ase try again later.", "settingtel003-main-update-tel-001-ERROR": "Invalid request parameters.", "settingtel003-main-update-tel-002-ERROR": "A system error has occurred. Please try again later.ase try again later.", "settingmail001-main-send-authcode-001-ERROR": "Invalid request parameters.", "settingmail001-main-send-authcode-002-ERROR": "A system error has occurred. Please try again later.ase try again later.", "settingmail001-main-send-authcode-003-ERROR": "A system error has occurred. Please try again later.ase try again later.", "settingmail001-main-send-authcode-004-ERROR": "A system error has occurred. Please try again later.ase try again later.", "settingmail001-main-send-authcode-005-ERROR": "A system error has occurred. Please try again later.ase try again later.", "settingmail001-main-send-authcode-006-ERROR": "A system error has occurred. Please try again later.ase try again later.", "settingmail001-main-send-authcode-007-ERROR": "A system error has occurred. Please try again later.ase try again later.", "settingmail001-main-send-authcode-008-ERROR": "A system error has occurred. Please try again later.ase try again later.", "settingmail001-main-check-authcode-001-ERROR": "Invalid request parameters.", "settingmail001-main-check-authcode-002-ERROR": "A system error has occurred. Please try again later.ase try again later.", "settingmail001-main-check-authcode-003-ERROR": "A system error has occurred. Please try again later.ase try again later.", "settingmail001-main-check-authcode-004-ERROR": "A system error has occurred. Please try again later.ase try again later.", "settingmail001-main-check-authcode-005-ERROR": "A system error has occurred. Please try again later.ase try again later.", "settingmail001-main-check-authcode-006-ERROR": "A system error has occurred. Please try again later.ase try again later.", "settingmail001-main-check-authcode-007-ERROR": "This code is invalid. Please request a new code.", "settingmail001-main-check-authcode-008-ERROR": "The authentication code is incorrect. Please check again.", "settingmail002-main-send-authcode-001-ERROR": "Invalid request parameters.", "settingmail002-main-send-authcode-002-ERROR": "The entered email address cannot be used.", "settingmail002-main-send-authcode-003-ERROR": "A system error has occurred. Please try again later.ase try again later.", "settingmail002-main-send-authcode-004-ERROR": "A system error has occurred. Please try again later.ase try again later.", "settingmail002-main-send-authcode-005-ERROR": "A system error has occurred. Please try again later.ase try again later.", "settingmail002-main-send-authcode-006-ERROR": "A system error has occurred. Please try again later.ase try again later.", "settingmail003-main-send-authcode-001-ERROR": "Invalid request parameters.", "settingmail003-main-send-authcode-002-ERROR": "A system error has occurred. Please try again later.ase try again later.", "settingmail003-main-send-authcode-003-ERROR": "A system error has occurred. Please try again later.ase try again later.", "settingmail003-main-send-authcode-004-ERROR": "A system error has occurred. Please try again later.ase try again later.", "settingmail003-main-check-authcode-001-ERROR": "Invalid request parameters.", "settingmail003-main-check-authcode-002-ERROR": "A system error has occurred. Please try again later.ase try again later.", "settingmail003-main-check-authcode-003-ERROR": "This code is invalid. Please request a new code.", "settingmail003-main-check-authcode-004-ERROR": "The authentication code is incorrect. Please check again.", "settingmail003-main-update-email-001-ERROR": "Invalid request parameters.", "settingmail003-main-update-email-002-ERROR": "A system error has occurred. Please try again later.ase try again later.", "settingmail003-main-update-email-003-ERROR": "A system error has occurred. Please try again later.ase try again later.", "settingmail003-main-update-email-004-ERROR": "A system error has occurred. Please try again later.ase try again later.", "settingmail003-main-update-email-005-ERROR": "A system error has occurred. Please try again later.ase try again later.", "settingmail003-main-update-email-006-ERROR": "A system error has occurred. Please try again later.ase try again later.", "settingmail003-main-update-email-007-ERROR": "A system error has occurred. Please try again later.ase try again later.", "settingmail003-main-update-email-008-ERROR": "A system error has occurred. Please try again later.ase try again later.", "settingtop001-main-send-authcode-001-ERROR": "Invalid request parameters.", "settingtop001-main-send-authcode-002-ERROR": "A system error has occurred. Please try again later.ase try again later.", "settingtop001-main-send-authcode-003-ERROR": "A system error has occurred. Please try again later.ase try again later.", "settingtop001-main-send-authcode-004-ERROR": "A system error has occurred. Please try again later.ase try again later.", "settingtop001-main-send-authcode-005-ERROR": "A system error has occurred. Please try again later.ase try again later.", "settingtop001-main-send-authcode-006-ERROR": "A system error has occurred. Please try again later.ase try again later.", "settingtop001-main-send-authcode-007-ERROR": "A system error has occurred. Please try again later.ase try again later.", "settingtop001-main-send-authcode-008-ERROR": "A system error has occurred. Please try again later.ase try again later.", "settingtop001-main-update-nickname-001-ERROR": "Invalid request parameters.", "settingtop001-main-update-nickname-002-ERROR": "A system error has occurred. Please try again later.ase try again later.", "chargecredit001-init-get-chargelimit-001-ERROR": "Invalid request parameters.", "chargecredit001-init-get-chargelimit-002-ERROR": "A system error has occurred. Please try again later.ase try again later.", "chargecredit001-init-get-chargelimit-003-ERROR": "A system error has occurred. Please try again later.ase try again later.", "chargecredit001-init-get-chargelimit-004-ERROR": "A system error has occurred. Please try again later.ase try again later.", "chargecredit002-main-execute-charge-001-ERROR": "Invalid request parameters.", "chargecredit002-main-execute-charge-002-ERROR": "A system error has occurred. Please try again later.ase try again later.", "chargecredit002-main-execute-charge-003-ERROR": "A system error has occurred. Please try again later.ase try again later.", "chargecnveni001-init-get-chargelimit-001-ERROR": "Invalid request parameters.", "chargecnveni001-init-get-chargelimit-002-ERROR": "A system error has occurred. Please try again later.ase try again later.", "chargecnveni001-init-get-chargelimit-003-ERROR": "A system error has occurred. Please try again later.ase try again later.", "chargecnveni001-init-get-chargelimit-004-ERROR": "A system error has occurred. Please try again later.ase try again later.", "chargecnveni002-main-execute-charge-001-ERROR": "Invalid request parameters.", "chargecnveni002-main-execute-charge-002-ERROR": "A system error has occurred. Please try again later.ase try again later.", "chargecnveni002-main-execute-charge-003-ERROR": "A system error has occurred. Please try again later.ase try again later.", "chargeintbnk001-init-get-chargelimit-001-ERROR": "Invalid request parameters.", "chargeintbnk001-init-get-chargelimit-002-ERROR": "A system error has occurred. Please try again later.ase try again later.", "chargeintbnk001-init-get-chargelimit-003-ERROR": "A system error has occurred. Please try again later.ase try again later.", "chargeintbnk001-init-get-chargelimit-004-ERROR": "A system error has occurred. Please try again later.ase try again later.", "chargeintbnk002-main-execute-charge-001-ERROR": "Invalid request parameters.", "chargeintbnk002-main-execute-charge-002-ERROR": "A system error has occurred. Please try again later.ase try again later.", "chargeintbnkpaytest001-main-register-bankpay-002-ERROR": "A system error has occurred.", "chargeintbnkpaytest001-main-register-bankpay-003-ERROR": "A system error has occurred.", "chargeintbnkpaytest001-main-register-bankpay-004-ERROR": "A system error has occurred.", "chargeprepid001-main-execute-charge-001-ERROR": "Invalid request parameters.", "chargeprepid001-main-execute-charge-002-ERROR": "A system error has occurred. Please try again later.ase try again later.", "chargeprepid001-main-execute-charge-003-ERROR": "A system error has occurred. Please try again later.ase try again later.", "chargeprepid001-main-execute-charge-004-ERROR": "Invalid serial code.", "chargeprepid001-main-execute-charge-005-ERROR": "A system error has occurred. Please try again later.ase try again later.", "chargeprepid001-main-execute-charge-006-ERROR": "A system error has occurred. Please try again later.ase try again later.", "chargeprepid001-main-execute-charge-007-ERROR": "Invalid serial code.", "chargeprepid001-main-execute-ocr-001-ERROR": "Invalid request parameters.", "chargeprepid001-main-execute-ocr-002-ERROR": "A system error has occurred. Please try again later.ase try again later.", "store003-init-get-store-detail-002-ERROR": "A system error has occurred.", "store003-init-get-store-detail-003-ERROR": "A system error has occurred.", "store003-init-get-store-detail-004-ERROR": "A system error has occurred.", "store003-init-get-store-detail-005-ERROR": "A system error has occurred.", "store003-init-get-store-detail-006-ERROR": "A system error has occurred.", "store003-init-get-store-detail-007-ERROR": "A system error has occurred.", "store001-main-get-store-query-002-ERROR": "A system error has occurred.", "store001-main-get-store-query-003-ERROR": "A system error has occurred.", "store001-main-get-store-query-004-ERROR": "A system error has occurred.", "store001-main-get-store-query-005-ERROR": "A system error has occurred.", "store001-main-get-store-query-006-ERROR": "A system error has occurred.", "store001-main-get-store-query-007-ERROR": "A system error has occurred.", "store001-main-get-store-query-008-ERROR": "A system error has occurred.", "store001-main-get-store-query-009-ERROR": "A system error has occurred.", "store001-init-get-store-query-001-ERROR": "A system error has occurred.", "store001-init-get-store-query-002-ERROR": "A system error has occurred.", "store001-init-get-store-query-003-ERROR": "A system error has occurred.", "rocketssurvey001-init-get-survey-list-001-ERROR": "Invalid request parameters.", "rocketssurvey001-init-get-survey-list-002-ERROR": "A system error has occurred. Please try again later.", "rocketssurvey001-init-get-survey-list-003-ERROR": "The survey is outside the reception period.", "rocketssurvey001-init-get-survey-list-004-ERROR": "The survey is outside the reception period.", "rocketssurvey001-init-get-survey-list-005-ERROR": "A system error has occurred. Please try again later.", "rocketssurvey001-init-get-survey-list-006-ERROR": "A system error has occurred. Please try again later.", "rocketssurvey001-init-get-survey-list-007-ERROR": "No data found. Please try again later.", "rocketssurvey001-init-get-survey-list-008-ERROR": "A system error has occurred. Please try again later.", "rocketssurvey001-init-get-survey-list-009-ERROR": "The response has already been accepted.", "rocketssurvey002-main-send-answer-001-ERROR": "Invalid request parameters.", "rocketssurvey002-main-send-answer-002-ERROR": "A system error has occurred. Please try again later.", "rocketssurvey002-main-send-answer-003-ERROR": "A system error has occurred. Please try again later.", "settingpersonal003-main-update-address-001-ERROR": "A system error has occurred.", "settingpersonal003-main-update-address-002-ERROR": "A system error has occurred. Please try again later.", "settingpersonal001-main-check-authcode-001-ERROR": "Invalid request parameters.", "settingpersonal001-main-check-authcode-002-ERROR": "A system error has occurred. Please try again later.", "settingpersonal001-main-check-authcode-003-ERROR": "A system error has occurred. Please try again later.", "settingpersonal001-main-check-authcode-004-ERROR": "A system error has occurred. Please try again later.", "settingpersonal001-main-check-authcode-005-ERROR": "A system error has occurred. Please try again later.", "settingpersonal001-main-check-authcode-006-ERROR": "A system error has occurred. Please try again later.", "settingpersonal001-main-check-authcode-007-ERROR": "This code is invalid. Please request a new code.", "settingpersonal001-main-check-authcode-008-ERROR": "The authentication code is incorrect. Please check again.", "settingpersonal001-main-send-authcode-001-ERROR": "Invalid request parameters.", "settingpersonal001-main-send-authcode-002-ERROR": "A system error has occurred. Please try again later.", "settingpersonal001-main-send-authcode-003-ERROR": "A system error has occurred. Please try again later.", "settingpersonal001-main-send-authcode-004-ERROR": "A system error has occurred. Please try again later.", "settingpersonal001-main-send-authcode-005-ERROR": "A system error has occurred. Please try again later.", "settingpersonal001-main-send-authcode-006-ERROR": "A system error has occurred. Please try again later.", "settingpersonal001-main-send-authcode-007-ERROR": "A system error has occurred. Please try again later.", "settingpersonal001-main-send-authcode-008-ERROR": "A system error has occurred. Please try again later.", "settingpersonal002-init-get-personalinfo-001-ERROR": "Invalid request parameters.", "settingpersonal002-init-get-personalinfo-002-ERROR": "A system error has occurred. Please try again later.", "common": {"button": {"cancel": "Cancel", "next": "Next", "change": "Change", "close": "Close", "close_kanji": "Close", "send_pin": "Send PIN Code", "forgot_pass": "Forgot Password?", "forgot_zodiac": "Forgot Zodiac Sign and Chinese Zodiac?", "complete": "Complete", "keep": "Save", "confirm": "Confirm", "open_google_map": "View in Maps App", "pay": "Pay", "pin_code_send": "Send Verification Code", "back_to_setting": "Back to Settings"}, "text": {"email": "Email Address", "password": "Password", "user_id": "User ID", "zodiac": "Chinese Zodiac & Zodiac Sign", "payment": "Payment", "yen_currency": "Yen", "news": "News List", "receivce_mail": "Receive Email Newsletter", "month": "Month", "day": "Day", "point": "pt", "point_jp": "Points", "remain": "Remaining", "up_to_characters": "Up to {0} characters", "language_choice": "Language Selection", "top": "Top", "include_tax": "(Tax Included)", "click_here_for_user": "For Users Click Here", "login": "<PERSON><PERSON>", "my_page": "My Page"}, "language": {"pop_js": "ja"}, "zip_code_not_found": "Address could not be found."}, "setting": {"change_email": {"header": "Change Email Address", "pin": {"info": "Please enter the verification code sent to.", "tel_resend": "Resend Code", "pin_code": "Verification Code", "resend_to": "Verification code has been resent to.", "send_code": "Send Verification Code"}, "input": {"label_email": "New Email Address", "sub_label_email": "Example: <EMAIL>", "placeholder_email": "Enter your email address", "dialog_title": "Login Email Address", "dialog_message": "has been changed.", "header": "Enter your new email address", "max_length": "Exceeded the allowed number of characters."}, "step": [{"text": "One-Time Password Authentication"}, {"text": "Enter Email Address"}, {"text": "Verify Em<PERSON> Address"}, {"text": "Change Completed"}], "verify": {"notice_code": "Please enter the verification code sent to.", "verify_success_mess": "Email address has been changed.", "re_send_title": "Please enter the verification code sent to.", "submit": "Change"}, "complete": {"screen_title": "Change Completed", "title": "Email Address\nHas Been Changed", "button": "Return to Settings"}}, "change_tel": {"header": "Change Phone Number", "header_complete": "Change Completed", "input": {"title_form": "Enter New Phone Number", "label_tel": "Mobile Phone Number (No Hyphen)", "sub_label_tel": "Example: ***********", "placeholder_tel": "Enter your mobile phone number", "phone_length": "Please enter your phone number.", "first_three_char": "The first three digits must be 070, 080, or 090."}, "pass": {"label_pass": "Password", "placeholder_pass": "Enter your password", "info": "Please enter the verification code sent to.", "resend_to": "Verification code has been resent to.", "pin_code": "Verification Code"}, "zodiac": {"title_left": "Your Chinese Zodiac", "title_left2": "Your Zodiac Sign", "title_right": "Please select."}, "pin": {"info": "Please enter the verification code sent to.", "tel_resend": "Resend Code", "pin_code": "PIN Code", "dialog_title": "Phone Number Changed"}, "step": [{"text": "One-Time Password Authentication"}, {"text": "Enter Phone Number"}, {"text": "SMS Authentication"}, {"text": "Change Completed"}], "complete": "Phone Number\nHas Been Changed"}, "change_zodiac": {"header": "Change Chinese Zodiac & Zodiac Sign", "current": {"title_left": "Currently Set Chinese Zodiac", "title_left1": "Currently Set Zodiac Sign", "title_right": "Please select."}, "new": {"title_top_left": "Register Chinese Zodiac", "title_top_left1": "Register Zodiac Sign", "title_top_right": "Please select.", "title_bottom_left": "For login,", "title_bottom_mid": "Used for identity verification,", "title_bottom_right": "will be applied.", "dialog_title": "Chinese Zodiac & Zodiac Sign Changed"}, "step": [{"text": "Chinese Zodiac Verification"}, {"text": "Zodiac Sign Verification"}, {"text": "Register Chinese Zodiac"}, {"text": "Register Zodiac Sign"}, {"text": "Change Completed"}]}, "change_pass": {"dialog_title": "Password Changed", "header": "Change Password", "pin": {"info": "Please enter the verification code sent to.", "pin_code": "Verification Code", "tel_resend": "Resend Code", "resend_to": "Verification code has been resent to.", "next_button": "Send Verification Code"}, "step": [{"text": "One-Time Password Authentication"}, {"text": "Enter Password"}, {"text": "Change Completed"}], "complete": {"screen_title": "Change Completed", "title": "Password\nHas Been Changed", "button": "Return to Settings"}, "label_old_pass": "Current Password", "sub_label_old_pass": "At least 8 alphanumeric characters", "placeholder_old_pass": "Enter your current password", "label_pass": "New Password", "sub_label_pass": "At least 8 alphanumeric characters", "placeholder_pass": "Enter your new password", "label_confirm_pass": "Re-enter New Password", "placeholder_confirm_pass": "Re-enter your new password", "special_number_char": "Please include a special character in your password.", "password_not_match": "Password and confirmation password do not match.", "wrong_password": "Incorrect password."}, "withdraw": {"info": "Please enter the verification code sent to.", "tel_resend": "Resend Code", "pin_code": "Verification Code", "placeholder_reason": "Enter reason for withdrawal", "label_reason": "Reason for Withdrawal", "header": "Withdraw", "dialog_title": "<PERSON><PERSON><PERSON> Accepted", "dialog_message1": "Thank you for using our service.", "dialog_message2": "We look forward to serving you again.", "button_withdraw": "Stop", "label_checkbox": "I have confirmed.", "dialog_confirm_title": "Are you sure you want to withdraw?", "dialog_confirm_message1": "All data related to this service will be deleted.", "dialog_confirm_message2": "Data cannot be restored.", "resend_pin": "Verification code has been resent to {0}."}, "delete_account": {"003": {"header": "<PERSON><PERSON><PERSON> Accepted", "title1": "Thank you for using our service.", "title2": "We look forward to serving you again."}}, "choose_image": {"title_collection": "Photo Library", "title_camera": "Take a Photo or Video", "title_folder": "Select a File"}, "menu_setting": {"header": "Settings", "user_info": "User Information", "alert_change_name": "Nickname has been changed.", "alert_copy": "User ID copied.", "button_verify": "Verify Identity", "button_copy": "Copy", "label_email": "Email Address (User ID)", "label_tel": "Phone Number", "label_noti": "Receive Notifications", "label_google": "Sync with Google Calendar", "button_withdraw": "Withdraw", "label_avatar": "Change Icon", "button_update": "Update", "account_information": "Personal Information (Address, Name, etc.)", "receive_mail": "Receive Email Newsletter", "receive_mail_success": "Email newsletter preferences updated.", "accept_receive_mail": "Subscribe", "not_receive_mail": "Unsubscribe", "external_service": "External Service Integration", "icon_change": "Change Icon", "confirm_cancel_line_mesage": "退会前にLINEとの連携を解除します", "confirm_link_line_mesage": "LINEと連携を行います", "confirm_unlink_line_mesage": "LINEとの連携を解除しますか？"}, "change_receive_mail": {"stop_title": "Stop receiving the\nemail newsletter?", "stop_info": "If you stop receiving emails,\nyou will no longer get special offers.", "button_stop": "Stop", "stop_success_title": "You have stopped\nreceiving the email newsletter.", "stop_success_info": "If you wish to resubscribe,\nplease update your settings.", "receive_title": "Subscribe to the\nemail newsletter?", "receive_info": "By subscribing,\nyou will receive exclusive deals.", "button_receive": "Subscribe", "receive_success_title": "You have subscribed\nto the email newsletter.", "receive_success_info": "Enjoy shopping with great deals!"}, "change_personal_info": {"verify_code": "Change Personal Information", "personal_info_view": {"header": "Change Personal Information", "full_name": "Full Name", "not_change": "Cannot be changed", "dob": "Date of Birth", "gender": "Gender", "address": "Address", "back_to_setting": "設定へ戻る"}, "error_message": {"invalid_code": "This code is invalid. Please request a new code.", "incorrect_code": "The verification code is incorrect. Please check again."}, "change_address": {"header": "Change Details", "postal_code": "Postal Code", "postal_code_sub_label": "Example: 1234567", "postal_code_placeholder": "Enter your postal code", "cannot_find_address": "Address could not be found.\nPlease fill in the form below.", "prefectures": "Prefecture", "prefectures_placeholder": "Select", "municipality": "City/Ward/Town/Village", "municipality_sub_label": "Example: <PERSON><PERSON><PERSON><PERSON><PERSON>, Shin<PERSON><PERSON>", "municipality_placeholder": "Enter city/ward/town/village", "neigh_bour_hood": "Neighborhood", "neigh_bour_hood_sub_label": "Example: Shinkiba", "neigh_bour_hood_placeholder": "Enter neighborhood", "address": "Street Address", "address_sub_label": "Example: 1-18-7", "address_placeholder": "Enter street address", "building_name": "Building Name", "building_name_sub_label": "Example: NES Building 101", "building_name_placeholder": "Enter building name", "complete_message": "Address has been changed."}, "pin": {"info": "Please enter the verification code sent to.", "pin_code": "Verification Code", "tel_resend": "Resend Code", "resend_to": "Verification code has been resent to.", "send_code": "Send Verification Code"}, "complete": {"header": "Change Completed", "title": "Personal Information\nHas Been Changed."}}}, "register": {"register_step": [{"text": "Review Terms of Use"}, {"text": "One-Time Password Authentication"}, {"text": "User Information Registration"}, {"text": "Registration Completed"}], "terms": {"header": "New Registration", "terms_of_use": "Terms of Use", "agree_term_of_use": "Agree to the Terms of Use", "agree_commercial_law": "Acknowledge the Handling of Personal Information", "read_and_agree_before_start": "Please read to the end and agree before starting the service.", "article_1_header": "Article 1 (Purpose of These Terms of Use)", "article_1_content": "Dummy text dummy text dummy text dummy text dummy text dummy text dummy text dummy text dummy text dummy text.", "article_2_header": "Article 2 (Dummy Text)", "article_2_1_content": "1. Dummy text dummy text dummy text dummy text dummy text dummy text.", "article_2_2_content": "2. Dummy text dummy text dummy text dummy text dummy text dummy text dummy text dummy text.", "article_3_header": "Article 3 (<PERSON>mmy Text)", "article_3_1_content": "1. Dummy text dummy text dummy text dummy text dummy text dummy text dummy text dummy text dummy text dummy text dummy text dummy text dummy text.", "article_3_2_content": "2. Dummy text dummy text dummy text dummy text dummy text.", "article_3_2_subpoints": ["(a) If it is reasonably expected that the service will be used in violation of these terms.", "(b) If there is a delay in fulfilling obligations owed to the company, or if there has been a delay in the past.", "(c) If false information is provided at the time of application.", "(d) If the applicant is a minor, an adult ward, a person under curatorship, or a person under assistance, and lacks the legal capacity to enter into a contract without the consent of a legal representative, and such consent or approval cannot be confirmed by the company.", "(e) If the applicant is affiliated with an anti-social force.", "(f) If identity verification cannot be performed.", "(g) In addition to the above, if the company determines that there is an operational hindrance or a risk of such hindrance."], "article_4_header": "Article 4 (Contents of the Service)", "article_4_content": "1. This service is an online inheritance support service that enables the creation, execution, storage, and management of asset inventories and electronic contracts on a cloud server and blockchain system. The service primarily provides the following functions to customers.", "article_4_subpoints": ["(a) Function for creating an asset inventory through photo uploads or text input.", "(b) Function for storing digital data such as photos and letters.", "(c) Function for displaying and downloading the asset inventory.", "(d) Function for using an electronic seal by performing identity verification.", "(e) Function for creating a civil trust contract to facilitate the inheritance process for assets listed in the asset inventory.", "(f) Function for concluding and managing electronic contracts for civil trust contracts with designated customers.", "(g) Function for preventing fraudulent use of the above functions through identity verification."], "button_back": "Back", "button_agree_and_register": "Agree and Register"}, "account_verify_code_send": {"verify_text": "Please enter your mobile phone number.", "1": {"for_verify": "SMS", "enter": "Mobile Phone Number", "label": "Mobile Phone Number (No Hyphen)", "sub_label": "Example: ***********", "placeholder": "Enter your mobile phone number"}, "2": {"for_verify": "Email", "enter": "Email Address", "label": "Email Address", "sub_label": "Example: <EMAIL>", "placeholder": "Enter your email address"}, "verification_text": "To verify your identity via {0}, please enter {1}.", "telephone_number": "Mobile Phone Number", "email": "Email", "email_address": "Email Address", "enter_valid_value": "Please enter correctly.", "exceed_number_of_digits": "Exceeded the allowed number of digits.", "button": {"send_verification_code": "Send Verification Code"}}, "account_info": {"verification_text": "To verify your identity via {0}, please enter {1}.", "telephone_number_text": "Mobile Phone Number", "email": "Email", "telephone_number_label": "Mobile Phone Number", "email_address_label": "Email Address", "email_address": "Email Address", "telephone_number_sub_label": "Example: ***********", "telephone_number_placeholder": "Enter your mobile phone number", "email_sub_label": "Example: <EMAIL>", "email_placeholder": "Enter your email address", "email_invalid_message": "Invalid email address.", "email_incorrect_message": "The email address is incorrect.", "nickname_label": "Nickname", "nickname_sub_label": "Nickname used within the app", "nickname_placeholder": "Enter your nickname", "password_text": "Password", "password_label": "Set Password", "password_sub_label": "At least 8 alphanumeric characters", "password_placeholder": "Enter your password", "password_incorrect_message": "The password is incorrect.", "confirm_password_label": "Re-enter Password", "confirm_password_placeholder": "Enter your password again", "send_verification_code": "Send Verification Code", "button_pin_code_send": "Send PIN Code", "account_creation_failed_message": "Account creation failed", "up_to_characters": "Up to {0} characters", "gender": "Gender", "gender_option": ["Male", "Female", "Other", "Prefer not to answer"], "gender_male": "Male", "gender_women": "Female", "gender_others": "Other", "gender_private": "Prefer not to answer", "age": "Age", "age_placeholder": "Select", "residence": "Residence", "residence_placeholder": "Select", "full_name": "Full Name", "full_name_sub_label": "Enter your real name", "full_name_placeholder": "Enter your real name", "last_name_placeholder": "Last Name", "invalid_last_name": "Invalid last name.", "first_name_placeholder": "First Name", "invalid_first_name": "Invalid first name.", "date_of_birth": "Date of Birth", "year_of_birth_placeholder": "Year", "month_of_birth_placeholder": "Month", "day_of_birth_placeholder": "Day", "postal_code": "Postal Code", "postal_code_sub_label": "Example: 1234567", "postal_code_placeholder": "Enter your postal code", "cannot_find_address": "The postal code does not exist.", "prefectures": "Prefecture", "prefectures_placeholder": "Select", "municipalities": "City/Ward/Town/Village", "municipalities_sub_label": "Example: <PERSON><PERSON><PERSON><PERSON><PERSON>, Shin<PERSON><PERSON>", "municipalities_placeholder": "Enter city/ward/town/village", "town_area": "Neighborhood", "town_area_sub_label": "Example: Shinkiba", "town_area_placeholder": "Enter neighborhood", "address": "Street Address", "address_sub_label": "Example: 1-18-7", "address_placeholder": "Enter street address", "building_name": "Building Name", "building_name_sub_label": "Example: NES Building 101", "building_name_placeholder": "Enter building name", "receive_mail_magazine": "Receive Email Newsletter", "choose_year_month_first": "You need to select both year and month.", "password_must_have_letter_digit": "The password must contain both letters and numbers.", "password_must_have_specific_chars": "The password must include special characters.", "password_not_match": "Password and confirmation password do not match.", "button": {"address_autofill": "Auto-fill Address", "sign_up": "Sign Up"}}, "account_verify": {"enter_verification_code": "Please enter the verification code sent to {0}.", "resend_pin_code_text": "Resend Code", "resend_pin_code_message": "Verification code has been resent to {0}.", "verification_code": "Verification Code", "button_next": "Next", "error_message": {"invalid_code": "This code is invalid. Please request a new code.", "incorrect_code": "The verification code is incorrect. Please check again."}}, "user_information": {"user_name": "Nickname", "postcode": "Postal Code", "municipality": "City/Ward/Town/Village", "address": "Neighborhood & Street Address", "title": "New Registration", "user_name_sub": "Nickname used within Fuku Wallet", "user_name_placeholder": "Enter your nickname", "gender": "Gender", "gender_male": "Male", "gender_women": "Female", "gender_others": "Other", "gender_private": "Prefer not to answer", "birthday": "Date of Birth", "birthday_placeholder": "Enter your date of birth", "postcode_description": "▼ Enter your postal code, and part of the address will be auto-filled.", "postcode_placeholder": "Enter your postal code", "prefectures": "Prefecture", "prefectures_placeholder": "Select", "municipality_placeholder": "Enter city/ward/town/village", "municipality_description": "▼ Enter the address from the neighborhood onward.", "address_placeholder": "Enter street address", "municipality_description_2": "Example: ○○ Town ⇒ ○○ Town, ○ Chome, ○ Ban, ○ Go", "building": "Building Name, etc.", "building_placeholder": "Enter building name", "btn_submit": "Register"}, "zodiac_register": {"title": "New Registration", "please_select": " Please select.", "select_1": "Your Zodiac Sign", "select_2": "Your Chinese Zodiac", "login_time": "During login,", "identity_verification": "Used for identity verification,", "to_do": "will be applied."}}, "application_form": {"title": {"picture_text": "Childbirth & Childcare Support Gift Application Form", "gift_text": "If you have already purchased and used the gift certificate, please apply using the button below.", "gift_info1": "Already purchased and used the gift certificate", "gift_info2": "for those who have", "header_text": "Purchaser Application Form", "comfirm_text": "Please check if the entered information is correct.", "regulation_text": "NESPay Terms of Use", "checkbox": "Agree to the Terms of Use", "warning_text": "Please check", "applicated": "Your application has been received."}, "label": {"set": "Number of Application Sets", "full_name": "Full Name", "fugirana": "<PERSON><PERSON><PERSON>", "postcode": "Postal Code", "prefectures": "Prefecture", "municipalities": "City/Ward/Town/Village", "townArea": "Neighborhood", "address": "Street Address", "buildingName": "Building Name", "contact": "Mobile Phone Number", "mailAddress": "Email Address"}, "sub_label": {"full_name": "Example: <PERSON><PERSON>", "fugirana": "Example: <PERSON><PERSON>", "postcode": "Example: 1234567", "municipalities": "Example: Koto-ku", "townArea": "Example: Shinkiba", "address": "Example: 1-18-7", "buildingName": "Example: NES Building 101", "contact": "Example: ***********", "mailAddress": "Example: <EMAIL>"}, "place_holder": {"full_name": "Enter your full name", "fugirana": "<PERSON><PERSON>", "postcode": "Enter your postal code", "prefectures": "Select", "municipalities": "Enter city/ward/town/village", "townArea": "Enter neighborhood", "address": "Enter street address", "buildingName": "Enter building name", "contact": "Enter your mobile phone number", "mailAddress": "Enter your email address"}, "step": [{"text": "Specify Number of Sets"}, {"text": "Enter Applicant Information"}, {"text": "Confirm Applicant Information"}, {"text": "Application Completed"}], "button": {"get_code": "Auto-fill Address", "navigate_confirm": "Go to Confirmation Screen", "back": "Back", "application": "Apply", "close": "Close"}, "warning_content": "The application completion email will be sent from {0}. Please ensure you can receive emails from {0}."}, "login": {"screen_title": "<PERSON><PERSON>", "user_id_placeholder": "Enter your registered email address", "password_sub_label": "At least 8 alphanumeric characters", "password_placeholder": "Enter your registered password", "incorrect_user_id_or_pass": "Incorrect user ID or password.", "title_sign_up": "If you do not have an account, please sign up.", "email_invalid_message": "Invalid email address.", "out_side_service_id_login": "Login with External Service ID", "button": {"sign_in": "<PERSON><PERSON>", "sign_up": "Sign Up", "forgot_password": "Forgot Password?", "line_login_button": "Login with LINE", "yahoo_login_button": "Login with Yahoo! JAPAN ID"}, "text": {"outOfPeriodTitle": "Outside Service Period", "periodDetail": "Available Service Period:", "close_button": "Close", "timeTile": "until"}}, "login000": {"login_Info": "If you already have an account, log in here.", "login_button": "<PERSON><PERSON>", "sign_up_info": "If you do not have an account, sign up here.", "sign_up_button": "Sign Up", "text": {"outOfPeriodTitle": "Outside Service Period", "periodDetail": "Available Service Period:", "close_button": "Close", "timeTile": "until"}}, "payment": {"payment_gift": {"used_gift_label": "Usage Target", "store_name_label": "Store Name", "payment_amount_label": "Payment Amount", "payment_point_label": "Points to Pay", "coupon_discount_label": "Coupon Discount", "store_input": {"title": "Specify the store to pay", "store_code_label": "Store Address (10-digit half-width number)", "store_code_sub_label": "Please check the store address at each store.", "store_code_placeholder": "Enter the store address"}, "input": {"title_1": "Enter the amount and", "title_2": "confirm the payment details.", "payment_amount_placeholder": "Enter the payment amount", "over_current_balance": "Please enter an amount within the balance.", "balance": "Balance"}, "confirm": {"header": "Confirm Payment Details", "title": "Please check your payment details", "date": "Payment Date & Time", "checkbox_text": "I have shown this screen to the store staff", "button": {"submit": "Pay with this information"}}, "complete": {"header": "Payment Completed", "title": "Payment has been completed", "payment_amount_after_discount": "Payment Amount After Discount", "button": {"to_top": "Go to {0} Top", "to_home": "Return to Home"}}}, "paysingle005": {"point": "You have received", "amount": "pt"}, "paymulti006": {"point": "You have received", "amount": "pt"}}, "reset_pass": {"header": "Password Reset", "input": {"title": "To verify your identity via email, please enter your email address.", "email_sub_label": "Example: <EMAIL>", "email_placeholder": "Enter your registered email address"}, "pin": {"info": "Please enter the verification code sent to {0}.", "resend_to": "Verification code has been resent to {0}.", "resend_code": "Resend Code", "pin_code": "PIN Code", "new_pin_code": "Verification Code", "exp_time": "This code is invalid. Please request a new code.", "wrong_pin_code": "The verification code is incorrect. Please check again."}, "zodiac": {"step_1": "Your Chinese Zodiac", "step_2": "Your Zodiac Sign", "choose": "Please select."}, "confirm": {"header": "Reset Password", "password_reset_label": "New Password", "password_reset_sub_label": "At least 8 alphanumeric characters", "password_reset_placeholder": "Enter your new password", "password_confirm_label": "Re-enter New Password", "password_confirm_sub_label": "At least 8 alphanumeric characters", "password_confirm_placeholder": "Re-enter your new password", "button": {"submit": "Change"}, "dialog": {"title": "Password Changed", "sub_title": "You can now log in with your new password"}, "password_notice": "The password must contain both letters and numbers.", "password_not_match": "Password and confirmation password do not match."}, "complete": {"header": "Change Completed", "title": "Password\nHas Been Changed", "back_to_login": "Return to Login"}, "button": {"send_code": "Send Verification Code"}, "text": {"email": "Invalid email format."}, "step": [{"text": "One-Time Password Authentication"}, {"text": "Reset Password"}, {"text": "Setup Completed"}]}, "store_detail": {"title": "Store Details", "business_hours_title": "Business Hours", "location_title": "Access", "route_public_transport_heading": "By Train/Bus", "route_car_heading": "By Car", "price_heading": "Can be used for Yen worth", "detail_pay_heading": "With your card or gift certificate", "regular_holiday": "Regular Holidays", "dealing_medal_headline": "Applicable Usage", "HP_link": "Official Store Website"}, "store_list": {"screen_name": "List of Stores", "show_more_item": "Show next {0} items", "search_narrow_down": "Search & Filter", "search_with_gift": "Available for Childbirth & Childcare Support Gift", "search_result": "Search Results", "unit": "items", "medal_service_tail": "can be used", "area_name_tail": "located in", "data_store_not_found": "No stores match the search criteria."}, "store_store": {"header": "Filter", "use_gift_certificate_point_headline": "Usage Target", "store_type_headline": "Type of Store", "area_headline": "Area (District)", "search_button": "Filter with this criteria", "keyword_headline": "Keyword", "keyword_input_placeholder": "Enter address or store name", "placeholder_dropdown": "All", "key_word": "Keyword"}, "charge": {"charge_optional": {"title": "Select a Deposit Method", "header": "Select a Charge Method", "credit_card": {"title": "Deposit with Credit Card", "text": "Credit Card", "message": "Charge the specified amount from your credit card", "button": "Charge with Credit Card"}, "convenient_store": {"title": "Deposit with Convenience Store Payment", "text": "Convenience Store Payment", "message": "Charge by completing the required procedure at a convenience store", "button": "Charge with Convenience Store Payment"}, "bankpay": {"message": "Charge via Bank Pay", "button": "Charge with Bank Pay"}, "bank": {"title": "Deposit with Bank Payment", "text": "Bank Payment", "message": "Charge via Pay-easy", "button": "Charge with Pay-easy"}, "serial_code": {"text": "Prepaid Card", "message": "Charge from a purchased prepaid card", "link": "Click here for prepaid card sales", "button": "Charge with Prepaid Card"}, "qr_code": {"text": "QR Code", "message": "Deposit by scanning a QR code", "button": "Deposit with QR Code"}, "qr_code_reading": {"text": "QR Code", "message": "Charge by scanning a QR code", "button": "Scan QR Code"}, "qr_code_display": {"text": "QR Code", "message": "Charge by displaying a QR code", "button": "Display QR Code"}}, "charge_input": {"card": {"title": "Enter Charge Amount", "text": "Credit Card", "message": "Deposit the specified amount from your credit card", "button": "Deposit with Credit Card"}, "cvs": {"title": "Enter Charge Amount", "text": "Convenience Store Payment", "message": "Deposit by completing the required procedure at a convenience store", "button": "Deposit with Convenience Store Payment"}, "bank": {"title": "Enter Charge Amount", "text": "Bank Payment", "message": "Deposit via internet banking", "button": "Deposit with Bank Payment"}, "message1": "Select an amount to deposit or", "message2": "enter the amount directly in the form", "label": "Enter Amount", "sub_label": "Charges must be in 1,000 yen units", "place_holder": "Enter amount", "deposit_charge_limit": "You can deposit up to {0} yen", "deposit_charge_limit_1": "You can charge up to", "deposit_charge_limit_2": "yen", "button": {"cancel": "Cancel", "confirm": "Confirm"}}, "charge_confirm": {"title": "Confirm Charge Details", "message": "Please check the deposit details", "total_text": "Charge Amount", "amount": "Purchase Amount", "confirm_value": "Total Deposit Amount", "message_footer": "When you press the 'Confirm' button, the payment screen of our partner DG Financial Technology will be displayed. Please proceed with the payment process.", "accept": "Confirm", "message_err": "An error has occurred.", "error_text_1": "The product does not exist, so charging is not possible.", "error_text_4": "After charging, the balance will exceed", "error_text_4_1": "yen, so charging is not possible.", "error_text_default": "Charging is not possible."}, "charge_complete": {"card": {"title": "Charge Completed", "made_deposit": "Charged Successfully", "charge_date_title": "Expiration Date"}, "cvs": {"title": "Charge Completed", "made_deposit": "Charged Successfully", "charge_date_title": "Expiration Date"}, "bank": {"title": "Charge Completed", "made_deposit": "Charged Successfully", "charge_date_title": "Expiration Date"}, "reason": "Childbirth & Childcare Support Gift", "unit": "Yen", "button": "Return to Home", "expire_date": "Expiration Date:", "message_err": "Charge failed.", "message_err1": "An error occurred during the payment process."}, "charge_gift": {"quantity": {"button_buy": "Purchase", "title": "Purchase Gift Certificates", "number_of_purchases": "Specify Quantity", "unit": "Set", "minutes": "Minutes"}, "method": {"title": "Select Charge Method", "description_1": "Please select a charging method.", "description_2": "The payment screen of our partner DG Financial Technology will be displayed. Please proceed with the payment process.", "credit_type": "Credit Card", "credit_type_description": "Purchase the specified amount using a credit card", "button_charge_credit": "Purchase with Credit Card", "convenience_store_type": "Convenience Store Payment", "convenience_store_type_description": "Complete the required procedure at a convenience store to purchase", "button_convenience_store": "Purchase with Convenience Store Payment", "error": {"can_not_purchase": "The product does not exist, so charging is not possible.", "can_not_charge": "Charging is not possible.", "unknown": "An error has occurred.", "balance_after_charging": "After charging, the balance will be", "can_not_charge_because_exceeds": "yen, so charging is not possible."}}, "complete": {"title": "Charge Completed", "unit": "Set", "description": "Purchased Successfully", "reason": "Childbirth & Childcare Support Gift"}}, "charge_qr_code": {"scan": {"title": "Deposit via QR Code"}, "input": {"title": "Deposit via QR Code", "lbl_input_deposit": "Enter Deposit Code", "sub_lbl_input_deposit": "The deposit code is the 10-character alphanumeric code located below the QR code.", "placeholder_input_deposit": "Enter deposit code", "btn_submit": "Confirm"}, "complete": {"title": "Charge Completed", "unit": "Set", "description": "Charge Successful", "reason": "Childbirth & Childcare Support Gift"}}, "charge_qr_code_read": {"001": {"title": "Charge via QR Code"}, "002": {"title": "QR Code Charge", "lbl_input_deposit": "Enter Charge Code", "sub_lbl_input_deposit": "The charge code is the 10-character alphanumeric code located below the QR code.", "placeholder_input_deposit": "Enter here", "btn_submit": "Confirm"}, "003": {"title": "Charge Completed", "unit": "Set", "description": "Charge Successful", "reason": "Childbirth & Childcare Support Gift", "deposited": "Charged Successfully", "acquired": "Acquired", "yen": "Yen", "point": "pt"}}, "qr_common": {"qr_description": "Unable to read QR code", "qr_header": "Point the camera at the QR code", "permission_error": "Camera Launch Error", "close_dialog_permission": "Close", "guide_permission": "Camera access was denied.\nIf you have denied camera access, please refresh the browser and allow camera access,\nor enter the destination manually or select from the address book.\nIf the confirmation screen does not appear, for iOS, go to Settings > Safari > Allow Camera Access.\n(This feature is supported only on the latest Safari for iOS and the latest Chrome for Android.)"}, "charge_serial_code": {"scan": {"tap_serial_code": "Tap the serial code", "read_failed": "Read Failed", "scan_failed": "Failed to read the serial code.", "permission_error": "Camera Launch Error", "permission_error_message": "An error occurred while launching the camera.\nPlease check if the camera is connected or if camera access is allowed.", "qr_title": "Serial Code Scan", "serial_code_scan_header": "Point the camera at the serial code", "camera_not_ready": "Camera is not ready!", "read_serial_code_failure": "Failed to read", "btn_capture": "Capture", "btn_confirm": "Confirm"}, "input": {"title": "Enter Serial Code", "error": {"coupon_error": "Failed to retrieve coupon information. Please refresh the screen.\nIf the issue persists, please try again later.", "fail_to_charge": "Charge failed.", "code_not_exist": "The entered serial code does not exist.", "code_been_used": "The entered serial code has already been used.", "invalid": "Invalid serial code.", "balance_error_1": "After charging, the balance will exceed", "balance_error_2": "yen, so charging is not possible."}, "description": "The serial code is a 16-character alphanumeric code printed on the back of the prepaid card.", "btn_read": "Read", "enter_serial_code": "Enter Serial Code", "input_serial_code_placeholder": "Enter here", "btn_submit": "Confirm"}, "complete": {"title": "Charge Completed", "unit": "Yen", "description": "Charge Successful", "reason": "Childbirth & Childcare Support Gift"}}, "charge_bankpay": {"title": "BankPay Payment Test", "button": {"register": "Register"}, "messages": {"success": "Membership registration completed."}}}, "regulation": {"regulation_omission": "…Omitted…", "term_of_service_title": "Terms of Service", "commercial_law_title": "Commercial Law Notation"}, "history": {"title": "History & Expiry", "moment_locate": "ja", "moment_weekdays_short": ["Sun", "Mon", "<PERSON><PERSON>", "Wed", "<PERSON>hu", "<PERSON><PERSON>", "Sat"], "history_transaction_list": {"label_history_transaction": "History", "moment_format": "YYYY/MM/DD (ddd) HH:mm", "history_transaction_expiry": "Expiry Date:", "history_empty": "No history available", "button": "History"}, "history_expiry_list": {"label_expiry": "Expiry Date", "moment_format_no_time": "YYYY/MM/DD (ddd)", "expiry_day_heading": "Expiration Date", "balance_heading": "Balance", "button": "Expiry"}, "medal_history_list": {"label_history_transaction": "History", "moment_format": "YYYY/MM/DD (ddd) HH:mm", "history_transaction_expiry": "Expiry Date:", "history_empty": "No history available", "button": "History", "show_more_item": "Show next {0} items"}, "medal_expiration_list": {"label_expiry": "Expiry Date", "moment_format_no_time": "YYYY/MM/DD (ddd)", "expiry_day_heading": "Expiration Date", "history_empty": "No history available", "balance_heading": "Balance", "button": "Expiry", "label-amount": "Amount", "label_date": "Purchase Date"}}, "contact": {"option_select1": "About Registration", "option_select2": "About Login", "option_select3": "About Application", "option_select4": "About Purchase", "option_select5": "About Payment", "option_select6": "About Partner Stores", "option_select7": "About Usage Environment", "option_select8": "About Device Operation", "option_select9": "Other", "header": "Inquiry Details", "label_select": "Please select the most relevant inquiry category", "placeholder_select": "Select", "placeholder_inquiry_detail": "Please enter details", "label_inquiry_detail": "Please provide detailed information", "placeholder_email": "Please enter your email", "label_email": "Reply <PERSON><PERSON> Address", "button_submit": "Submit", "dialog_title_top": "Thank you for your inquiry", "dialog_title_bot": "We appreciate your contact", "dialog_message": "Our representative will get back to you shortly.", "text_inquiry": "Up to 500 characters"}, "faq": {"title": "Frequently Asked Questions", "introduce_login_heading": "About Login", "other_heading": "Other", "introduce_login_content": {"heading": "How can I create an account?", "first_body1": "From the hamburger menu,", "mid_body1": "tap the Sign-Up button", "last_body1": "to register as a new member.", "body2": "To complete new member registration, you need to set an email address, password, and phone number."}, "other_content": {"panel2": {"heading": "I forgot my password.", "first_body1": "On the login screen within My Page, tap", "mid_body1": "Forgot Password?", "last_body1": "to reset your password.", "body2": "If you have not completed the initial registration, please return to the initial screen and select 'Sign Up' to register."}, "panel3": {"heading": "Is there a limit on coin usage?", "first_body1": "The balance, including premium amounts, is limited to less than 50,000 yen.", "mid_body1": "There are no restrictions on payments", "last_body1": "."}, "panel4": {"heading": "I forgot my zodiac sign and Chinese zodiac.", "first_body1": "If you forgot your zodiac sign and Chinese zodiac,", "mid_body1": "please contact us through the 'Contact' option in the hamburger menu."}}, "user_registration": {"heading": "About User Registration", "child1": {"heading": "How do I register as a user?", "first_body1": "On the top page,", "mid_body1": "tap the Login/Sign-Up button", "mid2_body1": "to transition to the login screen, then", "mid3_body1": "tap the Sign-Up button", "last_body1": "to complete user registration.", "body2": "You need to set an email address, password, and mobile phone number. User registration is only possible using the email address and mobile phone number used during the '〇〇' application process."}, "child2": {"heading": "I didn't receive an SMS with a PIN code after entering my mobile phone number. What should I do?"}}, "purchase_pay": {"heading": "About Purchasing (Charging) 〇〇"}, "payment_pay": {"heading": "About Payments & Shopping with '〇〇'"}, "usage_environment": {"heading": "About Usage Environment"}, "terminal_operation": {"heading": "About Device Operation"}, "contact": {"title1": "If the issue is not resolved,", "title2": "please contact us.", "contact": "Contact Us"}}, "login_open_id": {"heading-1": "Each OpenID", "heading-2": "Authentication Sequence", "btn-title": "<PERSON><PERSON>"}, "menu": {"account_verified": "Identity Verification", "setting": "Settings", "guest": "Guest", "button": {"account_verify": "Verify Identity", "sign_out": "Sign Out"}, "items": {"home": "Home", "wallet": "Wallet", "external_link": "External Link", "news": "News", "nft_market": "NFT Market", "language_select": "Language Selection", "terms_of_service": "Terms of Service", "commercial_law": "Commercial Law Notation", "faq": "FAQ", "coupon": "Coupon", "event_list": "Events", "event_form": "Application Form", "brand_link": "Regional List", "store": "Stores", "stamp_rally": "Stamp Rally", "mission": "Mission", "logout": "Logout", "item": "<PERSON><PERSON>", "ticket": "Ticket", "collection": "Collection"}, "confirmed": "My Number Card Verified", "unconfirmed": "My Number Card Verification Incomplete", "btn-identity": "Verify Identity", "menu002": {"header_title": "Language Choice", "label_ja": "Japanese", "label_en": "English"}}, "identification001": {"title": "Start Identity Verification", "card_info": {"title": "<PERSON>an My Number Card", "description": "Verify your identity by reading the IC chip on the card"}}, "header": {"news": "News", "menu": "<PERSON><PERSON>"}, "footer": {"items": {"home": "Home", "search": "Search", "payment": "Pay", "news": "Notice", "menu": "<PERSON><PERSON>", "wallet": "Wallet", "collection": "Collection", "community": "Community"}, "text": {"outOfPeriodTitle": "Service Period Expired", "periodDetail": "Available Service Period:", "close_button": "Close", "timeTile": "Until"}}, "news": {"news_for_you": "News for You", "important_news": "Important News", "loadmore": "Show next {0} items", "news_detail": "News Details", "button": {"home": "Back to Home"}}, "coupon": {"screen_title": "Coupon List", "coupon_detail": {"title": "Coupon Details", "dialog_confirm": {"activate_coupon_text": "Mark coupon as used.", "activate_coupon_question": "Are you sure?", "operation_staff_text": "This operation should be done", "confirm_again_text": "in confirmation with store staff.", "execution_button": "Execute", "cancel_button": "Cancel"}, "to": "Until", "detail_name_coupon_label": "Coupon Details", "store_name_label": "Store", "end_date_time_label": "Expiration Date", "address_label": "Address", "go_map_button": "View in Map App", "notice_label": "Important Notes", "use_flag_label": "Usage Status", "used_coupon": "Used", "not_used_coupon": "Not Used", "use_button": "Use", "use_button_disable": "Used", "go_coupon_list_button": "Item List"}, "coupon004": {"screen_title": "Coupon Confirmation", "title_dialog_success": "Coupon has been marked as used", "title_checkbox": "I have shown this screen to the store staff", "ticket_advisory_confirmation": "This operation should be done\nin confirmation with store staff."}, "available_coupon_tab": "Available", "used_coupon_tab": "Used", "btn_search": "Filter with this criteria", "date_use": "Scheduled Usage Date", "title_search": "Keyword Search", "location": "Location (Hot Springs / Prefecture)", "show_more_item": "Show next {0} items", "title_popup_search": "Filter", "place_holder_key_word": "Keyword", "start_date": "Start Date", "end_date": "End Date", "used_coupon": "Used", "text_btn_use": "Use", "text_from": "Until", "text_use_any_times": "Unlimited Use!", "text_no_coupon": "No coupons available.", "filter": {"dialog_title": "Filter", "headline": "Usage Status", "btn_search": "Filter", "btn_reset": "Reset"}}, "home": {"click_here_for_details": "Click here for details", "selected": "Selected", "view_details": "Details", "find_store": "Search for\na shop", "deposit_save": "Charge", "charge_coins": "Charge Coins", "pay_with_this": "Payment", "collection": "Collection", "find_nft": "Find NFT", "history": "History", "gift": "Send\nReceive", "usage": "Usage", "open": "Open", "close": "Close", "campaign": "Campaign", "see_more": "See more", "coupon": "Coupon", "event_ticket": "Event", "coupon_use": "Use", "balance": "Balance", "balance_near_expire": "There is a balance nearing expiration", "to": "Until", "purchase_button_title": "Buy", "event_date": "Event Date:", "title": "Community", "memberIcon": "Member ID", "chatIcon": "Cha<PERSON>", "votingIcon": "Vote", "jogging": "Event", "chat": "Popular Posts", "ticket_type": {"paid": "Tickets available (Paid)", "free": "Tickets available (Free)", "no_ticket": "No ticket available"}, "event_ticket_button": "Use", "coupon_store_name": "Dealer", "coupon_end_date_time": "Date of Expiry", "ticket_date_time": "Open Time", "text_from": "To", "limited_store": "Members Only", "limited_store_intro_text": "Special offer just for you! Enjoy a limited menu that is different from usual", "limited_store_use": "Detail", "text": {"outOfPeriodTitle": "Outside Service Period", "periodDetail": "Available Service Period:", "close_button": "Close", "timeTile": "Until"}}, "usage": {"title_header_charging": "How to Deposit", "title_header_payment": "How to Make a Payment", "title_header_history": "How to Check History"}, "survey": {"screen_name": "Survey", "reception_period": "Reception Period:", "to": "Until", "survey001": {"input_placeholder": "If you selected 'Other,' please provide details", "prefectures_select_box": "Go to Confirmation Screen", "municipalities_info": "Cancel", "has_ended": "has ended"}, "multiple_select": "※ Multiple selections allowed", "survey002": {"header": "Confirmation Screen", "heading": "Please check if the entered information is correct", "fix_btn": "Modify", "send_btn": "Submit", "warning_text": "If you do not agree to the handling of personal information, you cannot submit the form.", "terms_button": "About the Handling of Personal Information", "terms_check": "I agree to the handling of personal information"}, "survey003": {"header": "Reception Completed", "text": "Survey has been submitted", "button": "Close", "back_to_home": "Return to Home", "survey_end": "The survey reception period\nhas ended", "reception_period": "Reception Period"}}, "paymulti": {"title_header_scan": "Payment", "title_header_confirm": "Confirm Payment Details", "title_header_complete": "Payment Completed", "title_confirm": "Please check your payment details", "gift_certificates": "Gift Certificates / Points to Use", "store_name": "Store Name", "payment_amount": "Payment Amount", "title_checkbox": "I have shown this screen to the store staff", "confirm_checkbox": "Pay with this information", "payment_complete": "Payment has been completed", "return_home": "Return to Home", "determine_payment_amount": "Determine Payment Amount", "at_store": "At this store", "available_balance": "Available Balance", "not_found_store": "Invalid store address", "amount_error": "Please enter an amount within the balance.", "store_to_pay": "Specify the store to pay", "store_address_not_exist": "The entered store address does not exist.", "unit": "Yen", "place_holder_input_store": "Enter store address", "label_input_store": "Store Address (10-digit half-width number)", "sub_label_input_store": "Please check the store address at each store", "place_holder_payment": "Enter payment amount", "label_payment": "Payment Amount", "button_back": "Back", "button_next": "Next", "balance": "Balance", "use": "Use", "gift_certificates_point": "Available Usage", "what_to_use": "Select Usage", "remaining_payment_amount": "Remaining Payment Amount"}, "charge_vouchr": {"charge_vouchr001": {"header_title": "Purchase Gift Certificate", "purchase_info": "Specify Quantity", "done_button": "Confirm", "required_text": "Required", "product_volume": "Yen / 1 Set", "product_premium_value": "Yen Value", "product_unit": "Set"}, "charge_vouchr002": {"header_title": "Select Purchase Method", "chage_info": "Please select a purchase method.", "settlement_agency_info": "The payment screen of our partner DG Financial Technology will be displayed. Please proceed with the payment process.", "credit_card_info_header": "Credit Card", "credit_card_info_content": "Charge the specified amount from your credit card", "credit_card_done_button": "Charge with Credit Card"}, "charge_vouchr003": {"header_title": "Purchase Completed", "description_title": "Purchase Successful", "date_title": "Expiration Date:"}}, "ticket": {"ticket001": {"screen_title": "Owned Ticket List", "available_button": "Available", "already_used_button": "Used", "event_display_button": "Show next 5 items", "free": "Free", "text_no_ticket": "No coupons available.", "price_unit": "Yen", "no_date_time_specified": "No specified date or time", "no_date_specified": "No specified date", "no_time_specified": "No specified time"}, "ticket002": {"screen_name": "Ticket Details", "event_ticket_price": {"unit": "Yen", "free": "Free"}, "date_headline": "Date", "time_headline": "Time", "inquiry_headline": "Inquiry", "notes_headline": "Important Notes", "purchase_date_headline": {"free": "Acquisition Date", "charge": "Purchase Date"}, "reserve_id_headline": "Reservation Number", "expiration_headline": "Expiration Date", "usage_headline": "Usage Status", "unused": "Not Used", "used": "Used", "usedButton": "Used", "useButton": "Use", "ticket_list_button": "Event Details"}, "ticket003": {"screen_name": "Ticket Confirmation", "ticket_use_confirmation": "Mark the ticket as used.\nAre you sure?", "ticket_advisory_confirmation": "This operation should be done\nin confirmation with store staff.", "execution_button": "Execute", "cancel_button": "Cancel", "price_unit": "Yen", "reserve_id": "Reservation Number", "useButton": "Use", "title_checkbox": "I have shown this screen to the staff."}, "ticket004": {"ticket_changed": "The ticket has been marked as used."}}, "stamp_rally": {"stamp_rally001": {"screen_name": "Stamp Rally List", "show_more_item": "Show next {0} items", "data_stamp_rally_not_found": "No stamp rally available", "stamp_rally_refinement": {"all": "All", "in_session": "Ongoing", "before_session": "Upcoming"}, "situation_info": "Achieved", "filter": {"dialog_title": "Filter", "text_search": "Filter", "headline": "Event Status", "btn_search": "Filter", "btn_cancel": "Reset"}}, "stamp_rally002": {"error_get_location": "Failed to retrieve location information.", "error_permission_location": "Location access is not permitted.", "mission": {"achieved": "Achieved", "not_achieved": "Not Achieved"}, "reward": {"obtained": "Obtained", "not_obtained": "Not Obtained"}, "clear_rule": {"not_written": "Not Specified", "achieved_mission": "Complete {0} MISSION(s)", "scan_qr": "Scan QR code on-site", "purchase_qr": "Scan QR code on-site to purchase", "clear": "Clear when within the pin area", "achieved_all": "Complete all stamps", "load_qr": "Scan QR code"}, "display": {"map": "Switch to Map View", "list": "Switch to List View"}, "qr_button": "Scan QR Code", "location_button": "Current Location", "date_time": "Until {0}"}, "stamp_rally003": {"incentive_header": "Completion Rewards"}, "rally004": {"screen_name": "Scan QR Code", "info_text": "Point the camera at the QR code", "qr_unreadable_info": "Unable to read QR code", "scan_error": "Invalid code"}, "rally005": {"screen_name": "Spot Code", "heading": "Enter Spot Code", "code_info": "The spot code is a 10-character alphanumeric code located below the QR code", "code_input_placeholder": "Enter spot code"}, "rally007": {"message_success": "Stamp has been applied"}, "stamp_rally006": {"screen_title": "Spot Details", "mission": {"achieved": "Achieved", "not_achieved": "Not Achieved"}, "reward": {"obtained": "Obtained", "not_obtained": "Not Obtained"}, "to": "Until", "stamp_detail_title": "Stamp Details", "contact_title": "Contact", "address_title": "Access", "notes_title": "Important Notes", "incentive_title": "Completion Rewards", "name_qr_code_btn1": "Obtain", "name_qr_code_btn2": "Scan QR Code", "name_qr_code_btn3": "Unlock when all MISSIONS are completed", "name_qr_code_btn4": "Use", "name_qr_code_btn5": "Collection", "back_button": "Back", "mission_complete_date": "Completion Date: {0}", "get_reward_date": "Acquisition Date: {0}"}, "stamp_rally007": {"guidance_coupon": "You have received a coupon", "guidance_coin": "You have received a coin", "guidance_nft": "You have received an NFT", "guidance_stamp": "You have received a stamp", "nft_info1": "It may take a few minutes to issue", "nft_info2": "Issued NFTs can be checked in", "nft_info3": "Collection", "nft_info4": "section."}}, "paysingle001-main-search-storepubliccode-001-ERROR": "Invalid request parameters.", "paysingle001-main-search-storepubliccode-002-ERROR": "A system error has occurred. Please try again later.ase try again later.", "paysingle001-main-search-storepubliccode-003-ERROR": "The store address is invalid.", "paysingle001-main-search-storepubliccode-004-ERROR": "A system error has occurred. Please try again later.ase try again later.", "paysingle001-main-search-storepubliccode-005-ERROR": "The store address is invalid.", "paysingle004-main-execute-payment-001-ERROR": "Invalid request parameters.", "paysingle004-main-execute-payment-002-ERROR": "A system error has occurred. Please try again later.ase try again later.", "paysingle004-main-execute-payment-003-ERROR": "A system error has occurred. Please try again later.ase try again later.", "paysingle004-main-execute-payment-004-ERROR": "A system error has occurred. Please try again later.ase try again later.", "paysingle004-main-execute-payment-005-ERROR": "A system error has occurred. Please try again later.ase try again later.", "paysingle004-main-execute-payment-006-ERROR": "A system error has occurred. Please try again later.ase try again later.", "paysingle004-main-execute-payment-007-ERROR": "A system error has occurred. Please try again later.ase try again later.", "paysingle002-main-search-storepubliccode-001-ERROR": "Invalid request parameters.", "paysingle002-main-search-storepubliccode-002-ERROR": "A system error has occurred. Please try again later.ase try again later.", "paysingle002-main-search-storepubliccode-003-ERROR": "The entered store address does not exist.", "paysingle002-main-search-storepubliccode-004-ERROR": "A system error has occurred. Please try again later.ase try again later.", "paysingle002-main-search-storepubliccode-005-ERROR": "The store address is invalid.", "paymulti005-main-execute-payment-001-ERROR": "Invalid request parameters.", "paymulti005-main-execute-payment-002-ERROR": "A system error has occurred. Please try again later.ase try again later.", "paymulti005-main-execute-payment-003-ERROR": "A system error has occurred. Please try again later.ase try again later.", "paymulti005-main-execute-payment-004-ERROR": "A system error has occurred. Please try again later.ase try again later.", "paymulti005-main-execute-payment-005-ERROR": "A system error has occurred. Please try again later.ase try again later.", "paymulti005-main-execute-payment-006-ERROR": "A system error has occurred. Please try again later.ase try again later.", "paymulti005-main-execute-payment-007-ERROR": "A system error has occurred. Please try again later.ase try again later.", "paymulti005-main-execute-payment-008-ERROR": "A system error has occurred. Please try again later.ase try again later.", "paymulti005-main-execute-payment-009-ERROR": "A system error has occurred. Please try again later.ase try again later.", "paymulti005-main-execute-payment-010-ERROR": "A system error has occurred. Please try again later.ase try again later.", "paymulti003-init-get-availablecoinid-001-ERROR": "Invalid request parameters.", "paymulti003-init-get-availablecoinid-002-ERROR": "A system error has occurred. Please try again later.ase try again later.", "paymulti003-init-get-availablecoinid-003-ERROR": "A system error has occurred. Please try again later.ase try again later.", "paymulti002-main-search-storepubliccode-001-ERROR": "Invalid request parameters.", "paymulti002-main-search-storepubliccode-002-ERROR": "A system error has occurred. Please try again later.ase try again later.", "paymulti002-main-search-storepubliccode-003-ERROR": "The store address is invalid.", "paymulti001-main-search-storepubliccode-001-ERROR": "Invalid request parameters.", "paymulti001-main-search-storepubliccode-002-ERROR": "A system error has occurred. Please try again later.ase try again later.", "paymulti001-main-search-storepubliccode-003-ERROR": "The store address is invalid.", "history001-init-get-history-list-001-ERROR": "Invalid request parameters.", "history001-init-get-history-list-002-ERROR": "A system error has occurred. Please try again later.ase try again later.", "history001-init-get-history-list-003-ERROR": "A system error has occurred. Please try again later.ase try again later.", "history001-init-get-history-list-004-ERROR": "A system error has occurred. Please try again later.ase try again later.", "chargevouchr002-main-execute-charge-001-ERROR": "Invalid request parameters.", "chargevouchr002-main-execute-charge-002-ERROR": "A system error has occurred. Please try again later.ase try again later.", "chargevouchr002-init-get-clientkey-001-ERROR": "Invalid request parameters.", "chargevouchr002-init-get-clientkey-002-ERROR": "A system error has occurred. Please try again later.ase try again later.", "chargeselect001-init-get-clientkey-001-ERROR": "Invalid request parameters.", "chargeselect001-init-get-clientkey-002-ERROR": "A system error has occurred. Please try again later.ase try again later.", "news001-init-get-important-news-unread-flag-001-ERROR": "A system error has occurred.", "news001-init-get-important-news-unread-flag-002-ERROR": "A system error has occurred.", "news001-init-get-personal-news-unread-flag-001-ERROR": "A system error has occurred.", "news001-init-get-personal-news-unread-flag-002-ERROR": "A system error has occurred.", "ticket001-init-get-event-ticket-list-001-INFO": "", "ticket001-init-get-event-ticket-list-002-ERROR": "A system error has occurred.", "ticket001-init-get-event-ticket-list-003-ERROR": "A system error has occurred.", "ticket001-init-get-event-ticket-list-004-ERROR": "A system error has occurred.", "ticket001-init-get-event-ticket-list-005-ERROR": "A system error has occurred.", "ticket001-init-get-event-ticket-list-006-ERROR": "A system error has occurred.", "event": {"screen_title": "Event List", "show_more_item": "Show next {0} items", "search_button": "Search", "search_placeholder": "Search & Filter", "squeeze_search_Button": "Filter", "ticket_link": "Owned Tickets", "data_not_found": "No events available", "search_event": "Search results for '{0}': {1} items", "event_002": {"title": "Filter", "search_label": "Keyword Search", "search_placeholder": "Keyword", "rearrange_label": "Sort", "location_label": "Location (Prefecture)", "location_placeholder": "All", "event_date_label": "Event Period", "reset_filter_button": "Reset", "filter_button": "Filter", "start_date": "Start Date", "end_date": "End Date"}, "event_003": {"title": "Event Details", "ticket_sale_term_headline": "Ticket Sales Period", "ticket_sale_term_empty": "No sales period", "precautions_message": "※ Tickets must be purchased in advance.", "precautions_free_message": "※ Tickets must be obtained in advance.", "out_of_period_date": "Outside Ticket Sales Period", "buy_ticket": "Purchase Admission Ticket", "ticket_out_of_stock": "Tickets are sold out", "ticket_free": "Get Ticket", "sns_headline": "Official Event SNS", "hp_headline": "Official Event Website", "event_detail_headline": "Event Details", "open_date_headline": "Event Date", "open_google_map": "View in Map App", "train_headline": "By Train/Bus", "car_headline": "By Car", "ticket_headline": "Admission Fee", "precautions_headline": "Important Notes", "location_title": "Access", "ticket_buy_free": "Get Ticket", "ticket_buy_paid": "Purchase Admission Ticket", "status_Clear": "Sold Out", "status_ChangeHistory": "Limited Availability", "status_PanoramaFishEye": "On Sale", "no_time": "No specific time", "free_price": "Free", "total": "Total", "sheet_headline": "Number of Tickets", "sheet_unit": "Tickets", "ticket_free_button": "Get", "ticket_paid_button": "Purchase", "message_check_balance": "Insufficient balance", "message_ticket_sale_check": "Only {0} tickets remaining.\nYou cannot {1} more than {0} tickets.", "message_max_buy_check": "You can only {0} up to {1} tickets at a time."}, "event_004": {"title": "Payment", "title_content": "Remaining Payment Amount", "back_button": "Back", "next_button": "Next", "close_button": "Close", "back_home_button": "Home", "error_message": "Insufficient balance", "hint_message": "Please deposit funds", "payment_amount_remaining": "Remaining Payment Amount", "list_payment_item": "Available Options"}, "event_005": {"title_free": "Confirm Details", "title_paid": "Confirm Payment Details", "paid_headline": "Please check your payment details", "free_headline": "Please check the details", "ticket_free_button": "Get", "ticket_paid_button": "Purchase", "cancel_button": "Cancel", "date_headline": "Date", "time_headline": "Time", "sheet_headline": "Number of Tickets", "payment_date_headline": "Payment Date", "paymen_coin_deadline": "Available Usage", "payment_amount_headline": "Payment Amount", "ticket": "Ticket", "point": "Point", "processing_text": "Processing"}, "event_006": {"title_free": "Acquisition Complete", "title_paid": "Purchase Complete", "free_text": "Acquired", "paid_text": "Purchased", "payment_completed_info_free": "You have\nacquired a ticket", "payment_completed_info_paid": "You have\npurchased a ticket", "back_home_button": "Return to Home", "back_ticket_list_button": "Owned Ticket List", "ticket_bought_text": "You have {0} a ticket", "confirm_bought_text1": "The {0} ticket", "confirm_bought_text2": "can be checked in", "confirm_bought_text3": "Items"}}, "charge_qr_code_present_001": {"header_title": "QR Code Display", "transfer_code_title": "[QR Code Number]"}, "event_form002": {"screen_title": "Event Application Form", "mess_success": "Survey has been submitted", "mess_fail": "The sample event application form has ended", "reception_period": "Reception Period:", "to": "Until", "input_placeholder": "If you selected 'Other,' please provide details", "prefectures_select_box": "Go to Confirmation Screen", "municipalities_info": "Cancel"}, "event_form": {"event_form002": {"confirmation": "Please check if the entered information is correct", "screen_title": "Confirm Entered Information", "fix_btn": "Modify Entered Information", "send_btn": "Submit"}, "event_form003": {"text": "Event application has been received", "button": "Close"}, "event_form001": {"screen_title": "Event Application Form", "show_more_item": "Show next {0} items", "data_event_form_not_found": "No application forms available", "event_form_refinement": {"all": "All", "in_session": "Ongoing", "before_session": "Upcoming"}}}, "mission": {"mission001": {"screen_name": "Mission List", "show_more_item": "Show next {0} items", "data_mission_not_found": "No missions available", "situation_info": "Achieved", "search_title": "Filter", "search_title_1": "Event Status", "reset_btn": "Reset", "search_btn": "Filter", "search_checkbox_option_1": "Ongoing", "search_checkbox_option_2": "Upcoming", "search_checkbox_option_3": "Ended"}, "mission002": {"show_more_item": "Show next {0} items", "data_mission_not_found": "No missions available", "situation_info": "Achieved", "incentive_title": "Completion Rewards", "reward": {"achieved": "Achieved", "unachieved": "Not Achieved"}, "conditiontype": {"login": "<PERSON><PERSON>", "balance": "Charge", "payment": "Payment", "event": "Event", "qrcode": "Scan QR Code", "qrcodeocation": "Scan GPS or QR Code on-site"}, "mission_key": "MISSION", "check_point_key": "CHECK POINT", "name_qr_code_btn1": "Use", "name_qr_code_btn2": "Acquired", "name_qr_code_btn3": "Unlock when all MISSIONS are completed", "current_status": "Current Status", "mission_list": "Mission List", "mission_tips": "Tips for Completing Missions"}, "mission003": {"screen_title": "Mission Details", "reward": {"obtained": "Achieved", "not_obtained": "Not Achieved"}, "to": "Until", "stamp_detail_title": "Stamp Details", "contact_title": "Contact", "address_title": "Address", "notes_title": "Important Notes", "incentive_title": "Completion Rewards", "name_qr_code_btn1": "Achieve by scanning GPS", "name_qr_code_btn2": "Achieve by scanning QR Code", "name_qr_code_btn3": "Use", "name_qr_code_btn4": "Achieved", "back_button": "Back", "mission_complete_date": "Completion Date: {0}", "get_reward_date": "Acquisition Date: {0}", "message_success": "Mission completed"}, "mission004": {"screen_name": "Scan QR Code", "message_success": "Mission completed"}, "mission005": {"screen_name": "Mission Code", "heading": "Enter Mission Code", "code_info": "The mission code is a 10-character alphanumeric code located below the QR code", "code_input_placeholder": "Enter mission code", "message_success": "Mission completed"}, "mess_coupon_success": "Coupon acquired", "mess_coin_success": "Coin acquired"}, "ec": {"ec_item001": {"title": "Product List", "cart": "Shopping Cart", "purchase_history": "Purchase History", "search_placeholder": "What are you looking for?", "next_button_text": "Show next {0} items", "no_result": "No products available.", "tax_label": "(Tax Included)"}, "ec_item002": {"header": "Product Details", "shopping_cart": "Shopping Cart", "purchase_history": "Purchase History", "quantity": "Quantity", "item_description": "Product Description", "shop_info": "Shop Information", "shop_name": "Shop Name", "business_hours": "Business Hours", "access": "Access", "view_with_map_app": "View in Map App", "add_to_cart": "Add to Cart", "go_to_cart": "Proceed to Cart", "tax_included": "(Tax Included)", "added_to_shopping_cart": "Added to Shopping Cart", "cannot_add_more": "Cannot add more", "cannot_proceed": "Cannot add to cart due to ongoing payment processing.", "product_limit_error": "The product quantity exceeds the limit and cannot be added to the cart.", "product_addition_error": "The order quantity exceeds the allowed purchase limit and cannot be added to the cart.", "add_product_success": "Product added to the shopping cart.", "out_of_stock": "Out of Stock", "return_to_shopping_top_page": "Return to Shopping Home"}, "ec_cart001": {"header": "Shopping Cart", "quantity": "Quantity", "product_is_currently_unavailable_for_purchase": "This product is currently unavailable for purchase", "quantity_exceeds_stock": "The quantity exceeds available stock", "please_check_product_details": "Please check product details", "go_to_product_details": "Go to Product Details", "delete": "Delete", "not_complete_successfully": "Previous payment process was not completed successfully.", "subtotal": "Subtotal", "merchandise": "Items", "unit": "Yen", "proceed_to_purchase_procedure": "Proceed to Purchase", "return_to_shopping_top_page": "Return to Shopping Home", "no_products": "No products in the shopping cart", "tax_label": "(Tax Included)"}, "item_status": {"has_deleted": "This product has been deleted", "currently_unavailable_for_purchase": "This product is currently unavailable for purchase", "not_yet_available_for_sale": "This product is not yet available for sale", "no_longer_available_for_sale": "This product is no longer available for sale"}, "ec_payment001": {"header": "Select Payment Method", "step": [{"text": "Shopping Cart"}, {"text": "Purchase Process"}, {"text": "Payment Method"}, {"text": "Purchase Completed"}], "title1": "Confirm Payment Method", "title2": "※ Purchase is not yet confirmed", "credit_label1": "Credit Card", "credit_label2": "Proceed with payment via credit card", "button_credit": "Pay with Credit Card", "payment_processing": "Payment is being processed. Please wait before retrying.", "payment_update_datetime": "The shopping cart has been updated. Please check your cart.", "payment_product_update": "Some products have been updated. Please check product details.", "purchase_failed": "Transaction failed.", "top_button": "Return to Shopping Home"}, "ec_purchase001": {"title": "Purchase Process", "bread_crumb_list": [{"text": "Shopping Cart"}, {"text": "Purchase Process"}, {"text": "Payment Method"}, {"text": "Purchase Completed"}], "content_confirm_label1": "Confirm Purchase Details", "content_confirm_label2": "※ Purchase is not yet confirmed", "pickup_date_time_label": "Pickup Date & Time", "pickup_location_label": "Pickup Location", "pickup_date_time": "You will receive an email with pickup details when ready", "payment_select_button": "Select Payment Method", "top_button": "Return to Shopping Home", "user_info_label": "Orderer Information", "last_name_placeholder": "Last Name", "first_name_placeholder": "First Name", "required_text": "Required", "name_label": "Name", "phone_no_label": "Phone Number", "sub_phone_no_label": "Example: ***********", "phone_no_placeholder": "Enter your mobile number", "quantity_label": "Quantity", "total_count_label": "Subtotal ({0} items)", "tax_label": "(Tax Included)"}, "ec_purchase002": {"title": "Purchase History", "purchase_date_time_label": "Purchase Date:", "next_button_text": "Show next {0} items", "no_result": "No purchase history available.", "return_to_shopping_top_page": "Return to Shopping Home"}, "ec_purchase003": {"title": "Purchase Completed", "your_purchase_is_complete": "Purchase Completed", "purchase_information": "※ You can check your purchase details in your purchase history", "back_to_top_page": "Return to Shopping Home"}, "ec_purchase004": {"title": "Purchase Error", "error_has_occurred": "An error occurred. Purchase was not completed.", "purchase_information": "Please try the purchase process again", "back_to_top_page": "Return to Shopping Home"}}, "transfer": {"transfer001": {"title": "Money Transfer", "transfer_amount_label": "金額", "medal_service_label": "使用する対象", "input": {"over_current_balance": "残高以内の金額を入力ください。", "balance": "残高"}, "qr_code_button": "QRコード読取"}, "transfer002": {"title": "Scan the QR code of the person you are sending to", "mess_qr_invalid": "The QR code is invalid"}, "transfer003": {"header": "確認", "transfer_amount_label": "金額", "name_label": "送る相手", "medal_service_label": "使用する対象", "balance": "残高", "pay_btn_label": "送る"}, "transfer004": {"title": "完了", "name": "さんへ", "transfer_amount": "円　送りました。", "btn": "ホームに戻る"}, "transfer005": {"title": "もらう", "info_test_line1": "もらう相手のカードをお手元に準備してください。", "info_test_line2": "QRコード読取ボタンを押下して、\n カードのQRコードを読み取ってください。", "qr_code_button": "QRコード読取"}, "transfer006": {"title": "カード読み取り", "info_test": "もらう相手のカードのQRコードを読み取ってください", "qr_code_invalid": "QRコードが不正です", "qr_code_not_allow": "カードのQRコードを読み取らせてください"}, "transfer007": {"title": "もらう", "transfer_amount_label": "金額", "name_label": "もらう相手", "medal_service_label": "使用する対象", "input_error_message": "残高以内の金額を入力ください", "next_button": "次へ", "input": {"over_current_balance": "残高以内の金額を入力ください。"}}, "transfer008": {"title": "確認", "transfer_amount_label": "金額", "name_label": "もらう相手", "medal_service_label": "使用する対象", "pay_button": "もらう"}, "transfer009": {"title": "完了", "name": "さんから", "transfer_amount": "円　もらいました。", "btn": "ホームに戻る"}}, "event001-init-get-event-ticket-list-001-INFO": "", "event001-init-get-event-ticket-list-002-ERROR": "A system error has occurred.", "event001-init-get-event-ticket-list-003-ERROR": "A system error has occurred.", "event001-init-get-event-ticket-list-004-ERROR": "A system error has occurred.", "event001-init-get-event-ticket-list-005-ERROR": "A system error has occurred.", "event001-init-get-event-ticket-list-006-ERROR": "A system error has occurred.", "event001-init-get-evevnt-ticket-query-001-ERROR": "A system error has occurred.", "event001-init-get-evevnt-ticket-query-002-ERROR": "A system error has occurred.", "event003-init-get-availablecoinid-001-ERROR": "Invalid request parameters.", "event003-init-get-availablecoinid-002-ERROR": "A system error has occurred. Please try again later.ase try again later.", "event003-init-get-availablecoinid-003-ERROR": "A system error has occurred. Please try again later.ase try again later.", "event003-init-get-event-ticket-detail-001-INFO": "", "event003-init-get-event-ticket-detail-002-ERROR": "A system error has occurred.", "event003-init-get-event-ticket-detail-003-ERROR": "A system error has occurred.", "event003-init-get-event-ticket-detail-004-ERROR": "A system error has occurred.", "event003-init-get-event-ticket-detail-005-ERROR": "A system error has occurred.", "event005-main-wallet-cash-transfer-001-ERROR": "Invalid request parameters.", "event005-main-wallet-cash-transfer-002-ERROR": "A system error has occurred. Please try again later.ase try again later.", "event005-main-wallet-cash-transfer-003-ERROR": "A system error has occurred. Please try again later.ase try again later.", "event005-main-wallet-cash-transfer-004-ERROR": "A system error has occurred. Ticket purchase failed.", "event005-main-wallet-cash-transfer-005-ERROR": "A system error has occurred. Ticket purchase failed.", "event005-main-wallet-cash-transfer-006-ERROR": "A system error has occurred. Please try again later.ase try again later.", "event005-main-wallet-cash-transfer-007-ERROR": "Only {購入可能枚数} tickets remaining.\nYou cannot purchase more than {購入可能枚数} tickets.", "event005-main-wallet-cash-transfer-008-ERROR": "You can purchase up to {購入可能上限枚数} tickets at a time.", "event005-main-wallet-cash-transfer-009-ERROR": "A system error has occurred. Please try again later.", "event005-main-wallet-cash-transfer-010-ERROR": "Insufficient balance.", "event005-main-wallet-cash-transfer-011-ERROR": "A system error has occurred. Please try again later.", "event005-main-wallet-cash-transfer-012-ERROR": "A system error has occurred. Failed to register the user event ticket list. UserEventTicketID: {0}", "event005-main-wallet-cash-transfer-013-ERROR": "A system error has occurred. Failed to update the event ticket detail list. EventTicketDetailID: {0}", "event005-main-wallet-cash-transfer-014-ERROR": "A system error has occurred. Please try again later.ase try again later.", "ticket002-init-get-event-ticket-detail-002-ERROR": "A system error has occurred.", "ticket002-init-get-event-ticket-detail-003-ERROR": "A system error has occurred.", "ticket002-init-get-event-ticket-detail-004-ERROR": "A system error has occurred.", "ticket002-init-get-event-ticket-detail-005-ERROR": "A system error has occurred.", "ticket002-init-get-event-ticket-detail-006-ERROR": "A system error has occurred.", "ticket003-main-use-event-ticket-001-INFO": "", "ticket003-main-use-event-ticket-002-ERROR": "A system error has occurred.", "ticket003-main-use-event-ticket-003-ERROR": "A system error has occurred.", "ticket003-main-use-event-ticket-004-WARNING": "This ticket has already been used.", "ticket003-main-use-event-ticket-005-ERROR": "A system error has occurred.", "ticket003-main-use-event-ticket-006-ERROR": "A system error has occurred.", "stamprally006-main-search-stamprally-002-ERROR": "Invalid request parameters.", "stamprally006-main-search-stamprally-003-ERROR": "Failed to retrieve data.", "stamprally006-main-search-stamprally-004-ERROR": "The code could not be read.", "stamprally006-main-search-stamprally-005-ERROR": "Failed to retrieve data.", "stamprally006-main-search-stamprally-006-ERROR": "Failed to retrieve data.", "stamprally006-main-search-stamprally-007-WARNING": "This user has already completed the mission.", "stamprally006-main-search-stamprally-008-ERROR": "Failed to update data.", "stamprally006-main-search-stamprally-009-WARNING": "This user has already completed the mission.", "stamprally006-main-search-stamprally-010-ERROR": "Failed to update data.", "stamprally006-main-search-stamprally-011-ERROR": "Failed to update data.", "stamprally006-main-search-stamprally-012-ERROR": "Failed to update data.", "stamprally006-main-search-stamprally-013-ERROR": "Failed to update data.", "stamprally006-main-search-stamprally-014-ERROR": "Failed to update data.", "stamprally006-main-search-stamprally-015-ERROR": "Failed to update data.", "stamprally006-main-search-stamprally-016-ERROR": "Failed to update data.", "stamprally006-main-search-stamprally-017-ERROR": "Failed to update data.", "stamprally006-main-search-stamprally-018-ERROR": "Execution of common-wallet-charge failed.", "stamprally006-main-search-stamprally-019-ERROR": "Failed to update data.", "stamprally006-main-search-stamprally-020-ERROR": "Failed to update data.", "stamprally006-main-search-stamprally-021-ERROR": "Failed to update data.", "stamprally006-main-search-stamprally-022-ERROR": "Purchase limit exceeded.", "stamprally006-main-search-stamprally-023-ERROR": "Outside the sales period.", "stamprally006-main-search-stamprally-024-ERROR": "Out of stock.", "stamprally006-main-search-stamprally-025-ERROR": "Failed to retrieve the serial code.", "stamprally006-main-search-stamprally-026-ERROR": "Failed to update data.", "stamprally006-main-search-stamprally-027-ERROR": "Failed to update data.", "stamprally006-main-search-stamprally-028-ERROR": "Execution of common-wallet-payment failed.", "stamprally006-main-search-stamprally-029-ERROR": "Failed to update data.", "stamprally006-main-search-stamprally-030-WARNING": "This user has already completed the mission.", "stamprally006-main-search-stamprally-031-ERROR": "Failed to update data.", "stamprally006-main-search-stamprally-032-WARNING": "You are too far from the mission pin location.", "stamprally006-main-search-stamprally-033-ERROR": "A system error has occurred.", "stamprally006-main-search-stamprally-034-ERROR": "A system error has occurred.", "stamprally004-init-get-stamprally-mission-list-002-ERROR": "A system error has occurred.", "stamprally004-init-get-stamprally-mission-list-003-ERROR": "A system error has occurred.", "stamprally004-init-get-stamprally-mission-list-004-ERROR": "A system error has occurred.", "stamprally004-init-get-stamprally-mission-list-005-ERROR": "A system error has occurred.", "stamprally004-init-get-stamprally-mission-list-006-ERROR": "A system error has occurred.", "stamprally004-init-get-stamprally-mission-list-007-ERROR": "A system error has occurred.", "stamprally004-init-get-stamprally-mission-list-008-ERROR": "A system error has occurred.", "stamprally004-init-get-stamprally-mission-list-009-ERROR": "A system error has occurred.", "stamprally004-init-get-stamprally-mission-list-010-ERROR": "A system error has occurred.", "stamprally005-init-update-stamprally-002-ERROR": "Invalid request parameters.", "stamprally005-init-update-stamprally-003-ERROR": "Failed to retrieve data.", "stamprally005-init-update-stamprally-004-ERROR": "Failed to retrieve data.", "stamprally005-init-update-stamprally-005-WARNING": "This user has already completed the mission.", "stamprally005-init-update-stamprally-006-ERROR": "Failed to update data.", "stamprally005-init-update-stamprally-007-WARNING": "This user has already completed the mission.", "stamprally005-init-update-stamprally-008-ERROR": "Failed to update data.", "stamprally005-init-update-stamprally-009-ERROR": "Failed to update data.", "stamprally005-init-update-stamprally-010-ERROR": "Failed to update data.", "stamprally005-init-update-stamprally-011-ERROR": "Failed to update data.", "stamprally005-init-update-stamprally-012-ERROR": "Failed to update data.", "stamprally005-init-update-stamprally-013-ERROR": "Failed to update data.", "stamprally005-init-update-stamprally-014-ERROR": "Failed to update data.", "stamprally005-init-update-stamprally-015-ERROR": "Failed to update data.", "stamprally005-init-update-stamprally-016-ERROR": "Failed to update data.", "stamprally005-init-update-stamprally-017-ERROR": "Execution of common-wallet-charge failed.", "stamprally005-init-update-stamprally-018-ERROR": "Failed to update data.", "stamprally005-init-update-stamprally-019-ERROR": "Failed to update data.", "stamprally005-init-update-stamprally-020-ERROR": "Failed to update data.", "stamprally005-init-update-stamprally-021-ERROR": "Purchase limit exceeded.", "stamprally005-init-update-stamprally-022-ERROR": "Outside the sales period.", "stamprally005-init-update-stamprally-023-ERROR": "Out of stock.", "stamprally005-init-update-stamprally-024-ERROR": "Failed to retrieve the serial code.", "stamprally005-init-update-stamprally-025-ERROR": "Failed to update data.", "stamprally005-init-update-stamprally-026-ERROR": "Failed to update data.", "stamprally005-init-update-stamprally-027-ERROR": "Execution of common-wallet-payment failed.", "stamprally005-init-update-stamprally-028-ERROR": "Failed to update data.", "stamprally005-init-update-stamprally-029-WARNING": "This user has already completed the mission.", "stamprally005-init-update-stamprally-030-ERROR": "Failed to update data.", "stamprally005-init-update-stamprally-031-ERROR": "Failed to update data.", "stamprally005-init-update-stamprally-032-WARNING": "You are too far from the mission pin location.", "stamprally005-init-update-stamprally-033-ERROR": "A system error has occurred.", "stamprally005-init-update-stamprally-034-ERROR": "A system error has occurred.", "stamprally001-init-get-stamprally-list-002-ERROR": "A system error has occurred.", "stamprally001-init-get-stamprally-list-003-ERROR": "A system error has occurred.", "stamprally001-init-get-stamprally-list-004-ERROR": "A system error has occurred.", "stamprally001-init-get-stamprally-list-005-ERROR": "A system error has occurred.", "stamprally002-init-get-stamprally-detail-002-ERROR": "A system error has occurred.", "stamprally002-init-get-stamprally-detail-003-ERROR": "A system error has occurred.", "stamprally002-init-get-stamprally-detail-004-ERROR": "A system error has occurred.", "stamprally002-init-get-stamprally-detail-005-ERROR": "A system error has occurred.", "stamprally002-init-get-stamprally-detail-006-ERROR": "A system error has occurred.", "stamprally002-init-get-stamprally-detail-007-ERROR": "A system error has occurred.", "stamprally003-init-get-stamprally-item-002-ERROR": " A system error has occurred.", "stamprally003-init-get-stamprally-item-003-ERROR": " A system error has occurred.", "stamprally003-init-get-stamprally-item-004-ERROR": " A system error has occurred.", "nft": {"nft001": {"nft_categories_list": "NFT Category List", "no_nf_ts_available_for_purchase": "No NFTs available for purchase"}, "nft002": {"list_of_nf_ts": "NFT List", "no_nf_ts_available_for_purchase": "No NFTs available for purchase", "show_more": "Show More", "not_acquired": "Not Acquired", "newest": "Newest", "sorted_by_popularity": "Most Popular", "order_of_sales": "Top Sales"}, "nft003": {"this_nft_is_now_sold_out": "This NFT is sold out", "this_nft_is_not_for_sale": "This NFT is not for sale", "this_nft_is_no_longer_available_for_sale": "This NFT is no longer available for sale", "rest": "Remaining", "point": "points", "limited": "Limited", "benefit_ticket": "Benefit Coupon", "card_id": "Card ID", "card_information": "Card Information", "sales_start": "Sales Start", "end_of_sale": "Sales End", "sale_ends_when_stock_runs_out": "Sales end when stock runs out", "purchase_with_coins": "Purchase with Coins", "purchase_with_credit_card": "Purchase with Credit Card", "back": "Back", "nf_ts_with_the_same_design_are": "NFTs with the same design are", "up_to_times": "up to", "available_for_purchase": "times available for purchase", "local_location_information_required_for_purchase": "Location information is required for purchase", "share_this_nft": "Share this NFT", "get_card": "Get Card"}, "nft005": {"point_your_camera_at_the_qr_code": "Scan QR Code", "point_the_power_mela_at_the_qr_code": "Point your camera at the QR code", "i_can_t_read_the_qr_code": "Unable to scan QR code"}, "nft006": {"screen_title": "Enter QR Code Number", "qr_code_number_label": "QR Code Number", "qr_code_number_placeholder": "# Enter QR Code Number", "cautionary_note": "※ Please check the QR code at each store."}, "nft007": {"screen_title": "Location Verification", "title_get_position_done": "Location acquired", "title_get_position_fail": "Unable to acquire location", "subtitle_get_position_fail": "Please try the following methods", "ios_header": "For iOS Users", "ios_description1": "(1) Set through browser (Safari)", "ios_description2": "(2) Set through 'Settings'", "ios_description3": "(3) If the above methods do not resolve the issue", "ios_sub_description3": "Please check this method as well.", "ios_description_support": "https://support.apple.com/ja-jp/HT207092", "android_header": "For Android Users", "android_description1": "(1) Set through 'Settings'", "android_description2": "(2) If the above methods do not resolve the issue", "android_sub_description2": "Please check this method as well.", "android_description_support": "https://support.google.com/accounts/answer/3467281?hl=ja", "next_button": "Next", "get_posotion_button": "Retry Location Acquisition", "cancel_purchase_button": "Cancel Purchase", "message_get_position_error1": "Location access is not permitted.", "message_get_position_error2": "Failed to acquire location."}, "nft008": {"screen_title": "Select Serial Number", "can_buy": "can be purchased.", "total_amount_label": "Total", "coin_balance_label": "Coin Balance", "charge_button": "Charge", "payment_amount_label": "Current Payment", "payment_balance_label": "Balance After Payment", "info_text2": "Please select a serial number", "cancel_button": "Cancel", "purchase_button": "Purchase", "placeholder_serial_code": "Select Serial Number", "cautionary_note": {"text1": "※ NFTs with the same design can be purchased up to", "text2": "times.", "text3": "Purchase is available."}, "payment_store_label": "Available for Use"}, "nft009": {"screen_title": "Payment Confirmation", "info_text": "Please check your payment details", "total_amount_label": "Total", "coin_balance_label": "Coin Balance", "payment_amount_label": "Current Payment", "payment_balance_label": "Balance After Payment", "cancel_button": "Cancel", "purchase_button": "Purchase", "serial_number_label": "Serial Number", "sale_end_info": "Sales will end when stock runs out", "unknown": "An error has occurred.", "purchase_failed": "Transaction failed.", "payment_store_label": "Available for Use"}, "nft010": {"info_text1": "Payment received.", "sub_info_text1": "Issuing NFT...", "info_text2": "You will be notified by email once issuance is complete.", "sub_info_text2": "※ Issuance may take a few minutes", "nft_list_button": "NFT List", "nft_home_button": "NFT Home", "info_text3": "The coupon is now available", "coupon_label": "Benefit Coupon", "coupon_button": "Check Coupon"}, "nft011": {"title_header": "Payment", "remaining_payment_amount": "Remaining Payment Amount", "unit": "Yen", "medal_info": "Available for Use"}, "nft_collection001": {"date_of_issue": "Date of Issue", "category": "Category", "serial_number": "Serial Number", "collection": "Collection", "express": "Display", "see_more": "See More"}, "nft_collection002": {"copyright_source": "Copyright Source", "maker": "Creator", "issuer": "Issuer", "token_id": "Token ID", "token_standard": "Token Standard", "blockchain": "Blockchain", "content_description": "Content Description", "content_image_url": "Content Image URL", "date_of_issue": "Date of Issue", "card_id": "Card ID", "serial_number": "Serial Number", "close": "Close", "see_more": "See More", "share_this_nft": "Share this NFT", "contract_address": "Contract Address", "collection_details": "Collection Details"}, "step": [{"text": "NFT Address Verification"}, {"text": "Location Verification"}, {"text": "Available for Purchase"}]}, "item": {"item001": {"no_time_specified": "No time specified", "no_event_date": "No event date specified, no time specified", "free": "Free", "unit": "Yen", "it_is_in": "is in", "item_list": "Item list", "there_are_no_items": "No items available", "no_date_specified": "No event date specified"}, "item002": {"sort": "Sort", "keyword_search": "Keyword search", "keyword": "Keyword", "new_arrival_order": "New arrivals first", "oldest": "Oldest first", "ticket": "Ticket", "not_utilized": "Not utilized", "used": "Used", "all": "All", "narrow_down": "Narrow down", "kinds": "Types", "usage_situation": "Usage situation", "location": "Location (Prefecture)", "reset": "Reset"}}, "community": {"community001": {"header": "Cha<PERSON>", "last_read": "ーーーーーーーーーLast Readーーーーーーーーー", "message_deleted": "Message has been deleted", "upload_image": "Upload image", "image": "Image", "max_upload_size": "※ Maximum upload size: 4MB", "change_image": "※ Click on the image to change it.", "select_image": "＋Select an image", "post_a_chat": "Post a chat", "enter_title": "Enter a title", "fill_in_text": "Try posting a new message!", "copied": "<PERSON>pied", "keep": "Save", "edited": "Edited", "sure_to_delete_thread": "Are you sure you want to delete the post?", "cancel": "Cancel", "delete": "Delete", "type_reply": "Enter a reply", "show_reply": "Show {0} replies", "show_more_reply": "Show more replies", "type_message": "Please enter a message", "new_label": "New post", "update_label": "Edit message"}, "community002": {"list_of_categories_and_channels": "Categories and Channels List", "create_a_new_category": "Create new category", "mention": "Mention", "create": "Create", "edit": "Edit", "editing": "Editing", "finish_editing": "Finish editing", "reacted_to_you": "{0} reacted to you", "mentioned_you": "{0} mentioned you"}, "community003": {"header": "New Registration", "category_name_label": "Category name", "channel_name_label": "Channel name", "confirm_button": "Create", "create_button": "Create", "back_to_list_button": "Back to list", "name_label_limit": "Up to 30 characters", "category_name_placeholder": "Please enter a category name.", "channel_name_placeholder": "Please enter a channel name.", "done": "Completed.", "error_permission": "You do not have permission.", "completed_msg": "Creation completed"}}, "top": {"top001": {"click_here_for_first_time": "Using for the first time here", "special_features": "Features", "search_purpose": "Search by purpose", "see_more": "See more", "event_date": "Event date:", "purpose": {"0": "Eat", "1": "Purchase", "2": "Experience", "3": "Event", "4": "Stamp Rally", "5": "Mission", "6": "Coupon", "7": "EC", "8": "NFT Market", "9": "Community"}}, "top002": {"header": "DAO Participation", "dao_participate_text": "Do you want to join the following community?", "dao_name_label_text": "Community Name", "over_view_label_text": "Overview", "content_label_text": "Activity Content", "participant_count_label_text": "Number of Participants", "count_unit_text": "people", "gt_unit_text": "GT", "issue_gt_count_label_text": "Total Issued GT", "allocation_gt_label_text": "Auto Allocated GT", "nick_name_label_text": "Nickname", "nick_name_placeholder": "Nickname", "message_label_text": "Message to display in the community", "message_placeholder": "Looking forward to working with you.", "cancel_button_text": "Cancel", "participate_button_text": "Join"}, "top003": {"header": "Profile", "update_button_text": "Change", "nick_name_label": "Nickname", "message_label": "Message to display in the community", "governance_token_label": "Owned GT", "back_button_text": "Close", "exit_button_text": "Exit Community", "exit_label": "Are you sure you want to exit the community?", "cancel_button": "Cancel", "exit_confirmed_button": "Exit", "done_label": "You have exited the community", "exit_back_button_text": "Back to Top"}, "top004": {"header": "Profile", "nick_name_label": "Nickname", "nick_name_placeholder": "Nickname", "message_label": "Message to display in the community", "message_placeholder": "Message to display in the community", "confirmation_button_text": "Go to Confirmation", "back_button_text": "Back"}, "top005": {"header": "Community Overview", "community_name": "Community Name", "overview": "Overview", "activity_content": "Activity Content", "target": "Target", "participant_number": "Number of Participants", "participant_unit": "people", "total_GT": "Total Issued GT", "automatic_GT": "Auto Allocated GT", "back_to_community_top": "Back to Community TOP", "GT": "GT", "blockchain_label": "Blockchain Usage", "and": "Yes", "without": "No"}, "top_preview": {"header": "Profile", "nick_name_label": "Nickname", "message_label": "Message to display in the community", "done_button_text": "Done", "cancel_button_text": "Cancel"}}, "voting": {"voting001": {"header": "Voting List", "narrow_down": "Filter", "search_result": "Search results {0} items", "label": {"before_voting": "Before Reception", "voting_in_progress": "In Progress", "end": "Reception Closed", "voted": "Voted", "not_voted_yet": "Not Voted"}, "button": {"next_5_items": "Show next 5 items", "create_voting_theme": "Create Voting Theme"}, "filter": {"header": "Filter", "voting_reception": "Reception Status", "before_reception": "Before Reception", "in_progress_reception": "In Progress", "end_reception": "Reception Closed", "voting_status": "Status", "not_voted": "Not Voted", "voted": "Voted", "voting_period": "Period", "start_date": "Start Date", "end_date": "End Date", "display_order": "Sort Order", "reception_start_date": "Reception Start Date", "reception_end_date": "Reception End Date", "filter_by_this_content": "Filter by this content", "new": "New", "old": "Old"}}, "voting004": {"header": "Voting Content", "header_edit": "Edit Voting Content", "voting_success_countCaution": "※ If the number of votes is not required, enter 0 \n※ By default, parameters from when the DAO was created are entered", "conversionGT_countCaution": "※ By default, parameters from DAO creation are set", "voting_format_code_placeholder": "Please select a voting format", "voting_format_code": {"regular_voting": "Regular Voting", "single_selection": "Single Selection", "multiple_selection": "Multiple Selection"}, "voting_title_placeholder": "Please enter the voting title", "voting_format_label": "Select Voting Format", "voting_name_label": "Voting Title", "voting_explanation_label": "Voting Explanation", "voting_explanation_placeholder": "Please enter the voting explanation", "choices_label": "Choices", "choice_image": "Choice {0} image", "number_of_votes_label": "Number of Votes", "number_of_votes": "{0} votes", "vote_converted_to_GT_label": "1 Vote Equivalent to GT", "voting_period_label": "Voting Period", "voting_candidate_addButton": "Add Choice", "voting_candidate_delete_button": "Delete Choice", "voting_candidate_placeholder": "Please enter a choice", "complete_button_create": "Post", "complete_button_update": "Update", "cancel_button": "Cancel", "close_button": "Close", "csv_tips": {"regular_voting": "Regular Voting", "regular_voting_content": "Vote from three choices: Agree, Disagree, or Neither.", "single_selection": "Single Selection Voting", "single_selection_content": "Vote for one choice.", "multiple_selection": "Multiple Selection Voting", "multiple_selection_content": "Vote for multiple choices."}, "up_to_characters": "{0} characters maximum", "image_MaxFileSize": "※ Maximum upload size: 4MB", "image_caution": "Click the image to change it.", "image": "Voting Image", "select_image": "+ Select Image", "voting_explanation_delete_button": "Delete Image", "voting_start_dateTime": "Start Date and Time", "voting_end_dateTime": "End Date and Time", "voting_theme_confirm_button": "Go to Confirmation", "voting_theme_update_button": "Update", "voting_theme_delete_button": "Delete", "voting_theme_back_button": "Back to List", "done": "Completed.", "expect_quantity_item": "Please create at least {0} choices", "no_more_quantity_item": "Please create no more than {0} choices", "expect_quantity_item_and_empty_content": "This is a required field. Please either delete or fill it in.", "delete_done": "Deletion completed.", "error_permission": "You do not have permission.", "delete_content_1": "Voting", "delete_content_2": "Are you sure you want to delete?"}, "voting002": {"header_detail": "Voting Content", "header_result": "Voting Results", "label": {"before_voting": "Before Reception", "voting_in_progress": "In Progress", "end": "Reception Closed", "voted": "Voted", "not_voted_yet": "Not Voted"}, "voting_not_possible": "You cannot vote due to insufficient GT", "period": "Period", "my_voting_result": "My Voting Result", "voting_result": "Voting Results, Total {0} Votes", "voting_history_csv": "CSV Download", "owned_gt": "Owned GT", "owned_gt_had": "GT owned at the time of voting", "voting_date_time": "Voting Date and Time", "not_voted": "Not Voted", "GT_unit": "GT", "vote_unit": "Votes", "required_GT_1unit": "GT required for 1 vote", "owned_vote_count": "My Voting Count", "vote_notice": "Your vote will be reflected based on your voting count.", "current_number_vote": "Current Number of Votes", "number_of_vote": "Number of Votes", "number_success_vote": "Number of Votes Established", "voting_date": "Period", "back_button": "Back to List", "vote": "Vote", "vote_again": "Re-vote", "completed": "Completed.", "voting_passed": "Voting Established", "voting_failed": "Voting Failed", "blockchain_registration_info": "Blockchain Registration Info", "contract_address": "Contract Address", "transaction_hash": "Transaction Hash", "voting_id": "Voting ID", "blockchain": "Blockchain", "voting_establish_datetime": "Voting Info Registration Date and Time", "csv_tutorial_header": "You can download the voting history for each voting result in CSV format with the following format: \nVoter Name, Vote Choice, Vote Count", "example": "Example", "tips_title": "Tips", "tips_content1": "Nickname", "tips_content2": "Choice A", "tips_content3": "100 votes", "copied": "<PERSON>pied", "right_to_vote_1": "Your voting rights are", "right_to_vote_2": "votes", "cannot_vote": "You cannot vote. Please contact the administrator.", "community_name": "Community Name", "voting_name": "Voting Title", "publication_date": "Publication Date", "poll_id": "Poll ID", "blockchain_recording_address": "Blockchain Recording Address", "preparing_for_blockchain": "Preparing for Blockchain Issuance..."}, "voting003": {"completed": "Voting completed", "back_button": "Back to Voting"}, "voting005": {"create_done": "Creation completed", "delete_done": "Deletion completed", "update_done": "Update completed", "back_voting_list": "Back to List", "back_voting_detail": "Back to Voting"}}, "dao_community": {"community_004": {"screen_title": "Category and Channel List", "category_label": "Category Name", "category_placeholder": "Please enter the category name", "up_to_characters": "Up to {0} characters", "btn_edit": "Update", "btn_delete": "Delete", "btn_back": "Back to List", "done": "Completed.", "type": "Category", "information_delete": "Are you sure you want to delete\n?", "warning_message": "※ If you delete the category, the channels will also be deleted", "cancel_btn": "Cancel", "delete_confirmed_button": "Delete", "error_permission": "You do not have permission.", "delete_label": "Deletion completed", "done_label2": "Update completed"}, "community_005": {"screen_title": "Category and Channel List", "category_label": "Category Name", "category_placeholder": "Please enter the category name", "channel_label": "Channel Name", "channel_placeholder": "Please enter the channel name", "up_to_characters": "Up to {0} characters", "btn_create": "Create", "btn_back": "Back to List", "done": "Completed.", "error_permission": "You do not have permission.", "done_label": "Creation completed"}, "community_006": {"screen_title": "Category and Channel List", "category_label": "Category Name", "channel_label": "Channel Name", "channel_placeholder": "Please enter the channel name", "up_to_characters": "Up to {0} characters", "btn_edit": "Update", "btn_delete": "Delete", "btn_back": "Back to List", "done": "Completed.", "type": "Channel", "information_delete": "Are you sure you want to delete\n?", "warning_message": "If you delete the category, the following channels will also be deleted", "cancel_btn": "Cancel", "delete_confirmed_button": "Delete", "channel_list_length_message": "At least one channel is required.", "error_permission": "You do not have permission.", "delete_label": "Deletion completed", "done_label2": "Update completed"}}, "mobile_order": {"item001": {"screen_title": "Product List", "cart_label": "<PERSON><PERSON>", "history_label": "History & Checkout", "search_placeholder": "What are you looking for?", "no_result": "No products available", "next_button_text": "Show next {0} items", "tax_label": "(Including tax)"}, "item002": {"screen_title": "Product Details", "tax_label": "(Including tax)", "cart_label": "<PERSON><PERSON>", "history_label": "History & Checkout", "quantity": "Quantity", "sold_out_label": "Sold Out", "explanation_label": "Product Description", "product_addition_error": "The order quantity exceeds the available quantity. Unable to add to cart.", "product_limit_error": "The quantity exceeds the limit. Unable to add to cart.", "add_product_success": "Product has been added to the cart.", "add_cart_button": "Add to Cart", "top_button": "Back to Product List", "item_status_message_1": "This product has been deleted", "item_status_message_2": "This product is currently unavailable for order", "item_status_message_3": "This product is not yet available for sale", "item_status_message_4": "This product is no longer available for sale"}, "confirm001": {"header": "Order Complete", "text": "Your order has been received", "item_button": "Continue Shopping", "history_button": "View History & Proceed to Checkout"}, "cart001": {"title": "<PERSON><PERSON>", "no_product": "No products in your cart", "quantity_label": "Quantity", "go_to_product_details": "Go to Product Details", "delete_item": "Delete", "tax_included": "(Including tax)", "sub_total": "Subtotal", "total_count": "({0} products)", "confirm_button": "Confirm Order", "top_button": "Back to Product List", "inconsistent_datetime": "The contents of the cart have changed. Please refresh the page.", "product_update": "Some products have updated information. Please check the product details.", "deleted_item_status_1": "This product has been deleted", "deleted_item_status_2": "This product is currently unavailable for order", "deleted_item_status_3": "This product is not yet available for sale", "deleted_item_status_4": "This product is no longer available for sale", "check_product_detail": "Please check the product details", "over_stock": "Quantity exceeds available stock"}, "history001": {"title": "Order History", "tax_label": "(Including tax)", "quantity_label": "Quantity", "sub_total_label": "Total", "btn": "Proceed to Checkout", "goods": "Goods", "error_text": "No confirmed products in the order.", "top_button": "Back to Product List"}}, "brand": {"brand001": {"community_list": "Community List", "join": "Join", "detail": "Details"}, "brand002": {"title": "Would you like to join?", "join": "Join"}}, "dao_top001": {"about_community": "About the Community", "view_profile": "View Profile", "join_chat": "Join <PERSON>", "vote_idea": "Vote on Ideas"}, "chargeqrcodepresent001-init-create-transfercode-002-ERROR": "Invalid request parameters.", "chargeqrcodepresent001-init-create-transfercode-003-ERROR": "Failed to register data.", "eventform002-main-send-answer-001-ERROR": "Invalid request parameters.", "eventform002-main-send-answer-002-ERROR": "A system error has occurred. Please try again later.ase try again later.", "eventform002-main-send-answer-003-ERROR": "A system error has occurred. Please try again later.ase try again later.", "eventform002-main-send-answer-004-ERROR": "A system error has occurred. Please try again later.ase try again later.", "eventform002-main-send-answer-005-ERROR": "A system error has occurred. Please try again later.ase try again later.", "eventform002-main-send-answer-006-ERROR": "A system error has occurred. Please try again later.ase try again later.", "eventform002-main-send-answer-007-ERROR": "A system error has occurred. Please try again later.ase try again later.", "eventform002-main-send-answer-008-ERROR": "A system error has occurred. Please try again later.ase try again later.", "eventform002-main-send-answer-009-ERROR": "A system error has occurred. Please try again later.ase try again later.", "eventform002-main-send-answer-010-ERROR": "A system error has occurred. Please try again later.ase try again later.", "eventform002-main-send-answer-011-ERROR": "A system error has occurred. Please try again later.ase try again later.", "eventform001-init-get-eventform-list-002-ERROR": "A system error has occurred.", "eventform001-init-get-eventform-list-003-ERROR": "A system error has occurred.", "eventform001-init-get-eventform-list-004-ERROR": "A system error has occurred.", "eventform001-init-get-eventform-list-005-ERROR": "A system error has occurred.", "eventform001-init-get-eventform-list-006-ERROR": "A system error has occurred.", "eventform001-init-get-eventform-list-007-ERROR": "A system error has occurred.", "eventform002-init-get-eventform-detail-001-ERROR": " Invalid request parameters.", "eventform002-init-get-eventform-detail-002-ERROR": " A system error has occurred. Please try again later.ase try again later.", "eventform002-init-get-eventform-detail-003-ERROR": "The application period is closed.", "eventform002-init-get-eventform-detail-004-ERROR": "The application process has ended.", "eventform002-init-get-eventform-detail-005-ERROR": "A system error has occurred. Please try again later.", "eventform002-init-get-eventform-detail-006-ERROR": "A system error has occurred. Please try again later.", "eventform002-init-get-eventform-detail-007-ERROR": "No data found. Please try again later.", "eventform002-init-get-eventform-detail-008-ERROR": "A system error has occurred. Please try again later.", "eventform002-init-get-eventform-detail-009-ERROR": "The response has already been accepted.", "my_number": {"error_title": "対応環境エラー", "error_content": "非対応環境のため本人確認できません。\nAndroid、iOSで本人確認を行ってください。", "my_number001": {"title": "データ利用同意", "description": "マイナンバーカードの券面事項入力補助の利用に関する同意確認", "message": "利用者登録に際し、マイナンバーカード記載の４情報について、券面事項入力補助を利用し、４情報（氏名・住所・生年月日・性別）を取得することに同意します。", "agree": "同意する", "not_agree": "同意しない"}, "my_number002": {"title": "マイナンバーカード読取\n（利用者証明用電子証明書）", "description": "画面下にある「読み取り開始」ボタンをクリックすることで、マイナポータルアプリを起動し、マイナンバーカードの利用者証明用電子証明書読み取りを行ってください。", "read_btn": "読み取り開始", "cancel_btn": "キャンセル", "back_btn": "戻る"}, "my_number003": {"title": "マイナンバーカード読取\n（利用者証明用電子証明書）", "description": "この画面が表示された場合は、ブラウザを閉じてください", "read_btn": "読み取り開始", "cancel_btn": "閉じる", "load_error": "読込エラー", "system_error": "システムエラー"}, "my_number004": {"title": "マイナンバーカード読み取り内容確認", "description": "マイナンバーカードの券面事項を読み取りました。\n以下の情報が正しいか、ご確認ください。\n\n正しくない場合、市役所窓口で券面事項の変更手続きを行ってからマイナンバーカード認証してください。\n※性別は非表示にしています。", "execute_btn": "上記内容で登録する", "cancel_btn": "戻る", "card_title": "■基本４情報（マイナンバーカード券面事項）", "full_name": "氏名", "address": "住所", "birthday": "生年月日"}, "my_number005": {"title": "マイナンバーカード読取\n（券面事項取得）", "description": "画面下にある「読み取り開始」ボタンをクリックすることで、マイナポータルアプリを起動し、マイナンバーカードの券面事項読み取りを行ってください。\n※マイナンバーは読み取りません。", "read_button": "読み取り開始", "close_button": "閉じる", "confirm_message": "画面を閉じて、処理を中断します。よろしいでしょうか？"}, "my_number006": {"title": "マイナンバーカード読取\n（券面事項取得）", "description": "この画面が表示された場合は、ブラウザを閉じてください。", "system_error": "システムエラー"}, "my_number008": {"screen_title": "マイナンバーカード読取内容確認", "message": "マイナンバーカードの券面事項を読み取りました。\n以下の情報が正しいか、ご確認ください。\n\n正しくない場合、市役所窓口で券面事項の変更手続きを行ってからマイナンバーカード認証してください。\n※性別は非表示にしています。", "card_title": "■基本４情報（マイナンバーカード券面事項）", "full_name": "氏名", "address": "住所", "birthday": "生年月日", "execute_button": "上記内容で登録する", "close_button": "閉じる", "confirm_message": "画面を閉じて、処理を中断します。よろしいでしょうか？"}, "my_number009": {"title": "マイナンバーカード認証結果", "description": "マイナンバーカードを認証しました。"}, "my_number011": {"header": "新規登録", "card_title": "■基本４情報（マイナンバーカード券面事項）", "full_name": "氏名", "address": "住所", "birthday": "生年月日", "mail_address_label": "メールアドレス", "mail_address_sub_label": "例 : <EMAIL>", "mail_address_placeholder": "メールアドレスを入力してください", "password_text": "パスワード", "password_label": "パスワードの設定", "password_sub_label": "半角英数字8文字以上で設定ください", "password_placeholder": "パスワードを入力してください", "confirm_password_label": "パスワード再入力", "confirm_password_placeholder": "再度パスワードを入力してください", "member_number_label": "会員ナンバー", "member_number_ext_label": "会員ナンバーをお持ちの方は、お持ちの番号をそのまま入力してください", "member_number_sub_label": "例 : TYK0123456789", "member_number_sub_placeholder": "会員ナンバーを入力してください", "password_must_have_letter_digit": "パスワードには英字、数字を含めてください。", "password_incorrect_message": "パスワードが誤っています。", "password_not_match": "パスワードとパスワード（確認）が異なっています。", "check_member_number_first_5_letters": "入力形式が正しくありません", "check_member_number_input": "4～13文字目は数字を入力してください", "check_member_number_length": "13文字で入力してください", "sign_up": "新規登録", "cellphone_no_info_label": "携帯電話番号", "cellphone_no_info_sub_label": "例：***********", "cellphone_no_info_placeholder": "携帯電話番号を入力してください", "cellphone_no_error": "携帯電話番号が正しくありません"}}, "my_number_test": {"error_title": "対応環境エラー", "error_content": "非対応環境のため本人確認できません。\nAndroid、iOSで本人確認を行ってください。", "my_number002": {"title": "マイナンバーカード読取\n（利用者証明用電子証明書）", "description": "画面下にある「読み取り開始」ボタンをクリックすることで、マイナポータルアプリを起動し、マイナンバーカードの利用者証明用電子証明書読み取りを行ってください。", "read_btn": "読み取り開始", "cancel_btn": "キャンセル", "back_btn": "戻る"}, "my_number003": {"title": "マイナンバーカード読取\n（券面事項取得）", "description": "画面下にある「読み取り開始」ボタンをクリックすることで、マイナポータルアプリを起動し、マイナンバーカードの券面事項読み取りを行ってください。\n※マイナンバーは読み取りません。", "read_btn": "読み取り開始", "cancel_btn": "戻る", "load_error": "読込エラー", "system_error": "システムエラー"}, "my_number004": {"title": "マイナンバーカード読み取り内容確認", "description": "マイナンバーカードの券面事項を読み取りました。\n以下の情報が正しいか、ご確認ください。\n\n正しくない場合、市役所窓口で券面事項の変更手続きを行ってからマイナンバーカード認証してください。\n※性別は非表示にしています。", "execute_btn": "上記内容で登録する", "cancel_btn": "戻る", "card_title": "■基本４情報（マイナンバーカード券面事項）", "full_name": "氏名", "address": "住所", "birthday": "生年月日"}, "my_number005": {"title": "マイナンバーカード読取\n（券面事項取得）", "description": "画面下にある「読み取り開始」ボタンをクリックすることで、マイナポータルアプリを起動し、マイナンバーカードの券面事項読み取りを行ってください。\n※マイナンバーは読み取りません。", "read_button": "読み取り開始", "close_button": "閉じる", "confirm_message": "画面を閉じて、処理を中断します。よろしいでしょうか？"}, "my_number006": {"title": "マイナンバーカード読取\n（券面事項取得）", "description": "この画面が表示された場合は、ブラウザを閉じてください。", "system_error": "システムエラー"}, "my_number008": {"screen_title": "マイナンバーカード読取内容確認", "message": "マイナンバーカードの券面事項を読み取りました。\n以下の情報が正しいか、ご確認ください。\n\n正しくない場合、市役所窓口で券面事項の変更手続きを行ってからマイナンバーカード認証してください。\n※性別は非表示にしています。", "card_title": "■基本４情報（マイナンバーカード券面事項）", "full_name": "氏名", "address": "住所", "birthday": "生年月日", "execute_button": "上記内容で登録する", "close_button": "閉じる", "confirm_message": "画面を閉じて、処理を中断します。よろしいでしょうか？", "yes": "はい", "no": "いいえ", "elasticache_information_label": "Elasticache情報", "myna_session_id_label": "セッションID", "from_screen_label": "FromScreen", "brand_ID_label": "ブランドID", "cheer_ID_label": "CheerID", "os_flag_label": "OSフラグ", "myna_mail_address_label": "メールアドレス", "myna_end_user_id_label": "エンドユーザID", "myan_validate_result_label": "検証結果", "myan_revocation_reason_code_label": "失効理由コード", "myan_revocation_reason_message_label": "失効理由メッセージ", "myan_process_result_label": "処理結果"}, "my_number009": {"title": "マイナンバーカード認証結果", "description": "マイナンバーカードを認証しました。"}, "my_number011": {"card_title": "■基本４情報（マイナンバーカード券面事項）", "full_name": "氏名", "address": "住所", "birthday": "生年月日", "mail_address_label": "メールアドレス", "mail_address_sub_label": "例 : <EMAIL>", "mail_address_placeholder": "メールアドレスを入力してください", "password_text": "パスワード", "password_label": "パスワードの設定", "password_sub_label": "半角英数字8文字以上で設定ください", "password_placeholder": "パスワードを入力してください", "confirm_password_label": "パスワード再入力", "confirm_password_placeholder": "再度パスワードを入力してください", "member_number_label": "会員ナンバー", "member_number_ext_label": "会員ナンバーをお持ちの方は、お持ちの番号をそのまま入力してください", "member_number_sub_label": "例 : TYK0123456789", "member_number_sub_placeholder": "会員ナンバーを入力してください", "password_must_have_letter_digit": "パスワードには英字、数字を含めてください。", "password_incorrect_message": "パスワードが誤っています。", "password_not_match": "パスワードとパスワード（確認）が異なっています。", "check_member_number_first_5_letters": "入力形式が正しくありません", "check_member_number_input": "4～13文字目は数字を入力してください", "check_member_number_length": "13文字で入力してください", "sign_up": "新規登録"}, "my_number000": {"screen_title": "メニュー・新規からマイナ連携", "menu_button": "メニューからマイナ連携", "mail_address_label": "メールアドレス", "mail_address_sub_label": "例 : <EMAIL>", "mail_address_placeholder": "メールアドレスを入力してください", "entry_button": "新規登録からマイナ連携", "ec_button": "ElastiCache情報表示"}}, "chargeqrcoderead001-main-execute-charge-001-INFO": "", "chargeqrcoderead001-main-execute-charge-002-ERROR": "Invalid request parameters.", "chargeqrcoderead001-main-execute-charge-003-ERROR": "Failed to retrieve data.", "chargeqrcoderead001-main-execute-charge-004-ERROR": "Failed to retrieve data.", "chargeqrcoderead001-main-execute-charge-005-ERROR": "A system error has occurred.", "chargeqrcoderead001-main-execute-charge-006-ERROR": "Failed to charge points.", "chargeqrcoderead001-main-execute-charge-007-ERROR": "Failed to retrieve data.", "chargeqrcoderead001-main-execute-charge-008-ERROR": "Failed to update data.", "chargeqrcoderead001-main-execute-charge-009-ERROR": "Failed to register data.", "chargeqrcoderead001-main-execute-charge-010-ERROR": "Failed to update data.", "chargeqrcoderead001-main-execute-charge-011-ERROR": "A system error has occurred.", "chargeqrcoderead002-main-execute-charge-001-INFO": "", "chargeqrcoderead002-main-execute-charge-002-ERROR": "Invalid request parameters.", "chargeqrcoderead002-main-execute-charge-003-ERROR": "Failed to retrieve data.", "chargeqrcoderead002-main-execute-charge-004-ERROR": "Failed to retrieve data.", "chargeqrcoderead002-main-execute-charge-005-ERROR": "A system error has occurred.", "chargeqrcoderead002-main-execute-charge-006-ERROR": "Failed to charge points.", "chargeqrcoderead002-main-execute-charge-007-ERROR": "Failed to retrieve data.", "chargeqrcoderead002-main-execute-charge-008-ERROR": "Failed to update data.", "chargeqrcoderead002-main-execute-charge-009-ERROR": "Failed to register data.", "chargeqrcoderead002-main-execute-charge-010-ERROR": "Failed to update data.", "chargeqrcoderead002-main-execute-charge-011-ERROR": "A system error has occurred.", "nft001-init-get-nft-category-list-001-ERROR": "A system error has occurred. Please try again later.ase try again later.", "nft001-init-get-nft-category-list-002-ERROR": "", "nft002-main-send-nft-user-like-001-ERROR": "A system error has occurred. Please try again later.ase try again later.", "nft002-main-send-nft-user-like-002-ERROR": "", "nft002-main-send-nft-user-like-003-ERROR": "", "nft002-main-send-nft-user-like-004-ERROR": "", "nft002-main-send-nft-user-like-005-ERROR": "", "nft002-main-send-nft-user-like-006-ERROR": "", "nft002-init-get-nft-purchase-list-001-ERROR": "A system error has occurred. Please try again later.ase try again later.", "nft002-init-get-nft-purchase-list-002-ERROR": "", "nft002-init-get-nft-purchase-list-003-ERROR": "", "nft002-init-get-nft-purchase-list-004-ERROR": "", "nft002-init-get-nft-purchase-list-005-ERROR": "", "nft002-init-get-nft-category-detail-001-ERROR": "A system error has occurred. Please try again later.ase try again later.", "nft002-init-get-nft-category-detail-002-ERROR": "", "nft002-init-get-nft-category-detail-003-ERROR": "", "nft003-init-get-nft-detail-001-ERROR": "A system error has occurred. Please try again later.ase try again later.", "nft003-init-get-nft-detail-002-ERROR": "", "nft003-init-get-nft-detail-003-ERROR": "", "nft003-init-get-nft-detail-004-ERROR": "", "nft003-init-get-nft-detail-005-ERROR": "", "nft003-init-get-nft-detail-006-ERROR": "", "nft003-init-get-nft-detail-007-ERROR": "", "nft003-init-get-nft-detail-008-ERROR": "", "nft003-main-send-nft-user-like-001-ERROR": "A system error has occurred. Please try again later.ase try again later.", "nft003-main-send-nft-user-like-002-ERROR": "", "nft003-main-send-nft-user-like-003-ERROR": "", "nft003-main-send-nft-user-like-004-ERROR": "", "nft003-main-send-nft-user-like-005-ERROR": "", "nft003-main-send-nft-user-like-006-ERROR": "", "nft005-init-check-nft-qr-001-ERROR": "A system error has occurred. Please try again later.ase try again later.", "nft005-init-check-nft-qr-002-ERROR": "-", "nft005-init-check-nft-qr-003-ERROR": "-", "nft005-init-check-nft-qr-004-ERROR": "The QR code number is invalid.", "nft005-init-check-nft-qr-005-ERROR": "-", "nft005-init-check-nft-qr-006-ERROR": "Outside the sales period.", "nft008-init-get-nft-001-ERROR": "A system error has occurred. Please try again later.", "nft008-init-get-nft-002-ERROR": "Parameter Exception.", "nft008-init-get-nft-003-ERROR": "Get NFTContentList Exception.", "nft008-init-get-nft-004-ERROR": "The corresponding NFT content information was not found.", "nft008-init-get-nft-005-ERROR": "Get StoreList Exception.", "nft008-init-get-nft-006-ERROR": "A system error has occurred. Please try again later.ase try again later.", "nft008-init-get-coin-001-ERROR": "A system error has occurred. Please try again later.ase try again later.", "nft008-init-get-coin-002-ERROR": "A system error has occurred. Please try again later.ase try again later.", "nft008-init-get-coin-003-ERROR": "A system error has occurred. Please try again later.ase try again later.", "nft008-init-get-coin-004-ERROR": "A system error has occurred. Please try again later.ase try again later.", "nft008-init-get-coin-005-ERROR": "A system error has occurred. Please try again later.ase try again later.", "nft008-init-get-coin-006-ERROR": "A system error has occurred. Please try again later.ase try again later.", "nft008-init-get-coin-007-ERROR": "A system error has occurred. Please try again later.ase try again later.", "nft008-main-check-nft-serial-001-ERROR": "A system error has occurred. Please try again later.ase try again later.", "nft008-main-check-nft-serial-002-ERROR": "Parameter Exception.", "nft008-main-check-nft-serial-003-ERROR": "Get NFTContentList Exception.", "nft008-main-check-nft-serial-004-ERROR": "Get NFTContentList Exception.", "nft008-main-check-nft-serial-005-ERROR": "Purchase limit reached. You cannot make a purchase.", "nft008-main-check-nft-serial-006-ERROR": "Outside the sales period.", "nft008-main-check-nft-serial-007-ERROR": "This item is sold out.", "nft008-main-check-nft-serial-008-ERROR": "The specified serial number has already been purchased.", "nft008-main-check-nft-serial-009-ERROR": "The specified serial number is currently unavailable for purchase.", "nft008-main-check-nft-serial-010-ERROR": "Set NFTContentList Exception.", "nft006-init-check-nft-qr-001-ERROR": "A system error has occurred. Please try again later.", "nft006-init-check-nft-qr-002-ERROR": "Parameter Exception.", "nft006-init-check-nft-qr-003-ERROR": "Get StoreList Exception.", "nft006-init-check-nft-qr-004-ERROR": "The QR code number is invalid.", "nft006-init-check-nft-qr-005-ERROR": "Get NFTContentList Exception.", "nft006-init-check-nft-qr-006-ERROR": "Outside the sales period.。", "nftcollection001-init-get-nft-user-list-001-ERROR": "Invalid request parameters.", "nftcollection001-init-get-nft-user-list-002-ERROR": "A system error has occurred. Please try again later.ase try again later.", "nftcollection001-init-get-nft-user-list-003-ERROR": "A system error has occurred. Please try again later.ase try again later.", "nftcollection001-init-get-nft-user-list-004-ERROR": "A system error has occurred. Please try again later.ase try again later.", "nftcollection001-init-get-nft-user-list-005-ERROR": "A system error has occurred. Please try again later.ase try again later.", "nftcollection001-init-get-nft-user-list-006-ERROR": "A system error has occurred. Please try again later.ase try again later.", "nftcollection002-init-get-nft-user-001-ERROR": "Invalid request parameters.", "nftcollection002-init-get-nft-user-002-ERROR": "A system error has occurred. Please try again later.ase try again later.", "nftcollection002-init-get-nft-user-003-ERROR": "You do not own this serial number NFT or it was not found.", "nftcollection002-init-get-nft-user-004-ERROR": "You do not own this serial number NFT or it was not found.", "nftcollection002-init-get-nft-user-005-ERROR": "A system error has occurred. Please try again later.ase try again later.", "nftcollection002-init-get-nft-user-006-ERROR": "A system error has occurred. Please try again later.ase try again later.", "nftcollection002-init-get-nft-user-007-ERROR": "A system error has occurred. Please try again later.ase try again later.", "nftcollection002-init-get-nft-user-008-ERROR": "A system error has occurred. Please try again later.ase try again later.", "mission001-init-get-mission-list-002-ERROR": "A system error has occurred.", "mission001-init-get-mission-list-003-ERROR": "A system error has occurred.", "mission001-init-get-mission-list-004-ERROR": "A system error has occurred.", "mission001-init-get-mission-list-005-ERROR": "A system error has occurred.", "mission001-init-get-mission-list-006-ERROR": "A system error has occurred.", "mission001-init-get-mission-list-007-ERROR": "A system error has occurred.", "mission002-init-get-mission-detail-002-ERROR": "A system error has occurred.", "mission002-init-get-mission-detail-003-ERROR": "A system error has occurred.", "mission002-init-get-mission-detail-004-ERROR": "A system error has occurred.", "mission002-init-get-mission-detail-005-ERROR": "A system error has occurred.", "mission002-init-get-mission-detail-006-ERROR": "A system error has occurred.", "mission002-init-get-mission-detail-007-ERROR": "A system error has occurred.", "mission002-init-get-mission-detail-008-ERROR": "A system error has occurred.", "mission002-init-get-mission-detail-009-ERROR": "A system error has occurred.", "mission002-init-get-mission-detail-010-ERROR": "A system error has occurred.", "mission002-init-get-mission-detail-011-ERROR": "A system error has occurred.", "mission002-init-get-mission-detail-012-ERROR": "A system error has occurred.", "mission002-init-get-mission-detail-013-ERROR": "A system error has occurred.", "mission002-init-get-mission-detail-014-ERROR": "A system error has occurred.", "mission002-init-get-mission-detail-015-ERROR": "A system error has occurred.", "mission002-init-get-mission-detail-016-ERROR": "A system error has occurred.", "mission002-init-get-mission-detail-017-ERROR": "A system error has occurred.", "mission002-init-get-mission-detail-018-ERROR": "A system error has occurred.", "mission002-init-get-mission-detail-019-ERROR": "A system error has occurred.", "mission003-init-get-mission-detail-002-ERROR": "A system error has occurred.", "mission003-init-get-mission-detail-003-ERROR": "A system error has occurred.", "mission003-init-get-mission-detail-004-ERROR": "A system error has occurred.", "mission003-init-get-mission-detail-005-ERROR": "A system error has occurred.", "mission003-init-get-mission-detail-006-ERROR": "A system error has occurred.", "mission003-init-get-mission-detail-007-ERROR": "A system error has occurred.", "mission003-main-update-mission-002-ERROR": "A system error has occurred. Please try again later.ase try again later.", "mission003-main-update-mission-003-ERROR": "A system error has occurred. Please try again later.ase try again later.", "mission003-main-update-mission-004-ERROR": "A system error has occurred. Please try again later.ase try again later.", "mission003-main-update-mission-005-ERROR": "A system error has occurred.", "mission003-main-update-mission-006-ERROR": "A system error has occurred.", "mission003-main-update-mission-007-ERROR": "A system error has occurred.", "mission003-main-update-mission-008-WARNING": "This user has already completed the mission.", "mission003-main-update-mission-009-ERROR": "A system error has occurred.", "mission003-main-update-mission-010-ERROR": "A system error has occurred.", "mission003-main-update-mission-011-WARNING": "This user has already completed the mission.", "mission003-main-update-mission-012-ERROR": "A system error has occurred.", "mission003-main-update-mission-013-ERROR": "A system error has occurred.", "mission003-main-update-mission-014-ERROR": "A system error has occurred.", "mission003-main-update-mission-015-ERROR": "A system error has occurred.", "mission003-main-update-mission-016-WARNING": "This user has already completed the mission.", "mission003-main-update-mission-017-ERROR": "A system error has occurred.", "mission003-main-update-mission-018-ERROR": "A system error has occurred.", "mission003-main-update-mission-019-ERROR": "A system error has occurred.", "mission003-main-update-mission-020-ERROR": "A system error has occurred.", "mission003-main-update-mission-021-ERROR": "A system error has occurred.", "商品一覧画面_商品一覧取得-002-ERROR": "Failed to retrieve information. Please refresh the browser screen.", "商品一覧画面_商品一覧取得-003-ERROR": "Failed to retrieve information. Please try again.", "商品一覧画面_商品一覧取得-004-ERROR": "EcItem Exception.", "共通機能_買い物かご情報存在確認-002-ERROR": "Failed to retrieve information. Please try again.", "共通機能_買い物かご情報存在確認-003-ERROR": "EcCart Exception.", "共通機能_URL取得-002-ERROR": "Failed to retrieve information. Please refresh the browser screen.", "共通機能_URL取得-003-ERROR": "Failed to retrieve information. Please try again.", "共通機能_URL取得-004-ERROR": "Bucket Name Error.", "nft009-main-execute-payment-001-ERROR": "Invalid request parameters.", "nft009-main-execute-payment-002-ERROR": "A system error has occurred. Please try again later.ase try again later.", "nft009-main-execute-payment-003-ERROR": "A system error has occurred. Please try again later.ase try again later.", "nft009-main-execute-payment-004-ERROR": "A system error has occurred. Please try again later.ase try again later.", "nft009-main-execute-payment-005-ERROR": "A system error has occurred. Please try again later.ase try again later.", "nft009-main-execute-payment-006-ERROR": "A system error has occurred. Please try again later.ase try again later.", "nft009-main-execute-payment-007-ERROR": "A system error has occurred. Please try again later.ase try again later.", "nft009-main-execute-payment-008-ERROR": "A system error has occurred. Please try again later.ase try again later.", "nft007-init-get-nft-001-ERROR": "A system error has occurred. Please try again later.ase try again later.", "nft007-init-get-nft-002-ERROR": "Parameter Exception.", "nft007-init-get-nft-003-ERROR": "Get NFTContentList Exception.", "nft007-init-get-nft-004-ERROR": "The corresponding NFT content information was not found.", "nft007-init-get-nft-005-ERROR": "Get StoreList Exception.", "nft007-init-get-nft-006-ERROR": "Purchase is not available at the current location.", "login001-main-get-line-token-002-ERROR": "Invalid request parameters.", "login001-main-get-line-token-003-ERROR": "Failed to retrieve data.", "login001-main-get-line-token-004-INFO": "User information was not found.", "login001-main-get-line-token-005-ERROR": "Failed to retrieve data.", "login001-main-get-line-token-006-INFO": "Authentication failed.", "login001-main-get-line-token-007-ERROR": "Failed to update data.", "共通機能_買い物かご情報更新-002-ERROR": "Failed to retrieve information. Please refresh the browser screen.", "共通機能_買い物かご情報更新-003-ERROR": "Failed to retrieve information. Please try again.", "共通機能_買い物かご情報更新-004-ERROR": "Failed to set information. Please try again.", "共通機能_買い物かご情報更新-005-ERROR": "EcItem Exception.", "共通機能_買い物かご情報更新-006-ERROR": "EcCart Exception.", "共通機能_買い物かご情報更新-007-ERROR": "EcCartDetail Exception.", "共通機能_決済期限切れ情報更新-001-INFO": "", "共通機能_決済期限切れ情報更新-002-ERROR": "Failed to retrieve information. Please try again.", "共通機能_決済期限切れ情報更新-003-ERROR": "Failed to set information. Please try again.", "共通機能_決済期限切れ情報更新-004-ERROR": "", "共通機能_決済期限切れ情報更新-005-ERROR": "", "共通機能_決済期限切れ情報更新-006-ERROR": "", "買い物かご画面_買い物かご情報取得-001-INFO": "", "買い物かご画面_買い物かご情報取得-002-ERROR": "Failed to retrieve information. Please try again.", "買い物かご画面_買い物かご情報取得-003-ERROR": "", "買い物かご画面_買い物かご情報取得-004-ERROR": "", "買い物かご画面_買い物かご情報取得-005-ERROR": "", "買い物かご画面_買い物かご情報取得-006-ERROR": "", "買い物かご画面_商品削除-001-INFO": "", "買い物かご画面_商品削除-002-ERROR": "Failed to retrieve information. Please refresh the browser screen.", "買い物かご画面_商品削除-003-ERROR": "Failed to retrieve information. Please try again.", "買い物かご画面_商品削除-004-ERROR": "Failed to delete information. Please try again.", "買い物かご画面_商品削除-005-ERROR": "", "買い物かご画面_商品削除-006-ERROR": "", "支払い画面_支払い前確認-002-ERROR": "Failed to retrieve information. Please refresh the browser screen.", "支払い画面_支払い前確認-003-ERROR": "Failed to retrieve information. Please try again.", "支払い画面_更新日時取得-002-ERROR": "Failed to retrieve information. Please refresh the browser screen.", "支払い画面_更新日時取得-003-ERROR": "Failed to retrieve information. Please try again.", "購入手続き画面_購入手続き情報更新-002-ERROR": "Failed to retrieve information. Please refresh the browser screen.", "購入手続き画面_購入手続き情報更新-003-ERROR": "Failed to set information. Please try again.", "購入手続き画面_購入手続き情報取得-002-ERROR": "Failed to retrieve information. Please refresh the browser screen.", "購入手続き画面_購入手続き情報取得-003-ERROR": "Failed to retrieve information. Please try again.", "購入履歴画面_購入履歴一覧取得-002-ERROR": "Failed to retrieve information. Please refresh the browser screen.", "購入履歴画面_購入履歴一覧取得-003-ERROR": "Failed to retrieve information. Please try again.", "購入履歴画面_購入履歴一覧取得-004-ERROR": "EcSalesDetail Exception.", "商品詳細画面_商品情報取得-002-ERROR": "Failed to retrieve information. Please refresh the browser screen.", "商品詳細画面_商品情報取得-003-ERROR": "Failed to retrieve information. Please try again.", "common-wallet-get-payment-002-ERROR": "A system error has occurred.", "common-wallet-get-payment-003-ERROR": "A system error has occurred.", "common-wallet-get-payment-004-ERROR": "A system error has occurred.", "common-wallet-get-payment-005-ERROR": "A system error has occurred.", "common-wallet-get-payment-006-ERROR": "A system error has occurred.", "common-wallet-get-payment-007-ERROR": "A system error has occurred.", "投票テーマ一覧画面_投票テーマ一覧取得-002-ERROR": "Failed to retrieve information. Please refresh the browser screen.", "投票テーマ一覧画面_投票テーマ一覧取得-003-ERROR": "Failed to retrieve information. Please try again.", "投票テーマ一覧画面_投票テーマ一覧取得-004-ERROR": "DaoVotingTheme Exception.", "投票テーマ一覧画面_投票テーマ一覧取得-005-ERROR": "DaoVotingCheer Exception.", "投票テーマ一覧画面_投票テーマ一覧取得-006-ERROR": "DaoID Varidation Error.", "共通機能_機能権限取得-002-ERROR": "Failed to retrieve information. Please refresh the browser screen.", "共通機能_機能権限取得-003-ERROR": "Failed to retrieve information. Please try again.", "共通機能_機能権限取得-004-ERROR": "DaoParticipation Exception.", "共通機能_機能権限取得-005-ERROR": "DaoFunctionAuthority Exception.", "共通機能_機能権限取得-006-ERROR": "DaoID Varidation Error.", "投票テーマ管理画面_投票テーマ初期情報取得-002-ERROR": "Failed to retrieve information. Please refresh the browser.", "投票テーマ管理画面_投票テーマ初期情報取得-003-ERROR": "Failed to retrieve information. Please try again.", "投票テーマ管理画面_投票テーマ初期情報取得-004-ERROR": "DaoVotingFormat Exception.", "投票テーマ管理画面_投票テーマ初期情報取得-005-ERROR": "DaoID Varidation Error.", "投票テーマ管理画面_投票テーマ初期情報取得-006-ERROR": "DaoManagement Exception.", "投票テーマ管理画面_投票テーマ情報取得-002-ERROR": "Failed to retrieve information. Please refresh the browser screen.", "投票テーマ管理画面_投票テーマ情報取得-003-ERROR": "Failed to retrieve information. Please try again.", "投票テーマ管理画面_投票テーマ情報取得-004-ERROR": "DaoVotingTheme Exception.", "投票テーマ管理画面_投票テーマ情報取得-005-ERROR": "DaoVotingCandidate Exception.", "投票テーマ管理画面_投票テーマ情報取得-006-ERROR": "DaoID Varidation Error.", "投票テーマ管理画面_投票テーマ作成-002-ERROR": "Failed to retrieve information. Please refresh the browser screen.", "投票テーマ管理画面_投票テーマ作成-003-ERROR": "Failed to retrieve information. Please try again.", "投票テーマ管理画面_投票テーマ作成-004-ERROR": "Failed to set information. Please try again.", "投票テーマ管理画面_投票テーマ作成-005-ERROR": "You do not have permission.", "投票テーマ管理画面_投票テーマ作成-006-ERROR": "DaoVotingTheme Exception.", "投票テーマ管理画面_投票テーマ作成-007-ERROR": "DaoVotingCandidate Exception.", "投票テーマ管理画面_投票テーマ作成-008-ERROR": "BrandSEQList Exception.", "投票テーマ管理画面_投票テーマ作成-009-ERROR": "FunctionAuthority Exception.", "投票テーマ管理画面_投票テーマ作成-010-ERROR": "DaoID Varidation Error.", "投票テーマ管理画面_投票テーマ更新-002-ERROR": "Failed to retrieve information. Please refresh the browser screen.", "投票テーマ管理画面_投票テーマ更新-003-ERROR": "Failed to retrieve information. Please try again.", "投票テーマ管理画面_投票テーマ更新-004-ERROR": "Failed to delete information. Please try again.", "投票テーマ管理画面_投票テーマ更新-005-ERROR": "Failed to set information. Please try again.", "投票テーマ管理画面_投票テーマ更新-006-ERROR": "You do not have permission.", "投票テーマ管理画面_投票テーマ更新-007-ERROR": "DaoVotingTheme Exception.", "投票テーマ管理画面_投票テーマ更新-008-ERROR": "DaoVotingCandidate Exception.", "投票テーマ管理画面_投票テーマ更新-009-ERROR": "FunctionAuthority Exception.", "投票テーマ管理画面_投票テーマ更新-010-ERROR": "DaoID Varidation Error.", "投票テーマ管理画面_投票テーマ削除-002-ERROR": "Failed to retrieve information. Please refresh the browser screen.", "投票テーマ管理画面_投票テーマ削除-003-ERROR": "Failed to retrieve information. Please try again.", "投票テーマ管理画面_投票テーマ削除-004-ERROR": "Failed to delete information. Please try again.", "投票テーマ管理画面_投票テーマ削除-005-ERROR": "You do not have permission.", "投票画面_投票情報取得-002-ERROR": "Failed to retrieve information. Please refresh the browser screen.", "投票画面_投票情報取得-003-ERROR": "Failed to retrieve information. Please try again.", "投票画面_投票情報取得-004-ERROR": "DaoVotingTheme Exception.", "投票画面_投票情報取得-005-ERROR": "DaoVotingCheer Exception.", "投票画面_投票情報取得-006-ERROR": "DaoVotingCandidate Exception.", "投票画面_投票情報取得-007-ERROR": "DaoVotingFormat Exception.", "投票画面_投票情報取得-008-ERROR": "DaoID Varidation Error.", "投票画面_投票情報取得-009-ERROR": "DaoVotingCheerDestination Exception.", "投票画面_投票情報取得-010-ERROR": "CheerList Exception.", "投票画面_投票情報取得-011-ERROR": "BlockchainMedalConfig Exception.", "投票画面_投票権取得-002-ERROR": "Failed to retrieve information. Please refresh the browser screen.", "投票画面_投票権取得-003-ERROR": "Failed to retrieve information. Please try again.", "投票画面_投票権取得-004-ERROR": "Failed to set information. Please try again.", "投票画面_投票権取得-005-ERROR": "DaoParticipant Exception.", "投票画面_投票権取得-006-ERROR": "DaoID Varidation Error.", "投票画面_投票権取得-007-ERROR": "DaoVotingTheme Exception.", "投票画面_投票権取得-008-ERROR": "API Execution Exception.", "投票画面_投票権取得-009-ERROR": "CheerList Exception.", "投票画面_投票権取得-012-ERROR": "Failed to retrieve information. Please refresh the browser screen.", "投票画面_投票状況情報取得-002-ERROR": "Failed to retrieve information. Please refresh the browser screen.", "投票画面_投票状況情報取得-003-ERROR": "Failed to retrieve information. Please try again.", "投票画面_投票状況情報取得-004-ERROR": "DaoVotingCandidate Exception.", "投票画面_投票状況情報取得-005-ERROR": "DaoVotingTheme Exception.", "投票画面_投票状況情報取得-006-ERROR": "DaoID Varidation Error.", "投票画面_投票者情報取得-002-ERROR": "Failed to retrieve information. Please refresh the browser screen.", "投票画面_投票者情報取得-003-ERROR": "Failed to retrieve information. Please try again.", "投票画面_投票者情報取得-004-ERROR": "DaoVotingCheerHistory Exception.", "投票画面_投票者情報取得-005-ERROR": "DaoVotingCheerDestination Exception.", "投票画面_投票者情報取得-006-ERROR": "DaoVotingTheme Exception.", "投票画面_投票者情報取得-007-ERROR": "DaoID Varidation Error.", "投票画面_投票者情報取得-008-ERROR": "CheerList Exception.", "投票画面_投票-002-ERROR": "Failed to retrieve information. Please refresh the browser screen.", "投票画面_投票-003-ERROR": "Failed to retrieve information. Please try again.", "投票画面_投票-004-ERROR": "Failed to delete information. Please try again.", "投票画面_投票-005-ERROR": "Failed to set information. Please try again.", "投票画面_投票-006-ERROR": "DaoVotingTheme Exception.", "投票画面_投票-007-ERROR": "DaoVotingCandidate Exception.", "投票画面_投票-008-ERROR": "DaoVotingCheer Exception.", "投票画面_投票-009-ERROR": "DaoParticipant Exception.", "投票画面_投票-010-ERROR": "CheerList Exception.", "投票画面_投票-011-ERROR": "DaoVotingCheerDestination Exception.", "投票画面_投票-012-ERROR": "BrandSEQList Exception.", "投票画面_投票-013-ERROR": "DaoManagement Exception.", "投票画面_投票-014-ERROR": "DaoVotingCheerHistory Exception.", "投票画面_投票-015-ERROR": "DaoID Varidation Error.", "投票画面_投票-016-ERROR": "API Execution Exception.", "投票画面_投票-017-ERROR": "Voting reception has ended.", "投票画面_投票-020-ERROR": "Failed to retrieve information. Please try again.", "チャネル作成・更新画面_チャネル作成-002-ERROR": "Failed to retrieve information. Please refresh the browser screen.", "チャネル作成・更新画面_チャネル作成-003-ERROR": "Failed to retrieve information. Please try again.", "チャネル作成・更新画面_チャネル作成-004-ERROR": "Failed to set information. Please try again.", "チャネル作成・更新画面_チャネル作成-005-ERROR": "You do not have permission.", "チャネル作成・更新画面_チャネル作成-006-ERROR": "DaoChannel Exception.", "チャネル作成・更新画面_チャネル作成-007-ERROR": "BrandSEQList Exception.", "チャネル作成・更新画面_チャネル作成-008-ERROR": "DaoID Varidation Error.", "チャネル作成・更新画面_チャネル作成-009-ERROR": "FunctionAuthority Exception.", "カテゴリー作成・更新画面_カテゴリー削除-002-ERROR": "Failed to retrieve information. Please refresh the browser screen.", "カテゴリー作成・更新画面_カテゴリー削除-003-ERROR": "Failed to retrieve information. Please try again.", "カテゴリー作成・更新画面_カテゴリー削除-004-ERROR": "Failed to delete information. Please try again.", "カテゴリー作成・更新画面_カテゴリー削除-005-ERROR": "Failed to set information. Please try again.", "カテゴリー作成・更新画面_カテゴリー削除-006-ERROR": "You do not have permission.", "カテゴリー作成・更新画面_カテゴリー削除-007-ERROR": "DaoCategory Exception.", "カテゴリー作成・更新画面_カテゴリー削除-008-ERROR": "DaoChannel Exception.", "カテゴリー作成・更新画面_カテゴリー削除-009-ERROR": "DaoID Varidation Error.", "カテゴリー作成・更新画面_カテゴリー削除-010-ERROR": "FunctionAuthority Exception.", "カテゴリー作成・更新画面_カテゴリー更新-002-ERROR": "Failed to retrieve information. Please refresh the browser screen.", "カテゴリー作成・更新画面_カテゴリー更新-003-ERROR": "Failed to retrieve information. Please try again.", "カテゴリー作成・更新画面_カテゴリー更新-004-ERROR": "Failed to set information. Please try again.", "カテゴリー作成・更新画面_カテゴリー更新-005-ERROR": "You do not have permission.", "カテゴリー作成・更新画面_カテゴリー更新-006-ERROR": "DaoCategory Exception.", "カテゴリー作成・更新画面_カテゴリー更新-007-ERROR": "DaoID Varidation Error.", "カテゴリー作成・更新画面_カテゴリー更新-008-ERROR": "FunctionAuthority Exception.", "チャネル作成・更新画面_チャネル削除-002-ERROR": "Failed to retrieve information. Please refresh the browser screen.", "チャネル作成・更新画面_チャネル削除-003-ERROR": "Failed to retrieve information. Please try again.", "チャネル作成・更新画面_チャネル削除-004-ERROR": "Failed to retrieve information. Please try again.", "チャネル作成・更新画面_チャネル削除-005-ERROR": "You do not have permission.", "チャネル作成・更新画面_チャネル削除-006-ERROR": "You do not have permission.", "チャネル作成・更新画面_チャネル削除-007-ERROR": "DaoCategory Exception.", "チャネル作成・更新画面_チャネル削除-008-ERROR": "DaoID Varidation Error.", "チャネル作成・更新画面_チャネル削除-009-ERROR": "FunctionAuthority Exception.", "チャネル作成・更新画面_チャネル更新-002-ERROR": "Failed to retrieve information. Please refresh the browser screen.", "チャネル作成・更新画面_チャネル更新-003-ERROR": "Failed to retrieve information. Please try again.", "チャネル作成・更新画面_チャネル更新-004-ERROR": "Failed to set information. Please try again.", "チャネル作成・更新画面_チャネル更新-005-ERROR": "You do not have permission.", "チャネル作成・更新画面_チャネル更新-006-ERROR": "DaoCategory Exception.", "チャネル作成・更新画面_チャネル更新-007-ERROR": "DaoID Varidation Error.", "チャネル作成・更新画面_チャネル更新-008-ERROR": "FunctionAuthority Exception.", "カテゴリー作成・更新画面_カテゴリー作成-002-ERROR": "Failed to retrieve information. Please refresh the browser screen.", "カテゴリー作成・更新画面_カテゴリー作成-003-ERROR": "Failed to retrieve information. Please try again.", "カテゴリー作成・更新画面_カテゴリー作成-004-ERROR": "Failed to set information. Please try again.", "カテゴリー作成・更新画面_カテゴリー作成-005-ERROR": "You do not have permission.", "カテゴリー作成・更新画面_カテゴリー作成-006-ERROR": "DaoCategory Exception.", "カテゴリー作成・更新画面_カテゴリー作成-007-ERROR": "BrandSEQList Exception.", "カテゴリー作成・更新画面_カテゴリー作成-008-ERROR": "DaoID Varidation Error.", "カテゴリー作成・更新画面_カテゴリー作成-009-ERROR": "FunctionAuthority Exception.", "カテゴリー作成・更新画面_カテゴリー作成-010-ERROR": "DaoChannel Exception.", "カテゴリー・チャネル表示画面_一覧取得-002-ERROR": "Failed to retrieve information. Please refresh the browser screen.", "カテゴリー・チャネル表示画面_一覧取得-003-ERROR": "Failed to retrieve information. Please try again.", "カテゴリー・チャネル表示画面_一覧取得-004-ERROR": "DaoCategory Exception.", "カテゴリー・チャネル表示画面_一覧取得-005-ERROR": "DaoChannel Exception.", "カテゴリー・チャネル表示画面_一覧取得-006-ERROR": "DaoChatMention Exception.", "カテゴリー・チャネル表示画面_一覧取得-007-ERROR": "DaoID Varidation Error.", "カテゴリー・チャネル表示画面_一覧取得-008-ERROR": "CheerList Exception.", "カテゴリー・チャネル表示画面_メンション一覧取得-002-ERROR": "Failed to retrieve information. Please refresh the browser screen.", "カテゴリー・チャネル表示画面_メンション一覧取得-003-ERROR": "Failed to retrieve information. Please try again.", "カテゴリー・チャネル表示画面_メンション一覧取得-004-ERROR": "DaoCategory Exception.", "カテゴリー・チャネル表示画面_メンション一覧取得-005-ERROR": "DaoChannel Exception.", "カテゴリー・チャネル表示画面_メンション一覧取得-006-ERROR": "DaoChatMention Exception.", "カテゴリー・チャネル表示画面_メンション一覧取得-007-ERROR": "DaoID Varidation Error.", "カテゴリー・チャネル表示画面_メンション一覧取得-008-ERROR": "CheerList Exception.", "カテゴリー・チャネル表示画面_メンション一覧取得-009-ERROR": "DaoChatReactionCheer Exception.", "カテゴリー・チャネル表示画面_未読件数取得-002-ERROR": "Failed to retrieve information. Please refresh the browser screen.", "カテゴリー・チャネル表示画面_未読件数取得-003-ERROR": "Failed to retrieve information. Please try again.", "カテゴリー・チャネル表示画面_未読件数取得-004-ERROR": "DaoCategory Exception.", "カテゴリー・チャネル表示画面_未読件数取得-005-ERROR": "DaoChannel Exception.", "カテゴリー・チャネル表示画面_未読件数取得-006-ERROR": "DaoID Varidation Error.", "カテゴリー・チャネル表示画面_未読件数取得-007-ERROR": "DaoChatRead Exception.", "カテゴリー・チャネル表示画面_未読件数取得-008-ERROR": "DaoChat Exception.", "カテゴリー・チャネル表示画面_未読件数取得-009-ERROR": "DaoChatMention Exception.", "カテゴリー・チャネル表示画面_未読件数取得-010-ERROR": "DaoChatReactionCheer Exception.", "コミュニティチャット画面_スレッド情報取得-002-ERROR": "Failed to retrieve information. Please refresh the browser screen.", "コミュニティチャット画面_スレッド情報取得-003-ERROR": "Failed to retrieve information. Please try again.", "コミュニティチャット画面_スレッド情報取得-004-ERROR": "DaoCategory Exception.", "コミュニティチャット画面_スレッド情報取得-005-ERROR": "DaoChannel Exception.", "コミュニティチャット画面_スレッド情報取得-006-ERROR": "DaoThread Exception.", "コミュニティチャット画面_スレッド情報取得-007-ERROR": "DaoID Varidation Error.", "コミュニティチャット画面_スレッド情報取得-008-ERROR": "CheerList Exception.", "コミュニティチャット画面_スレッド情報取得-009-ERROR": "DaoChat Exception.", "コミュニティチャット画面_スレッド情報取得-010-ERROR": "DaoChatReactionCheer Exception.", "コミュニティチャット画面_スレッド情報取得-011-ERROR": "DaoParticipant Exception.", "コミュニティチャット画面_チャット情報取得-002-ERROR": "Failed to retrieve information. Please refresh the browser screen.", "コミュニティチャット画面_チャット情報取得-003-ERROR": "Failed to retrieve information. Please try again.", "コミュニティチャット画面_チャット情報取得-004-ERROR": "DaoID Varidation Error.", "コミュニティチャット画面_チャット情報取得-005-ERROR": "CheerList Exception.", "コミュニティチャット画面_チャット情報取得-006-ERROR": "DaoChat Exception.", "コミュニティチャット画面_チャット情報取得-007-ERROR": "DaoChatReactionCheer Exception.", "コミュニティチャット画面_チャット情報取得-008-ERROR": "DaoParticipant Exception.", "DAOトップ画面_話題の投稿情報取得-002-ERROR": "Failed to retrieve information. Please refresh the browser screen.", "DAOトップ画面_話題の投稿情報取得-003-ERROR": "Failed to retrieve information. Please try again.", "DAOトップ画面_話題の投稿情報取得-004-ERROR": "DaoID Varidation Error.", "DAOトップ画面_話題の投稿情報取得-005-ERROR": "DaoChannel Exception.", "DAOトップ画面_話題の投稿情報取得-006-ERROR": "DaoChat Exception.", "DAOトップ画面_話題の投稿情報取得-007-ERROR": "DaoParticipant Exception.", "DAOトップ画面_話題の投稿情報取得-008-ERROR": "DaoManagement Exception.", "DAOトップ画面_話題の投稿情報取得-009-ERROR": "DaoChatReactionCheer Exception.", "DAOトップ画面_参加状況取得-002-ERROR": "Failed to retrieve information. Please refresh the browser screen.", "DAOトップ画面_参加状況取得-003-ERROR": "Failed to retrieve information. Please try again.", "DAOトップ画面_参加状況取得-004-ERROR": "DaoParticipant Exception.", "DAOトップ画面_参加状況取得-005-ERROR": "DaoID Varidation Error.", "コミュニティチャット画面_リアクション画像取得-002-ERROR": "Failed to retrieve information. Please refresh the browser screen.", "コミュニティチャット画面_リアクション画像取得-003-ERROR": "Failed to retrieve information. Please try again.", "コミュニティチャット画面_リアクション画像取得-004-ERROR": "DaoReactionImage Exception.", "コミュニティチャット画面_リアクション画像取得-005-ERROR": "DaoID Varidation Error.", "home001-init-get-event-ticket-list-002-ERROR": "A system error has occurred.", "home001-init-get-event-ticket-list-003-ERROR": "A system error has occurred.", "DAO参加画面_参加者情報取得-001-INFO": "Request parameter: [${0}]", "DAO参加画面_参加者情報取得-002-ERROR": "Failed to retrieve information. Please refresh the browser screen.", "DAO参加画面_参加者情報取得-003-ERROR": "Failed to retrieve information. Please try again.", "DAO参加画面_参加者情報取得-004-ERROR": "DaoID Varidation Error.", "DAO参加画面_参加者情報取得-005-ERROR": "DaoManagement Exception.", "DAO参加画面_参加者情報取得-006-ERROR": "CheerList Exception.", "DAO参加画面_参加者登録-001-INFO": "Request parameter: [${0}]", "DAO参加画面_参加者登録-002-ERROR": "Failed to retrieve information. Please refresh the browser screen.", "DAO参加画面_参加者登録-003-ERROR": "Failed to retrieve information. Please try again.", "DAO参加画面_参加者登録-004-ERROR": "Failed to set information. Please try again.", "DAO参加画面_参加者登録-005-ERROR": "CheerList Exception.", "DAO参加画面_参加者登録-006-ERROR": "DaoParticipant Exception.", "DAO参加画面_参加者登録-007-ERROR": "DaoID Varidation Error.", "DAO参加画面_参加者登録-008-ERROR": "DaoManagement Exception.", "DAO参加画面_参加者登録-009-ERROR": "API Execution Exception.", "DAO参加画面_参加者登録-013-ERROR": "Not available.", "DAO参加画面_参加者登録-015-ERROR": "Failed to retrieve information. Please refresh the browser screen.", "DAOプロフィール表示画面_プロフィール情報取得-001-INFO": "Request parameter: [${0}]", "DAOプロフィール表示画面_プロフィール情報取得-002-ERROR": "Failed to retrieve information. Please refresh the browser screen.", "DAOプロフィール表示画面_プロフィール情報取得-003-ERROR": "Failed to retrieve information. Please try again.", "DAOプロフィール表示画面_プロフィール情報取得-004-ERROR": "You do not have permission.", "DAOプロフィール表示画面_プロフィール情報取得-005-ERROR": "DaoParticipant Exception.", "DAOプロフィール表示画面_プロフィール情報取得-006-ERROR": "DaoID Varidation Error.", "DAOプロフィール表示画面_プロフィール情報取得-007-ERROR": "API Execution Exception.", "DAOプロフィール表示画面_プロフィール情報取得-008-ERROR": "DaoManagement Exception.", "DAOプロフィール表示画面_プロフィール情報取得-009-ERROR": "FunctionAuthority Exception.", "DAOプロフィール更新画面_プロフィール更新-001-INFO": "Request parameter: [${0}]", "DAOプロフィール更新画面_プロフィール更新-002-ERROR": "Failed to retrieve information. Please refresh the browser screen.", "DAOプロフィール更新画面_プロフィール更新-003-ERROR": "Failed to retrieve information. Please try again.", "DAOプロフィール更新画面_プロフィール更新-004-ERROR": "Failed to set information. Please try again.", "DAOプロフィール更新画面_プロフィール更新-005-ERROR": "DaoParticipant Exception.", "DAOプロフィール更新画面_プロフィール更新-006-ERROR": "DaoID Varidation Error.", "DAOプロフィール表示画面_退出-002-ERROR": "Failed to retrieve information. Please refresh the browser screen.", "共通機能_URL取得-005-ERROR": "DaoVotingTheme Exception.", "共通機能_URL取得-006-ERROR": "DaoVotingCandidate Exception.", "共通機能_URL取得-007-ERROR": "BrandSEQList Exception.", "共通機能_URL取得-008-ERROR": "DaoID Varidation Error.", "コミュニティチャット画面_既読チャット情報取得-002-ERROR": "Failed to retrieve information. Please refresh the browser screen.", "コミュニティチャット画面_既読チャット情報取得-003-ERROR": "Failed to retrieve information. Please try again.", "コミュニティチャット画面_既読チャット情報取得-004-ERROR": "DaoID Varidation Error.", "コミュニティチャット画面_既読チャット情報取得-005-ERROR": "DaoChatRead Exception.", "コミュニティチャット画面_既読チャット更新-002-ERROR": "Failed to retrieve information. Please refresh the browser screen.", "コミュニティチャット画面_既読チャット更新-003-ERROR": "Failed to retrieve information. Please try again.", "コミュニティチャット画面_既読チャット更新-004-ERROR": "Failed to set information. Please try again.", "コミュニティチャット画面_既読チャット更新-005-ERROR": "DaoID Varidation Error.", "コミュニティチャット画面_既読チャット更新-006-ERROR": "DaoChatRead Exception.", "コミュニティチャット画面_チャット投稿登録-002-ERROR": "Failed to retrieve information. Please refresh the browser screen.", "コミュニティチャット画面_チャット投稿登録-003-ERROR": "Failed to retrieve information. Please try again.", "コミュニティチャット画面_チャット投稿登録-004-ERROR": "Failed to set information. Please try again.", "コミュニティチャット画面_チャット投稿登録-005-ERROR": "DaoThread Exception.", "コミュニティチャット画面_チャット投稿登録-006-ERROR": "DaoChat Exception.", "コミュニティチャット画面_チャット投稿登録-007-ERROR": "DaoID Varidation Error.", "コミュニティチャット画面_チャット投稿登録-008-ERROR": "BrandSEQList Exception.", "コミュニティチャット画面_チャット投稿登録-009-ERROR": "DaoChatMention Exception.", "コミュニティチャット画面_チャット投稿登録-010-ERROR": "CheerList Exception.", "コミュニティチャット画面_チャット投稿登録-011-ERROR": "DaoParticipant Exception.", "コミュニティチャット画面_チャット投稿更新-002-ERROR": "Failed to retrieve information. Please refresh the browser screen.", "コミュニティチャット画面_チャット投稿更新-003-ERROR": "Failed to retrieve information. Please try again.", "コミュニティチャット画面_チャット投稿更新-004-ERROR": "Failed to set information. Please try again.", "コミュニティチャット画面_チャット投稿更新-005-ERROR": "Failed to delete information. Please try again.", "コミュニティチャット画面_チャット投稿更新-006-ERROR": "You do not have permission.", "コミュニティチャット画面_チャット投稿更新-007-ERROR": "DaoThread Exception.", "コミュニティチャット画面_チャット投稿更新-008-ERROR": "DaoChat Exception.", "コミュニティチャット画面_チャット投稿更新-009-ERROR": "DaoID Varidation Error.", "コミュニティチャット画面_チャット投稿更新-010-ERROR": "BrandSEQList Exception.", "コミュニティチャット画面_チャット投稿更新-011-ERROR": "DaoChatMention Exception.", "コミュニティチャット画面_チャット投稿更新-012-ERROR": "FunctionAuthority Exception.", "コミュニティチャット画面_チャット投稿更新-013-ERROR": "CheerList Exception.", "コミュニティチャット画面_チャット投稿更新-014-ERROR": "DaoParticipant Exception.", "コミュニティチャット画面_チャット投稿削除-002-ERROR": "Failed to retrieve information. Please refresh the browser screen.", "コミュニティチャット画面_チャット投稿削除-003-ERROR": "Failed to retrieve information. Please try again.", "コミュニティチャット画面_チャット投稿削除-004-ERROR": "Failed to set information. Please try again.", "コミュニティチャット画面_チャット投稿削除-005-ERROR": "You do not have permission.", "コミュニティチャット画面_チャット投稿削除-006-ERROR": "DaoChat Exception.", "コミュニティチャット画面_チャット投稿削除-007-ERROR": "DaoID Varidation Error.", "コミュニティチャット画面_チャット投稿削除-008-ERROR": "BrandSEQList Exception.", "コミュニティチャット画面_チャット投稿削除-009-ERROR": "FunctionAuthority Exception.", "コミュニティチャット画面_チャット投稿削除-010-ERROR": "DaoThread Exception.", "コミュニティチャット画面_リアクション投稿削除-002-ERROR": "Failed to retrieve information. Please refresh the browser screen.", "コミュニティチャット画面_リアクション投稿削除-003-ERROR": "Failed to retrieve information. Please try again.", "コミュニティチャット画面_リアクション投稿削除-004-ERROR": "Failed to delete information. Please try again.", "コミュニティチャット画面_リアクション投稿削除-005-ERROR": "DaoChatReactionCheer Exception.", "コミュニティチャット画面_リアクション投稿削除-006-ERROR": "DaoID Varidation Error.", "コミュニティチャット画面_リアクション投稿削除-007-ERROR": "DaoChatReactionSummary Exception.", "コミュニティチャット画面_リアクション投稿登録-002-ERROR": "Failed to retrieve information. Please refresh the browser screen.", "コミュニティチャット画面_リアクション投稿登録-003-ERROR": "Failed to retrieve information. Please refresh the browser screen.", "コミュニティチャット画面_リアクション投稿登録-004-ERROR": "Failed to set information. Please try again.", "コミュニティチャット画面_リアクション投稿登録-005-ERROR": "DaoChatReactionCheer Exception.", "コミュニティチャット画面_リアクション投稿登録-006-ERROR": "DaoID Varidation Error.", "コミュニティチャット画面_リアクション投稿登録-007-ERROR": "BrandSEQList Exception.", "コミュニティチャット画面_リアクション投稿登録-008-ERROR": "DaoChat Exception.", "コミュニティチャット画面_リアクション投稿登録-009-ERROR": "DaoChatReactionSummary Exception.", "コミュニティチャット画面_参加者情報取得-002-ERROR": "Failed to retrieve information. Please refresh the browser screen.", "コミュニティチャット画面_参加者情報取得-003-ERROR": "Failed to retrieve information. Please try again.", "コミュニティチャット画面_参加者情報取得-004-ERROR": "DaoID Varidation Error.", "コミュニティチャット画面_参加者情報取得-005-ERROR": "DaoParticipant Exception.", "共通機能_買い物かご更新日時更新-001-INFO": "", "共通機能_買い物かご更新日時更新-003-ERROR": "Failed to retrieve information. Please try again.", "共通機能_買い物かご更新日時更新-004-ERROR": "Failed to set information. Please try again.", "共通機能_買い物かご更新日時更新-005-ERROR": "", "共通機能_買い物かご更新日時更新-006-ERROR": "", "item001-init-get-itemlist-001-INFO": "", "item001-init-get-itemlist-002-ERROR": "A system error has occurred.", "item001-init-get-itemlist-003-ERROR": "A system error has occurred.", "item001-init-get-itemlist-004-ERROR": "A system error has occurred.", "item001-init-get-itemlist-005-ERROR": "A system error has occurred.", "item001-init-get-itemlist-006-ERROR": "A system error has occurred.", "item002-init-get-itemquery-001-ERROR": "A system error has occurred.", "item002-init-get-itemquery-002-ERROR": "A system error has occurred.", "モバイルオーダー商品一覧画面_商品一覧取得-002-ERROR": "Failed to retrieve information. Please refresh the browser screen.", "モバイルオーダー商品一覧画面_商品一覧取得-003-ERROR": "Failed to retrieve information. Please try again.", "モバイルオーダー商品一覧画面_商品一覧取得-004-ERROR": "MobileOrderItem Exception.", "商品一覧画面_加盟店名取得-002-ERROR": "Failed to retrieve information. Please refresh the browser screen.", "商品一覧画面_加盟店名取得-003-ERROR": "Failed to retrieve information. Please try again.", "商品一覧画面_加盟店名取得-004-ERROR": "StoreList Exception.", "共通機能_カート情報存在確認-002-ERROR": "Failed to retrieve information. Please refresh the browser screen.", "共通機能_カート情報存在確認-003-ERROR": "Failed to retrieve information. Please try again.", "共通機能_カート情報存在確認-004-ERROR": "MobileOrderCartDetail Exception.", "モバイルオーダー共通機能_URL取得-002-ERROR": "Failed to retrieve information. Please refresh the browser screen.", "モバイルオーダー共通機能_URL取得-003-ERROR": "Failed to retrieve information. Please try again.", "モバイルオーダー共通機能_URL取得-004-ERROR": "Bucket Name Error.", "モバイルオーダー商品詳細画面_商品情報取得-002-ERROR": "Failed to retrieve information. Please refresh the browser screen.", "モバイルオーダー商品詳細画面_商品情報取得-003-ERROR": "Failed to retrieve information. Please try again.", "モバイルオーダー商品詳細画面_商品情報取得-004-ERROR": "MobileOrderItem Exception.", "モバイルオーダー商品詳細画面_商品情報取得-005-ERROR": "MobileOrderCart Exception.", "モバイルオーダー商品詳細画面_商品情報取得-006-ERROR": "MobileOrderCartDetail Exception.", "共通機能_カート情報更新-002-ERROR": "Failed to retrieve information. Please refresh the browser screen.", "共通機能_カート情報更新-003-ERROR": "Failed to retrieve information. Please try again.", "共通機能_カート情報更新-004-ERROR": "Failed to set information. Please try again.", "共通機能_カート情報更新-005-ERROR": "MobileOrderItem Exception.", "共通機能_カート情報更新-006-ERROR": "MobileOrderCart Exception.", "共通機能_カート情報更新-007-ERROR": "MobileOrderCartDetail Exception.", "共通機能_カート情報更新-008-ERROR": "BrandSEQList Exception.", "共通機能_カート情報更新-009-ERROR": "TransactWrite Exception.", "commonheader001-init-get-news-unread-flag-002-ERROR": "A system error has occurred.", "commonheader001-init-get-news-unread-flag-003-ERROR": "A system error has occurred.", "commonheader001-init-get-news-unread-flag-004-ERROR": "A system error has occurred.", "commonheader001-init-get-news-unread-flag-005-ERROR": "A system error has occurred.", "カート画面_商品削除-002-ERROR": "Failed to retrieve information. Please refresh the browser screen.", "カート画面_商品削除-003-ERROR": "Failed to retrieve information. Please try again.", "カート画面_商品削除-004-ERROR": "Failed to delete information. Please try again.", "カート画面_商品削除-005-ERROR": "", "カート画面_商品削除-006-ERROR": "", "カート画面_カート情報取得-002-ERROR": "Failed to retrieve information. Please refresh the browser screen.", "カート画面_カート情報取得-003-ERROR": "Failed to retrieve information. Please try again.", "カート画面_カート情報取得-004-ERROR": "", "カート画面_カート情報取得-005-ERROR": "", "カート画面_カート情報取得-006-ERROR": "", "カート画面_注文確定前確認-002-ERROR": "Failed to retrieve information. Please refresh the browser screen.", "カート画面_注文確定前確認-003-ERROR": "Failed to retrieve information. Please try again.", "カート画面_注文確定前確認-004-ERROR": "", "カート画面_注文確定前確認-005-ERROR": "", "カート画面_注文確定前確認-006-ERROR": "", "カート画面_注文確定-002-ERROR": "Failed to retrieve information. Please refresh the browser screen.", "カート画面_注文確定-003-ERROR": "Failed to retrieve information. Please try again.", "カート画面_注文確定-004-ERROR": "Failed to set information. Please try again.", "カート画面_注文確定-005-ERROR": "", "カート画面_注文確定-006-ERROR": "", "カート画面_注文確定-007-ERROR": "", "カート画面_注文確定-008-ERROR": "", "カート画面_注文確定-009-ERROR": "The cart contents have been changed.\nPlease refresh the page.", "カート画面_注文確定-010-ERROR": "Some items are out of stock.\nPlease check the product information.", "top001-init-get-info-002-ERROR": "Invalid request parameters.", "top001-init-get-info-003-ERROR": "A system error has occurred. Please try again later.ase try again later.", "top001-init-get-info-004-ERROR": "A system error has occurred. Please try again later.ase try again later.", "top001-init-get-info-005-ERROR": "A system error has occurred. Please try again later.ase try again later.", "top001-init-get-storelist-002-ERROR": "A system error has occurred.", "top001-init-get-storelist-003-ERROR": "A system error has occurred.", "top001-init-get-storelist-004-ERROR": "A system error has occurred.", "top001-init-get-eventlist-002-ERROR": "A system error has occurred.", "top001-init-get-eventlist-003-ERROR": "A system error has occurred.", "top001-init-get-eventlist-004-ERROR": "A system error has occurred.", "top001-init-get-stamprallylist-002-ERROR": "A system error has occurred.", "top001-init-get-stamprallylist-003-ERROR": "A system error has occurred.", "top001-init-get-stamprallylist-004-ERROR": "A system error has occurred.", "top001-init-get-missionlist-002-ERROR": "A system error has occurred.", "top001-init-get-missionlist-003-ERROR": "A system error has occurred.", "top001-init-get-missionlist-004-ERROR": "A system error has occurred.", "top001-init-get-ecitemlist-002-ERROR": "A system error has occurred.", "top001-init-get-ecitemlist-003-ERROR": "A system error has occurred.", "top001-init-get-ecitemlist-004-ERROR": "A system error has occurred.", "top001-init-get-ecitemlist-005-ERROR": "A system error has occurred.", "top001-init-get-nftcontentlist-002-ERROR": "A system error has occurred.", "top001-init-get-nftcontentlist-003-ERROR": "A system error has occurred.", "top001-init-get-nftcontentlist-004-ERROR": "A system error has occurred.", "top001-init-get-daolist-002-ERROR": "A system error has occurred.", "top001-init-get-daolist-003-ERROR": "A system error has occurred.", "top001-init-get-daolist-004-ERROR": "A system error has occurred.", "brand001-init-get-brand-list-002-ERROR": "A system error has occurred.", "brand001-init-get-brand-list-003-ERROR": "A system error has occurred.", "brand001-init-get-brand-list-004-ERROR": "A system error has occurred.", "brand001-init-get-brand-list-005-ERROR": "A system error has occurred.", "brand001-init-get-brand-list-006-ERROR": "A system error has occurred.", "brand001-init-get-brand-list-007-ERROR": "A system error has occurred.", "brand002-main-update-brand-002-ERROR": "A system error has occurred.", "brand002-main-update-brand-003-ERROR": "A system error has occurred.", "brand002-main-update-brand-004-WARNING": "A system error has occurred.", "brand002-main-update-brand-005-ERROR": "A system error has occurred.", "wallet001-init-get-limited-store-list-002-ERROR": "A system error has occurred.", "wallet001-init-get-limited-store-list-003-ERROR": "A system error has occurred.", "wallet001-init-get-limited-store-list-004-ERROR": "A system error has occurred.", "wallet001-init-get-limited-store-list-005-ERROR": "A system error has occurred.", "wallet001-init-get-limited-store-list-006-ERROR": "A system error has occurred.", "wallet001-init-get-limited-store-list-007-ERROR": "A system error has occurred.", "wallet001-init-get-limited-store-list-008-ERROR": "A system error has occurred.", "wallet001-init-get-limited-store-list-009-ERROR": "A system error has occurred.", "wallet001-init-get-banner-list-002-ERROR": "A system error has occurred.", "wallet001-init-get-coin-001-ERROR": "A system error has occurred. Please try again later.ase try again later.", "wallet001-init-get-coin-002-ERROR": "A system error has occurred. Please try again later.ase try again later.", "wallet001-init-get-coin-003-ERROR": "A system error has occurred. Please try again later.ase try again later.", "wallet001-init-get-coin-004-ERROR": "A system error has occurred. Please try again later.ase try again later.", "wallet001-init-get-coin-005-ERROR": "A system error has occurred. Please try again later.ase try again later.", "wallet001-init-get-coin-006-ERROR": "A system error has occurred. Please try again later.ase try again later.", "wallet001-init-get-coin-007-ERROR": "A system error has occurred. Please try again later.ase try again later.", "wallet001-init-get-coupon-list-002-ERROR": "A system error has occurred.", "wallet001-init-get-coupon-list-003-ERROR": "A system error has occurred.", "wallet001-init-get-coupon-list-004-ERROR": "A system error has occurred.", "wallet001-init-get-coupon-list-005-ERROR": "A system error has occurred.", "wallet001-init-get-coupon-list-006-ERROR": "A system error has occurred.", "wallet001-init-get-news-unread-latest-001-ERROR": "Invalid request parameters.", "wallet001-init-get-news-unread-latest-002-ERROR": "A system error has occurred. Please try again later.ase try again later.", "wallet001-init-get-news-unread-latest-003-ERROR": "A system error has occurred. Please try again later.ase try again later.", "wallet001-init-get-news-unread-latest-004-ERROR": "A system error has occurred. Please try again later.ase try again later.", "wallet001-init-get-news-unread-latest-005-ERROR": "A system error has occurred. Please try again later.ase try again later.", "wallet001-init-get-userinfo-001-ERROR": "Invalid request parameters.", "wallet001-init-get-userinfo-002-ERROR": "A system error has occurred. Please try again later.ase try again later.", "wallet001-init-get-userinfo-003-ERROR": "A system error has occurred. Please try again later.ase try again later.", "wallet001-main-get-payableflag-001-INFO": "Request parameter", "wallet001-main-get-payableflag-002-ERROR": "Invalid request parameters.", "wallet001-main-get-payableflag-003-ERROR": "A system error has occurred. Please try again later.ase try again later.", "wallet001-main-get-chargeableflag-001-INFO": "Request parameter", "wallet001-main-get-chargeableflag-002-ERROR": "Invalid request parameters.", "wallet001-main-get-chargeableflag-003-ERROR": "A system error has occurred. Please try again later.ase try again later.", "DAOトップ画面_DAO一覧取得-002-ERROR": "Failed to retrieve information. Please refresh the browser screen.", "DAOトップ画面_DAO一覧取得-003-ERROR": "Get DaoManagement Exception.", "DAO概要画面_DAO情報取得-002-ERROR": "Failed to retrieve information. Please refresh the browser screen.", "DAO概要画面_DAO情報取得-003-ERROR": "Failed to retrieve information. Please try again.", "DAO概要画面_DAO情報取得-004-ERROR": "DaoID Varidation Error.", "DAO概要画面_DAO情報取得-005-ERROR": "DaoManagement Exception.", "DAO概要画面_DAO情報取得-006-ERROR": "CheerList Exception.", "共通機能_参加状況取得-002-ERROR": "Failed to retrieve information. Please refresh the browser screen..", "投票テーマ管理画面_投票テーマ削除 -002-ERROR": "Failed to retrieve information. Please refresh the browser screen.", "投票テーマ管理画面_投票テーマ削除 -003-ERROR": "Failed to retrieve information. Please try again.", "投票テーマ管理画面_投票テーマ削除 -004-ERROR": "Failed to delete information. Please try again.", "投票テーマ管理画面_投票テーマ削除 -005-ERROR": "You do not have permission.", "common-init-get-userinfo-001-ERROR": "Invalid request parameters.", "common-init-get-userinfo-002-ERROR": "A system error has occurred. Please try again later.ase try again later.", "common-init-get-userinfo-003-ERROR": "A system error has occurred. Please try again later.ase try again later.", "deleteaccount002-main-delete-account-001-ERROR": "Invalid request parameters.", "deleteaccount002-main-delete-account-002-ERROR": "A system error has occurred. Please try again later.ase try again later.", "deleteaccount002-main-delete-account-003-ERROR": "A system error has occurred. Please try again later.ase try again later.", "deleteaccount002-main-delete-account-004-ERROR": "A system error has occurred. Please try again later.ase try again later.", "deleteaccount002-main-delete-account-005-ERROR": "A system error has occurred. Please try again later.ase try again later.", "deleteaccount002-main-delete-account-006-ERROR": "A system error has occurred. Please try again later.ase try again later.", "deleteaccount002-main-delete-account-007-ERROR": "A system error has occurred. Please try again later.ase try again later.", "deleteaccount002-main-delete-account-008-ERROR": "A system error has occurred. Please try again later.ase try again later.", "qrcode_present": {"qrcode_present001": {"title": "QR Code Display"}, "qrcode_present002": {"title": "Payment Completed", "payment_completed_info": "Payment has been completed.", "btn": "Return to Home", "got_point": "Acquired", "of": "of"}, "qrcode_present003": {"title": "Charge Completed", "charge_completed_info": "Charge has been completed.", "btn": "Return to Home"}}, "common-get-medalinfo-001-INFO": "リクエストパラメータ：[${0}]", "common-get-medalinfo-002-ERROR": "リクエストパラメータが異常です。", "common-get-medalinfo-003-ERROR": "Get BUCKET_URI Exception.", "common-get-medalinfo-004-ERROR": "システムエラーが発生しました。しばらく時間をおいてから再度お試しください。", "common-get-medalinfo-005-ERROR": "Get MedalServiceList Exception.", "common-get-medalinfo-006-ERROR": "システムエラーが発生しました。しばらく時間をおいてから再度お試しください。", "common-get-medalinfo-007-ERROR": "Get balance Exception.", "common-get-medalinfo-008-ERROR": "システムエラーが発生しました。しばらく時間をおいてから再度お試しください。", "common-get-medalinfo-009-ERROR": "Get BlockchainMedalConfig Exception.", "common-get-medalinfo-010-ERROR": "システムエラーが発生しました。しばらく時間をおいてから再度お試しください。", "common-transfer-main-execute-payment-001-INFO": "リクエストパラメータ：[${0}]", "common-transfer-main-execute-payment-002-ERROR": "リクエストパラメータが不正です。", "common-transfer-main-execute-payment-003-ERROR": "Call Lambda Error", "common-transfer-main-get-userinfo-001-INFO": "リクエストパラメータ：[${0}]", "common-transfer-main-get-userinfo-002-ERROR": "リクエストパラメータが異常です", "common-transfer-main-get-userinfo-003-ERROR": "Get QRAuthCodeList Exception.", "common-transfer-main-get-userinfo-004-ERROR": "システムエラーが発生しました\nしばらく時間をおいてから再度お試しください123", "common-transfer-main-get-userinfo-005-ERROR": "QRAuthCode NG.", "common-transfer-main-get-userinfo-006-INFO": "QRコードが不正です\n再度お試しください", "common-transfer-main-get-userinfo-007-ERROR": "QRAuthCode Timeout.", "common-transfer-main-get-userinfo-008-INFO": "QRコードの有効期限が過ぎています\n再度お試しください", "common-transfer-main-get-userinfo-009-ERROR": "Get CheerList Exception.", "common-transfer-main-get-userinfo-010-ERROR": "システムエラーが発生しました", "common-transfer-main-get-userinfo-011-ERROR": "Get CheerList Exception.", "common-transfer-main-get-userinfo-012-ERROR": "QRコードが不正です\n再度お試しください", "common-transfer-main-get-orderid-001-INFO": "リクエストパラメータ：[${0}]", "common-transfer-main-get-orderid-002-ERROR": "リクエストパラメータが異常です。", "common-transfer-main-get-orderid-003-ERROR": "Get GetOrderID Exception.", "transfer007-init-get-coin-001-ERROR": "システムエラーが発生しました。時間をおいて再度実行してください。", "transfer007-init-get-coin-002-ERROR": "システムエラーが発生しました。時間をおいて再度実行してください。", "transfer007-init-get-coin-003-ERROR": "システムエラーが発生しました。時間をおいて再度実行してください。", "transfer007-init-get-coin-004-ERROR": "システムエラーが発生しました。時間をおいて再度実行してください。", "transfer007-init-get-coin-005-ERROR": "システムエラーが発生しました。時間をおいて再度実行してください。", "transfer007-init-get-coin-006-ERROR": "システムエラーが発生しました。時間をおいて再度実行してください。", "transfer007-init-get-coin-007-ERROR": "システムエラーが発生しました。時間をおいて再度実行してください。", "transfer007-init-get-coin-008-INFO": "リクエストパラメータ：[${0}]", "transfer007-init-get-coin-009-ERROR": "リクエストパラメータが異常です", "transfer007-init-get-coin-010-ERROR": "システムエラーが発生しました。時間をおいて再度実行してください。", "common-mynumber-init-get-mynasetting-noauth-001-INFO": "リクエストパラメータ：[${0}]", "common-mynumber-init-get-mynasetting-noauth-002-ERROR": "リクエストパラメータが異常です", "common-mynumber-init-get-mynasetting-noauth-003-ERROR": "Get BrandList Exception.", "common-mynumber-init-get-mynasetting-noauth-004-ERROR": "システムエラーが発生しましたしばらく時間をおいてから再度お試しください", "mynumber002-init-get-usercert-stub-001-INFO": "リクエストパラメータ：[${0}]", "mynumber002-init-get-usercert-stub-002-ERROR": "リクエストパラメータが異常です。", "mynumber002-init-get-usercert-stub-003-ERROR": "Get BrandList Exception.", "mynumber002-init-get-usercert-stub-004-ERROR": "システムエラーが発生しました。", "mynumber002-init-get-usercert-stub-005-INFO": "APIの実行が完了しました。", "mynumber002-init-get-usercert-stub-006-ERROR": "Get BUCKET_URI Exception.", "mynumber002-init-get-usercert-stub-007-ERROR": "システムエラーが発生しました。しばらく時間をおいてから再度お試しください。", "mynumber002-init-get-usercert-stub-008-ERROR": "Set SessionInfo Exception.", "mynumber002-init-get-usercert-stub-009-ERROR": "セッション情報の登録に失敗しました。", "mynumber002-init-get-usercert-001-INFO": "リクエストパラメータ：[${0}]", "mynumber002-init-get-usercert-002-ERROR": "リクエストパラメータが異常です。", "mynumber002-init-get-usercert-003-ERROR": "Get BrandList Exception.", "mynumber002-init-get-usercert-004-ERROR": "システムエラーが発生しました。", "mynumber002-init-get-usercert-005-INFO": "APIの実行が完了しました。", "読取内容確認_MCAS取得-001-INFO": "リクエストパラメータ: [${0}]", "読取内容確認_MCAS取得-002-ERROR": "リクエストパラメータが不正です。", "読取内容確認_MCAS取得-003-ERROR": "Get Basic4Info Exception.", "読取内容確認_MCAS取得-004-ERROR": "基本4情報の取得に失敗しました。", "読取内容確認_MCAS取得-005-ERROR": "Get Basic4Info Exception.", "読取内容確認_MCAS取得-006-ERROR": "基本4情報の取得に失敗しました。", "読取内容確認_MCAS更新-001-INFO": "リクエストパラメータ: [${0}]", "読取内容確認_MCAS更新-002-ERROR": "リクエストパラメータが不正です。", "読取内容確認_MCAS更新-003-ERROR": "Get elastiCache Exception.", "読取内容確認_MCAS更新-004-ERROR": "セッション情報の取得に失敗しました。", "読取内容確認_MCAS更新-005-ERROR": "Get elastiCache Exception.", "読取内容確認_MCAS更新-006-ERROR": "セッション情報の取得に失敗しました。", "読取内容確認_MCAS更新-007-ERROR": "Get elastiCache Exception.", "読取内容確認_MCAS更新-008-ERROR": "ユーザ情報の取得に失敗しました。", "読取内容確認_MCAS更新-009-ERROR": "EnduserId Exception.", "読取内容確認_MCAS更新-010-ERROR": "登録されているカード情報と異なっています。", "読取内容確認_MCAS更新-011-ERROR": "Update IdeaDaoTaskManagement Exception.", "読取内容確認_MCAS更新-012-ERROR": "データの更新に失敗しました。"}