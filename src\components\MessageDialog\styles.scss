.common-message-dialog-component {
    .MuiDialog-paper {
        position: relative;
        width: 100%;
        height: 100%;
        max-height: 100%;
        margin: 0 !important;
        background-color: transparent !important;
        box-shadow: none !important;
        overflow: hidden;
        border-radius: 0 !important;
    }

    .message-dialog-icon-container {
        background-color: #fff;
        max-width: 400px;
        min-width: 100%;
        box-shadow: 0px 0px 10px 0px #999;

        .icon {
            width: 50px;
            height: 50px;
        }

        .text-red {
            color: red;
        }
    }

    .title {
        font-family: 'Noto Sans JP Bold', Meiryo, メイリオ, Arial, Helvetica, sans-serif;
        font-size: 16px;
        text-align: center;

        &-error {
            font-family: 'Noto Sans JP Bold', Meiryo, メイリオ, Arial, Helvetica, sans-serif;
            font-size: 16px;
            text-align: center;
            color: #de3523;
        }
    }

    .close-button {
        &-container {
            position: absolute;
            right: 0;
            top: 0;
            cursor: pointer;
            z-index: 9999;
        }
    }

    .message-dialog-backdrop {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: 'transparent';
    }
}