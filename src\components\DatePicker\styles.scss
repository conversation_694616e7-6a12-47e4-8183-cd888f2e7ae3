.common-date-picker-component {
    display: flex !important;
    max-height: 40px !important;
    &.error {
        .MuiOutlinedInput-root {
            border-color: #d31e2d !important;
        }
    }
    .MuiOutlinedInput-root {
        border: 1px solid #bfbfbf;
        height: 40px !important;
        max-height: 40px !important;
        box-sizing: border-box;
        border-radius: 4px;
        input {
            height: 100%;
            padding-top: 0 !important;
            padding-bottom: 0 !important;
            font-family: "Noto Sans JP", Meiryo, メイリオ, Arial, Helvetica, sans-serif;
            font-size: 14px;
            color: #222222;
        }

        &.Mui-focused {
            border: 1px solid var(--app-base-color);
        }
        .MuiOutlinedInput-notchedOutline {
            display: none;
        }
    }
    &.read-only {
        input {
            &::placeholder {
                color: #999999;
            }

            &:-ms-input-placeholder {
                color: #999999;
            }

            &::-ms-input-placeholder {
                color: #999999;
            }
            &.Mui-disabled {
                -webkit-text-fill-color: #222222;
            }
        }
    }
}

// there is no wrapper div for date picker, so I need to add this class here
.common-date-picker-error-container {
    min-height: 16px;
    .error {
        font-size: 10px;
        font-family: "Noto Sans JP", <PERSON>ryo, メイリオ, Arial, Helvetica, sans-serif;
        color: #d31e2d;
    }
}
