import { CommonCallback } from './common';

/**
 * Home001InitGetEventTicketListModel
 */
export interface Home001InitGetEventTicketListModel extends CommonCallback {
    // 1ページあたりの取得アイテム数
    PageSize: number;
}

/**
 * Home001InitGetEventTicketListItem
 */
export interface Home001InitGetEventTicketListItem {
    // イベントチケットID
    EventTicketID: string;
    // イベントチケット名
    EventTicketName: string;
    // イベントチケットトップ画像
    EventTicketTopImage: string;
    // イベントチケット種別
    EventTicketType: number;
}

/**
 * Home001InitGetEventTicketListResult
 */
export interface Home001InitGetEventTicketListResult {
    // イベントチケット一覧
    EventTicketList: Home001InitGetEventTicketListItem[];
}
