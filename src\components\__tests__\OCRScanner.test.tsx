import { shallow } from 'enzyme';
import OCRScanner from '../OCRScanner';
import { OCRScannerProps } from '../OCRScanner/OCRScanner';

// mock useNavigate function
const mockedUseNavigate = jest.fn();

/**
 * mock for react-router-dom lib
 */
jest.mock('react-router-dom', () => ({
    ...jest.requireActual('react-router-dom'),
    useSearchParams: () => null,
    useLoaderData: () => null,
    useNavigate: () => mockedUseNavigate,
    useLocation: () => ({
        pathname: '/',
    }),
}));

/**
 * Unit test for OCRScanner
 */
describe('Unit test for OCRScanner', () => {
    /**
     * setup element
     */
    const setup = (p: OCRScannerProps): React.JSX.Element => {
        return <OCRScanner {...p} />;
    };

    /**
     * should render correctly without props
     */
    it('should render correctly without props', () => {
        const wrapper = shallow(setup());

        expect(wrapper).toBeTruthy();
    });
});
