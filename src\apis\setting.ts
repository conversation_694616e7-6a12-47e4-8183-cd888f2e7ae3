/* eslint-disable max-len */
import API from '../constants/api';
import { BaseResponse } from '../models/common';
import { DeleteAccount001MainCheckAuthCodeModel } from '../models/deleteaccount001-main-check-authcode';
import { DeleteAccount001MainSendAuthCodeModel } from '../models/deleteaccount001-main-send-authcode';
import { DeleteAccount001MainDeleteAccountModel } from '../models/deleteaccount002-main-delete-account';
import { SettingMail001MainCheckAuthCodeModel } from '../models/settingmail001-main-check-authcode';
import { SettingMail002MainSendAuthCodeModel } from '../models/settingmail002-main-send-authcode';
import { SettingMail003MainCheckAuthCodeModel } from '../models/settingmail003-main-check-authcode';
import { SettingMail003MainSendAuthCodeModel } from '../models/settingmail003-main-send-authcode';
import { SettingMail003MainUpdateEmailModel } from '../models/settingmail003-main-update-email';
import { SettingMailMagazine001MainUpdateMailMagazineModel } from '../models/settingmailmagazine001-main-update-mail-magazine';
import { SettingPass001MainCheckAuthCodeModel } from '../models/settingpass001-main-check-authcode';
import { SettingPass002MainUpdateAuthCodeModel } from '../models/settingpass002-main-update-password';
import { SettingTel001MainCheckAuthCodeModel } from '../models/settingtel001-main-check-authcode';
import { SettingTel002MainSendAuthCodeModel } from '../models/settingtel002-main-send-authcode';
import { SettingTel003MainCheckAuthCodeModel } from '../models/settingtel003-main-check-authcode';
import { SettingTel003MainSendAuthCodeModel } from '../models/settingtel003-main-send-authcode';
import { SettingTel003MainUpdateTelModel } from '../models/settingtel003-main-update-tel';
import {
    SettingTop001InitGetUserInfoModel,
    SettingTop001InitGetUserInfoResult,
} from '../models/settingtop001-init-get-userinfo';
import {
    SettingTop001MainSendAuthCodeModel,
    SettingTop001MainSendAuthCodeResult,
} from '../models/settingtop001-main-send-authcode';
import { SettingTop001MainUpdateNicknameModel } from '../models/settingtop001-main-update-nickname';

import { SettingPersonal001MainCheckAuthCodeModel } from '../models/settingpersonal001-main-check-authcode';
import {
    SettingPersonal001MainSendAuthCodeModel,
    SettingPersonal001MainSendAuthCodeResult,
} from '../models/settingpersonal001-main-send-authcode';
import { SettingPersonal002InitGetPersonalInfoResult } from '../models/settingpersonal002-init-get-personalinfo';
import { SettingPersonal003MainUpdateAddressModel } from '../models/settingpersonal003-main-update-address';
import {
    SettingTop001MainGetLineTokenModel,
    SettingTop001MainGetLineTokenResult,
} from '../models/settingtop001-main-get-line-token';
import { SettingTop001MainUnlinkLineModel } from '../models/settingtop001-main-unlink-line';
import createAPI from './baseApi';

/**
 * SettingAPI
 */
class SettingAPI {
    /**
     * settingTop001InitGetUserInfo
     * @param data SettingTop001InitGetUserInfoModel
     * @returns Promise<BaseResponse<SettingTop001InitGetUserInfoResult>>
     */
    static settingTop001InitGetUserInfo = (
        data: SettingTop001InitGetUserInfoModel
    ): Promise<BaseResponse<SettingTop001InitGetUserInfoResult>> => {
        return createAPI<SettingTop001InitGetUserInfoResult>({
            url: API.SETTINGTOP001_INIT_GET_USERINFO,
            data,
        });
    };

    /**
     * settingTop001MainSendAuthCode
     * @param data SettingTop001MainSendAuthCodeModel
     * @returns Promise<BaseResponse<SettingTop001MainSendAuthCodeResult>>
     */
    static settingTop001MainSendAuthCode = (
        data: SettingTop001MainSendAuthCodeModel
    ): Promise<BaseResponse<SettingTop001MainSendAuthCodeResult>> => {
        return createAPI<SettingTop001MainSendAuthCodeResult>({
            url: API.SETTINGTOP001_MAIN_SEND_AUTHCODE,
            data,
        });
    };

    /**
     * settingMailMagazine001MainUpdateMailMagazine
     * @param data SettingMailMagazine001MainUpdateMailMagazineModel
     * @returns Promise<BaseResponse>
     */
    static settingMailMagazine001MainUpdateMailMagazine = (
        data: SettingMailMagazine001MainUpdateMailMagazineModel
    ): Promise<BaseResponse> => {
        return createAPI({
            url: API.SETTINGMAILMAGAZINE001_MAIN_UPDATE_MAILMAGAZINE,
            data,
        });
    };

    /**
     * settingTop001MainUpdateNickname
     * @param data SettingTop001MainUpdateNicknameModel
     * @returns Promise<BaseResponse>
     */
    static settingTop001MainUpdateNickname = (data: SettingTop001MainUpdateNicknameModel): Promise<BaseResponse> => {
        return createAPI({
            url: API.SETTINGTOP001_MAIN_UPDATE_NICKNAME,
            data,
        });
    };

    /**
     * tel001MainSendAuthCode
     * @returns Promise<BaseResponse
     */
    static tel001MainSendAuthCode = (): Promise<BaseResponse> => {
        return createAPI({
            url: API.SETTINGTEL001_MAIN_SEND_AUTHCODE,
        });
    };

    /**
     * tel001MainCheckAuthCode
     * @returns Promise<BaseResponses
     */
    static tel001MainCheckAuthCode = (data: SettingTel001MainCheckAuthCodeModel): Promise<BaseResponse> => {
        return createAPI({
            url: API.SETTINGTEL001_MAIN_CHECK_AUTHCODE,
            data,
        });
    };

    /**
     * tel002MainSendAuthCode
     * @returns Promise<BaseResponse<SettingTel002MainSendAuthCodeResult>
     */
    static tel002MainSendAuthCode = (data: SettingTel002MainSendAuthCodeModel): Promise<BaseResponse> => {
        return createAPI({
            url: API.SETTINGTEL002_MAIN_SEND_AUTHCODE,
            data,
        });
    };

    /**
     * tel003MainCheckAuthCode
     * @returns Promise<BaseResponse<SettingTel003MainCheckAuthCodeModel>
     */
    static tel003MainCheckAuthCode = (data: SettingTel003MainCheckAuthCodeModel): Promise<BaseResponse> => {
        return createAPI({
            url: API.SETTINGTEL003_MAIN_CHECK_AUTHCODE,
            data,
        });
    };

    /**
     * tel003MainSendAuthCode
     * @returns Promise<BaseResponse<SettingTel003MainSendAuthCodeModel>
     */
    static tel003MainSendAuthCode = (data: SettingTel003MainSendAuthCodeModel): Promise<BaseResponse> => {
        return createAPI({
            url: API.SETTINGTEL003_MAIN_SEND_AUTHCODE,
            data,
        });
    };

    /**
     * tel003MainUpdateTel
     * @returns Promise<BaseResponse<SettingTel003MainUpdateTelModel>
     */
    static tel003MainUpdateTel = (data: SettingTel003MainUpdateTelModel): Promise<BaseResponse> => {
        return createAPI({
            url: API.SETTINGTEL003_MAIN_UPDATE_TEL,
            data,
        });
    };

    /**
     * deleteAccount001MainSendAuthCode
     * @param data DeleteAccount001MainSendAuthCodeModel
     * @returns Promise<BaseResponse<>>
     */
    static deleteAccount001MainSendAuthCode = (
        data: DeleteAccount001MainSendAuthCodeModel
    ): Promise<BaseResponse<DeleteAccount001MainCheckAuthCodeModel>> => {
        return createAPI({
            url: API.DELETEACCOUNT001_MAIN_SEND_AUTHCODE,
            data,
        });
    };

    /**
     * deleteAccount001MainCheckAuthCode
     * @param data DeleteAccount001MainCheckAuthCodeModel
     * @returns Promise<BaseResponse<>>
     */
    static deleteAccount001MainCheckAuthCode = (
        data: DeleteAccount001MainCheckAuthCodeModel
    ): Promise<BaseResponse> => {
        return createAPI({
            url: API.DELETEACCOUNT001_MAIN_CHECK_AUTHCODE,
            data,
        });
    };

    /**
     * deleteAccount001MainDeleteAccount
     * @param data DeleteAccount001MainCheckAuthCodeModel
     * @returns Promise<BaseResponse<>>
     */
    static deleteAccount001MainDeleteAccount = (
        data: DeleteAccount001MainDeleteAccountModel
    ): Promise<BaseResponse> => {
        return createAPI({
            url: API.DELETEACCOUNT002_MAIN_DELETE_ACCOUNT,
            data,
        });
    };

    /**
     * mail001MainSendAuthCode
     * @returns Promise<BaseResponse>
     */
    static mail001MainSendAuthCode = (): Promise<BaseResponse> => {
        return createAPI({
            url: API.SETTINGMAIL001_MAIN_SEND_AUTHCODE,
        });
    };

    /**
     * mail001MainCheckAuthCode
     * @returns Promise<BaseResponse>
     */
    static mail001MainCheckAuthCode = (data: SettingMail001MainCheckAuthCodeModel): Promise<BaseResponse> => {
        return createAPI({
            url: API.SETTINGMAIL001_MAIN_CHECK_AUTHCODE,
            data,
        });
    };

    /**
     * mail002MainSendAuthCode
     * @returns Promise<BaseResponse>
     */
    static mail002MainSendAuthCode = (data: SettingMail002MainSendAuthCodeModel): Promise<BaseResponse> => {
        return createAPI({
            url: API.SETTINGMAIL002_MAIN_SEND_AUTHCODE,
            data,
        });
    };

    /**
     * mail003MainSendAuthCode
     * @returns Promise<BaseResponses>
     */
    static mail003MainSendAuthCode = (data: SettingMail003MainSendAuthCodeModel): Promise<BaseResponse> => {
        return createAPI({
            url: API.SETTINGMAIL003_MAIN_SEND_AUTHCODE,
            data,
        });
    };

    /**
     * mail003MainCheckAuthCode
     * @returns Promise<BaseResponses>
     */
    static mail003MainCheckAuthCode = (data: SettingMail003MainCheckAuthCodeModel): Promise<BaseResponse> => {
        return createAPI({
            url: API.SETTINGMAIL003_MAIN_CHECK_AUTHCODE,
            data,
        });
    };

    /**
     * mail003MainUpdateEmail
     * @returns Promise<BaseResponses>
     */
    static mail003MainUpdateEmail = (data: SettingMail003MainUpdateEmailModel): Promise<BaseResponse> => {
        return createAPI({
            url: API.SETTINGMAIL003_MAIN_UPDATE_EMAIL,
            data,
        });
    };

    /**
     * pass001MainCheckAuthCode
     * @returns Promise<BaseResponse>
     */
    static pass001MainCheckAuthCode = (data: SettingPass001MainCheckAuthCodeModel): Promise<BaseResponse> => {
        return createAPI({
            url: API.SETTINGPASS001_MAIN_CHECK_AUTH_CODE,
            data,
        });
    };

    /**
     * pass001MainSendAuthCode
     * @returns Promise<BaseResponse>
     */
    static pass001MainSendAuthCode = (): Promise<BaseResponse> => {
        return createAPI({
            url: API.SETTINGPASS001_MAIN_SEND_AUTH_CODE,
        });
    };

    /**
     * pass002MainUpdateAuthCode
     * @returns Promise<BaseResponse>
     */
    static pass002MainUpdateAuthCode = (data: SettingPass002MainUpdateAuthCodeModel): Promise<BaseResponse> => {
        return createAPI({
            url: API.SETTINGPASS002_MAIN_UPDATE_PASSWORD,
            data,
        });
    };

    /**
     * settingPersonal001MainSendAuthCode
     * @param data SettingPersonal001MainSendAuthCodeModel
     * @returns Promise<BaseResponse<SettingPersonal001MainSendAuthCodeResult>>
     */
    static settingPersonal001MainSendAuthCode = (
        data: SettingPersonal001MainSendAuthCodeModel
    ): Promise<BaseResponse<SettingPersonal001MainSendAuthCodeResult>> => {
        return createAPI({
            url: API.SETTING_PERSONAL001_MAIN_SEND_AUTHCODE,
            data,
        });
    };

    /**
     * settingPersonal001MainCheckAuthCode
     * @param data SettingPersonal001MainCheckAuthcodeModel
     * @returns Promise<BaseResponse>
     */
    static settingPersonal001MainCheckAuthCode = (
        data: SettingPersonal001MainCheckAuthCodeModel
    ): Promise<BaseResponse> => {
        return createAPI({
            url: API.SETTING_PERSONAL001_MAIN_CHECK_AUTHCODE,
            data,
        });
    };

    /**
     * settingPersonal002InitGetPersonalInfo
     * @param data { useLock: boolean }
     * @returns Promise<BaseResponse<SettingPersonal002InitGetPersonalInfoResult>>
     */
    static settingPersonal002InitGetPersonalInfo = (data: {
        useLock: boolean;
    }): Promise<BaseResponse<SettingPersonal002InitGetPersonalInfoResult>> => {
        return createAPI({
            url: API.SETTING_PERSONAL002_INIT_GET_PERSONALINFO,
            data,
        });
    };

    /**
     * settingPersonal003MainUpdateAddress
     * @param data SettingPersonal003MainUpdateAddressModel
     * @returns Promise<BaseResponse>
     */
    static settingPersonal003MainUpdateAddress = (
        data: SettingPersonal003MainUpdateAddressModel
    ): Promise<BaseResponse> => {
        return createAPI({
            url: API.SETTING_PERSONAL003_MAIN_UPDATE_ADDRESS,
            data,
        });
    };

    /**
     * settingTop001MainUnlinkLine
     * @param data SettingTop001MainUnlinkLineModel
     * @returns Promise<BaseResponse>
     */
    static settingTop001MainUnlinkLine = (data: SettingTop001MainUnlinkLineModel): Promise<BaseResponse> => {
        return createAPI({
            url: API.SETTINGTOP001_MAIN_UNLINK_LINE,
            data,
        });
    };

    /**
     * settingTop001MainGetLineToken
     * @param data SettingTop001MainGetLineTokenModel
     * @returns Promise<BaseResponse<SettingTop001MainGetLineTokenResult>>
     */
    static settingTop001MainGetLineToken = (
        data: SettingTop001MainGetLineTokenModel
    ): Promise<BaseResponse<SettingTop001MainGetLineTokenResult>> => {
        return createAPI({
            url: API.SETTINGTOP001_MAIN_GET_LINE_TOKEN,
            data,
        });
    };
}
export default SettingAPI;
