import { AxiosError, AxiosRequestConfig } from 'axios';
import { APIRequestOptions, APIResponse, CommonCallback } from '../models/common';
import axiosInstance from '../services/api.service';
import CommonUtils from '../utils/common';

/**
 * createAPI
 * @param options APIRequestOptions
 * @returns Promise<APIResponse>
 */
const createAPI = async <T = unknown, K = unknown>(options: APIRequestOptions): Promise<APIResponse<T, K>> => {
    // request options
    const { url, data = {} as CommonCallback, method = 'POST', signal } = options;

    const { useLock, ...payload } = data;
    // request config
    const config: AxiosRequestConfig = {
        url,
        method,
        data: payload,
        signal,
    };

    // lock screen if useLock true
    if (useLock) {
        CommonUtils.lockScreen();
    }

    // request response
    return axiosInstance(config)
        .then((res) => {
            const { statusCode, body } = res.data;
            const { status, result, Message = 'api.common.unknown_error', ...rest } = body || {};

            const response: APIResponse<T, K> = {
                isSuccess: true,
                status,
                Message,
                result: result || rest,
                statusCode,
                ...rest,
            };

            return response;
        })
        .catch((error: AxiosError) => {
            const remain = error.response?.data as K;

            const response: APIResponse<T, K> = {
                isSuccess: false,
                status: 1,
                Message: error?.code || 'api.common.unknown_error',
                statusCode: error?.status,
                error,
                code: error?.code || 'ERR_UNKNOWN',
                ...remain,
            };
            return response;
        })
        .finally(() => {
            // if useLock is true
            if (useLock) {
                // unlock screen after done
                CommonUtils.unlockScreen();
            }
        });
};

export default createAPI;
