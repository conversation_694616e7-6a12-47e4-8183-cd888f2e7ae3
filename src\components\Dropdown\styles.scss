.common-dropdown-component {
    .dropdown-container {
        margin-top: 12px;

        .MuiInputBase-root {
            border-width: 1px;
            border-style: solid;
            border-color: #bfbfbf;
            border-radius: 5px;
            min-height: 40px;
            background-color: #fff;
            overflow: hidden;

            &.Mui-focused {
                border: 1px solid var(--app-base-color) !important;
            }

            &.error {
                border: 1px solid #d31e2d !important;
            }
        }

        .MuiSelect-select {
            padding-left: 12px !important;
            padding-right: 32px !important;
            background-color: #fff;
            font-size: 15px;
            font-family: 'Noto Sans JP', Meiryo, メイリオ, Arial, Helvetica, sans-serif;
        }

        .MuiSelect-icon {
            top: calc(50% - 12px) !important;
            margin-right: 12px;
            transition: all 0.2s ease;
        }

        .MuiMenuItem-root {
            font-size: 10px;
        }

        .error-container {
            min-height: 16px;

            .error {
                font-size: 10px;
                font-family: 'Noto Sans JP', <PERSON><PERSON>, メイリオ, Arial, Helvetica, sans-serif;
                color: #d31e2d;
            }
        }
    }
}

.dropdown-component-menu-content-props {
    .MuiTouchRipple-root.dropdown-ripple {
        pointer-events: stroke !important;
    }

    .MuiMenuItem-root.dropdown-component-menu-item {
        text-overflow: ellipsis;
        overflow: hidden;
        min-height: auto !important;
        white-space: nowrap;
        width: 100%;
        display: inline-block;
    }
    .MuiMenuItem-root.dropdown-component-menu-item.with-icon {
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .MuiMenu-paper {
        max-width: min(calc(100% - 32px), 568px) !important;
        width: min(calc(100% - 32px), 568px) !important;
        min-width: auto !important;
    }
}
