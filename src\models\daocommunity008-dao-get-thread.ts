import { CommonCallback } from './common';

/**
 * DAOCommunity008DaoGetThreadModel
 */
export interface DAOCommunity008DaoGetThreadModel extends CommonCallback {
    // DaoID
    DaoID: string;
    // カテゴリーID
    CategoryID: string;
    // チャネルID
    ChannelID: string;
    // スレッドID
    ThreadID?: string;
    // チャットID
    ChatID?: string;
    // 前スレッド・次スレッド種別
    PrevNextType?: number;
    // 最後の並び順番号（この番号を基準にデータを取得）
    LastSortNo?: number;
    // スレッド取得上限数
    LimitCount?: number;
    // カテゴリー・チャネル一覧選択フラグ
    IsListSelect?: boolean;
    // チャット投稿登録後フラグ
    IsAfterPutChat?: boolean;
    // 最新の並び順（登録されたチャットのSortNo）
    NewSortNo?: number;
    // タイマー取得フラグ
    IsTimer?: boolean;
}

/**
 * DAOCommunity008DaoGetThreadMentionItem
 */
export interface DAOCommunity008DaoGetThreadMentionItem {
    // 応援者ID
    CheerID: string;
    // ニックネーム
    NickName: string;
    // メッセージ
    Message: string;
    // プロフィール画像ファイル名
    ProfileImageFileName: string;
    // optional
    ImageLink?: string;
}

/**
 * DAOCommunity008DaoGetThreadChatItem
 */
export interface DAOCommunity008DaoGetThreadChatItem {
    // チャットID
    ChatID: string;
    // スレッドID
    ThreadID?: string;
    // スレッド種別 // 0:本文 1:返信
    ThreadType: number;
    // タイトル（0:本文の場合のみ）
    Title?: string;
    // 投稿内容
    Content: string;
    // 編集フラグ
    EditFlag: number;
    // 削除フラグ
    DeleteFlag: number;
    // 有効並び順
    VaridSortNo: number;
    // 投稿者ID
    PostCheerID: string;
    // 投稿者名
    PostCheerName: string;
    // 投稿日時
    PostDateTime: string;
    // プロフィールメッセージ
    Message: string;
    // プロフィール画像ファイル名
    ProfileImageFileName: string;
    // リアクションリスト
    ReactionList?: DAOCommunity008ReactionItem[];
    // メンションリスト
    MentionList?: DAOCommunity008DaoGetThreadMentionItem[];
}

/**
 * DAOCommunity008DaoGetThreadThreadItem
 */
export interface DAOCommunity008DaoGetThreadThreadItem {
    // スレッドID
    ThreadID: string;
    // 有効並び順
    VaridSortNo: number;
    // 更新日時
    UpdateDateTime: string;
    // 前チャットデータ存在フラグ
    IsPrevChatDataExists: boolean;
    // 返信チャット件数
    ReplyCount: number;
    // チャットリスト
    ChatList: DAOCommunity008DaoGetThreadChatItem[];
}

/**
 * DAOCommunity008ReactionItem
 */
export interface DAOCommunity008ReactionItem {
    // スレッドID
    ThreadID: string;
    // チャットID
    ChatID: string;
    // リアクション画像ID
    ReactionImageID: string;
    // リアクション応援者ID
    ReactionCheerID: string;
}

/**
 * DAOCommunity008DaoGetThreadResult
 */
export interface DAOCommunity008DaoGetThreadResult {
    // スレッドリスト
    ThreadList: DAOCommunity008DaoGetThreadThreadItem[];
    // 1スレッド表示フラグ
    IsOneThread: boolean;
    // カテゴリー名
    CategoryName?: string;
    // チャネル名
    ChannelName?: string;
    // 前スレッドデータ存在フラグ
    IsPrevThreadDataExists: boolean;
}
