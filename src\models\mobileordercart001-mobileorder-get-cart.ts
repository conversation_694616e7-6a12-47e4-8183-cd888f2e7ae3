import { CommonCallback } from './common';

/**
 * MobileOrderCart001MobileOrderGetCartModel
 */
export interface MobileOrderCart001MobileOrderGetCartModel extends CommonCallback {
    StoreID: string;
}

/**
 * MobileOrderCart001MobileOrderGetCartItemList
 */
export interface MobileOrderCart001MobileOrderGetCartItemList {
    IsItemDelete?: boolean;
    IsItemUpdate?: boolean;
    IsStockOver?: boolean;
    ItemID: string;
    DetailID: string;
    ItemName: string;
    ItemImage?: string;
    Price: number;
    Quantity: number;
    FakeQuantity: number;
    AvailableStock: number;
    ItemStatus: string;
}

export interface MobileOrderCart001MobileOrderGetCartList {
    ItemList: MobileOrderCart001MobileOrderGetCartItemList[];
    UpdateDateTime: string;
}

/**
 * MobileOrderCart001MobileOrderGetCartResult
 */
export interface MobileOrderCart001MobileOrderGetCartResult {
    CartList: MobileOrderCart001MobileOrderGetCartList;
}
