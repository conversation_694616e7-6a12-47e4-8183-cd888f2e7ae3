import { CommonCallback } from './common';

/**
 * News001InitGetNewsListModel
 */
export interface News001InitGetNewsListModel extends CommonCallback {
    // ブランドID
    PageSize: number;
    // 排他開始キー
    ExclusiveStartKey?: string;
}
/**
 * News001InitGetNewsListItem
 */
export interface News001InitGetNewsListItem {
    // MessageID
    MessageID?: string;
    // 発行日時
    PublishDateTime: string;
    // タイトル
    Subject: string;
    // 既読フラグ
    ReadFlag: boolean;
}

/**
 * News001InitGetNewsListResult
 */
export interface News001InitGetNewsListResult {
    // お知らせ一覧
    NewsList: News001InitGetNewsListItem[];
    // LastEvaluatedKey
    LastEvaluatedKey?: string;
}

/**
 * News001InitGetPersonalNewsListModel
 */

export type News001InitGetPersonalNewsListModel = News001InitGetNewsListModel;
/**
 * News001InitGetPersonalNewsListItem
 */
export type News001InitGetPersonalNewsListItem = News001InitGetNewsListItem;

/**
 * News001InitGetPersonalNewsListResult
 */
export type News001InitGetPersonalNewsListResult = News001InitGetNewsListResult;

/**
 * News001InitGetImportantNewsListModel
 */
export type News001InitGetImportantNewsListModel = News001InitGetNewsListModel;

/**
 * News001InitGetImportantNewsListItem
 */
export type News001InitGetImportantNewsListItem = News001InitGetNewsListItem;

/**
 * News001InitGetImportantNewsListResult
 */
export type News001InitGetImportantNewsListResult = News001InitGetNewsListResult;
