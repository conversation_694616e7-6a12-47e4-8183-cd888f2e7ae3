import { CommonCallback } from './common';
import { Home001InitGetCoinItem } from './home001-init-get-coin';

/**
 * PaySingle001MainSearchStorePublicCodeModel
 */
export interface PaySingle001MainSearchStorePublicCodeModel extends CommonCallback {
    // 公開アドレス
    StorePublicCode: string;
    // メダルサービスID
    MedalServiceID: string;
}

/**
 * PaySingle001MainSearchStorePublicCodeResult
 */
export interface PaySingle001MainSearchStorePublicCodeResult {
    // 加盟店ID
    StoreID: string;
    // 加盟店名称
    StoreName: string;
    // 加盟店カナ
    StoreNameKana: string;
    // 初期支払い金額
    DefaultPaymentAmount: number;
}

/**
 * PaySingleLocationState
 */
export interface PaySingleLocationState extends PaySingle001MainSearchStorePublicCodeResult {
    StoreCode: string;
    Amount: number;
    giftItem: Home001InitGetCoinItem;
}
