import { CommonCallback } from './common';

/**
 * DAOVoting001DaoGetVotingThemeListModel
 */
export interface DAOVoting001DaoGetVotingThemeListModel extends CommonCallback {
    // DAOID
    DaoID: string;
}

/**
 * DAOVoting001DaoGetVotingThemeItem
 */
export interface DAOVoting001DaoGetVotingThemeItem {
    // 投票テーマID
    ThemeID: string;
    // タイトル
    Title: string;
    // 内容説明
    Explanation: string;
    // 投票開始日（yyyy/MM/dd HH:mm）
    VotingStartDateTime: string;
    // 投票終了日（yyyy/MM/dd HH:mm）
    VotingEndDateTime: string;
    // 投票テーマ作成日（yyyy/MM/dd HH:mm）
    CreateDateTime: string;
    // 投票テーマ作成者
    CreateCheerName: string;
    // イベント状態コード
    EventStatusCode: number;
    // 投票日時情報
    DateInfo: string;
    // 投票成立情報
    VotingSuccessInfo: string;
    // 投票済みフラグ
    IsVoted: boolean;
}

/**
 * DAOVoting001DaoGetVotingThemeListResult
 */
export interface DAOVoting001DaoGetVotingThemeListResult {
    // 投票テーマリスト
    VotingThemeList: DAOVoting001DaoGetVotingThemeItem[];
}
