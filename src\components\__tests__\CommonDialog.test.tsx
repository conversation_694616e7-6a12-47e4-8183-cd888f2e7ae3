import { Dialog } from '@mui/material';
import { shallow } from 'enzyme';
import { createRef } from 'react';
import CommonDialog, { CommonDialogProps, CommonDialogRef } from '../CommonDialog/CommonDialog';
import Icons from '../Icons/Icons';

/**
 * Unit test for CommonDialog
 */
describe('Unit test for CommonDialog', () => {
    /**
     * setup element
     * @param p CommonDialogProps
     */
    const setup = (p?: Omit<CommonDialogProps, 'children'>) => {
        const ref = createRef<CommonDialogRef>();
        const s = shallow(
            <CommonDialog {...p} ref={ref}>
                <div>dialog content</div>
            </CommonDialog>
        );

        return {
            s: s,
            props: p,
            ref,
        };
    };

    /**
     * should be show when open is true
     */
    it('should be show when open is true', () => {
        const { s, ref } = setup();

        // show dialog
        ref.current?.toggleDialog();

        expect(s.find('div.common-dialog-component')).toBeTruthy();
    });

    /**
     * should be show when call toggleDialog with open is true
     */
    it('should be show when call toggleDialog with open is true', () => {
        const { s, ref } = setup();

        // show dialog
        ref.current?.toggleDialog(true);

        expect(s.find('div.common-dialog-component')).toBeTruthy();
    });

    /**
     * should render with useTransaction
     */
    it('should render with useTransaction', () => {
        const { s, ref } = setup({
            useTransaction: true,
        });

        // show dialog
        ref.current?.toggleDialog(true);

        expect(s.find('div.common-dialog-component')).toBeTruthy();
    });

    /**
     * CloseIcon on the top right
     */
    describe('CloseIcon on the top right', () => {
        /**
         * should render close icon on the top right when useDialogCloseButton passed
         */
        it('should render useDialogCloseButton passed', () => {
            const { s, ref } = setup({
                useDialogCloseButton: true,
            });

            // show dialog
            ref.current?.toggleDialog();

            expect(s.find('div.common-dialog-component')).toBeTruthy();

            expect(s.find(Icons.DialogCloseIcon)).toBeTruthy();
        });

        /**
         * on close icon click
         */
        it('on close icon click', () => {
            const { s, ref } = setup({
                useDialogCloseButton: true,
            });

            // show dialog
            ref.current?.toggleDialog();

            const button = s.find('div.close-button-container');

            button.simulate('click');
        });

        /**
         * on close icon click
         */
        it('on close icon click with callback passed in useDialogCloseButton', () => {
            const callback = jest.fn();
            const { s, ref } = setup({
                useDialogCloseButton: {
                    callback,
                },
            });

            // show dialog
            ref.current?.toggleDialog();

            const button = s.find('div.close-button-container');

            button.simulate('click');

            expect(callback).toHaveBeenCalled();
        });
    });

    /**
     * should render with backdrop
     */
    describe('should render with backdrop', () => {
        /**
         * when useBackdropDismiss is passed
         */
        it('when useBackdropDismiss is passed', () => {
            const { s } = setup({
                useBackdropDismiss: true,
            });

            expect(s.find('div.dialog-content-overlay')).toBeTruthy();
        });

        /**
         * on backdrop click
         */
        it('on backdrop click', () => {
            const { s } = setup({
                useBackdropDismiss: true,
            });

            const backdrop = s.find('div.dialog-content-overlay');

            backdrop.simulate('click');
        });

        /**
         * on backdrop click with callback
         */
        it('on backdrop click with callback', () => {
            const callback = jest.fn();
            const { s } = setup({
                useBackdropDismiss: {
                    callback,
                },
            });

            const backdrop = s.find('div.dialog-content-overlay');

            backdrop.simulate('click');

            expect(callback).toHaveBeenCalled();
        });

        it('onClose dialog with useBackdropDismiss with callback', () => {
            const callback = jest.fn();
            const { s } = setup({
                useBackdropDismiss: {
                    callback,
                },
            });

            const { onClose } = s.find(Dialog).props();

            onClose?.({}, 'backdropClick');

            expect(callback).toHaveBeenCalled();
        });

        it('onClose dialog with useBackdropDismiss without callback', () => {
            const { s } = setup({
                useBackdropDismiss: true,
            });

            const { onClose } = s.find(Dialog).props();

            onClose?.({}, 'backdropClick');
        });
    });
});
