import { Typography } from '@mui/material';
import { mount, shallow } from 'enzyme';
import CommonDialog from '../CommonDialog';
import QRScanner, { Props } from '../QRScanner/QRScanner';
import QrCodeReader from '../QrCodeReader';

// mock useNavigate function
const mockedUseNavigate = jest.fn();

/**
 * mock for react-router-dom lib
 */
jest.mock('react-router-dom', () => ({
    ...jest.requireActual('react-router-dom'),
    useSearchParams: () => null,
    useLoaderData: () => null,
    useNavigate: () => mockedUseNavigate,
}));

/**
 * Unit test for QRScanner component
 */
describe('Unit test for QRScanner component', () => {
    /**
     * setup element
     * @param props Props
     */
    const setup = (props: Props) => {
        const s = shallow(<QRScanner {...props} />);
        const m = mount(<QRScanner {...props} />);

        return {
            shallow: s,
            mount: m,
            props,
        };
    };

    /**
     * display enough elements
     */
    describe('display enough elements', () => {
        const onScan = jest.fn();
        const onChangeMode = jest.fn();
        const onLeftButtonClick = jest.fn();
        const { mount } = setup({
            onScan,
            onChangeMode,
            header: {
                title: 'qr scanner title',
                onLeftButtonClick,
            },
        });

        /**
         * display title
         */
        it('display title', () => {
            expect(mount.find('div.qr-scan-header-title').text()).toEqual('qr scanner title');
        });

        /**
         * display header
         */
        it('display header', () => {
            expect(mount.find('div.qr-scan-body').find(Typography).text()).toEqual('カメラをQRコードに向けてください');
        });

        /**
         * display qr code reader
         */
        it('display qr code reader', () => {
            expect(mount.find(QrCodeReader).length).toEqual(1);
        });

        /**
         * display text cannot read qr code
         */
        it('display text cannot read qr code', () => {
            expect(mount.find('div.qr-scan-input').find('span.qr-scan-input-description').text()).toEqual(
                'QRコードが読み取れない'
            );
        });

        it('error dialog', () => {
            expect(mount.find(CommonDialog).length).toEqual(1);
        });
    });

    /**
     * Actions
     */
    describe('Actions', () => {
        /**
         * navigate back when do not pass onLeftButtonClick
         */
        it('navigate back when do not pass onLeftButtonClick', () => {
            const { mount } = setup({
                header: {
                    title: 'qr scanner title',
                },
            });
            const buttonBack = mount.find('div.back-icon').at(0);

            buttonBack.simulate('click');

            expect(mockedUseNavigate).toHaveBeenCalledWith(-1);
        });

        /**
         * navigate when pass onLeftButtonClick
         */
        it('navigate when pass onLeftButtonClick', () => {
            const onLeftButtonClick = jest.fn();
            const { mount } = setup({
                header: {
                    title: 'qr scanner title',
                    onLeftButtonClick,
                },
            });
            const buttonBack = mount.find('div.back-icon').at(0);

            buttonBack.simulate('click');

            expect(onLeftButtonClick).toHaveBeenCalledTimes(1);
        });

        /**
         * navigate to next screen when click QRコードが読み取れない
         */
        it('navigate to next screen when click QRコードが読み取れない', () => {
            const onScan = jest.fn();
            const onChangeMode = jest.fn();
            const { mount } = setup({
                onScan,
                onChangeMode,
                header: {
                    title: 'qr scanner title',
                },
            });

            const btnNext = mount.find('div.qr-scan-input').at(0);
            btnNext.simulate('click');

            expect(onChangeMode).toHaveBeenCalledTimes(1);
        });

        /**
         * check handle scan
         */
        it('check handle scan', () => {
            const onScanResult = jest.fn();
            const qrReader = mount(<QrCodeReader onScanResult={onScanResult} scanTimeout={3000} />);
            console.debug(qrReader);
            expect(onScanResult).toHaveBeenCalledTimes(1);
        });
    });
});
