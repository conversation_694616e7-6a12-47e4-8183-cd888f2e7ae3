// AppHeader style
#AppHeader {
    max-width: var(--app-main-width);
    border-color: #efefef;
    border-width: 1px;
    border-style: solid;
    z-index: 999;

    .back-icon {
        min-width: 42px;
        min-height: 42px;
        display: flex;
        justify-content: center;
        align-items: center;
        overflow: hidden;
        padding-left: 16px;
        padding-right: 16px;
        cursor: pointer;
    }

    .back-icon-width-50 {
        min-width: 50px;
        min-height: 42px;
        display: flex;
        justify-content: start;
        align-items: center;
        overflow: hidden;
        margin-left: 16px;
        cursor: pointer;
    }

    .close-icon {
        min-width: 52px;
        min-height: 50px;
        display: flex;
        justify-content: center;
        align-items: center;
        overflow: hidden;
        padding-left: 16px;
        padding-right: 16px;
        cursor: pointer;
    }

    .left,
    .right {
        min-width: 50px;
        min-height: 50px;
        display: flex;
        justify-content: center;
        align-items: center;
        overflow: hidden;
    }

    .right {
        position: relative;
        width: 200px;
    }

    .logo {
        height: 32px;
        max-height: 32px;
        background-color: #ffffff;
        cursor: pointer;

        &-full {
            max-width: 100px;
            min-width: 100px;
            width: 100px;
            font-size: 10px;
        }

        &-half {
            max-width: 50px;
            min-width: 50px;
            width: 50px;
            font-size: 10px;
        }
    }

    .title {
        padding-top: 12px;
        padding-bottom: 11px;
        font-family: 'Noto Sans JP Bold', Meiryo, メイリオ, Arial, Helvetica, sans-serif;
        font-size: 14px !important;
        color: #222222;
        text-align: center;
        pointer-events: none;
    }

    .translate-container,
    .main-container {
        position: relative;

        .title,
        .logo {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
        }
    }

    .logo-center-and-menu-right {
        position: relative;

        &__right {
            min-width: 100px;
            position: absolute;
            right: 0;
        }

        &__infoButton {
            cursor: pointer;

            &__text {
                font-size: 8px;
                color: var(--app-base-color);
                white-space: nowrap;
                font-family: 'Noto Sans JP Bold', Meiryo, メイリオ, Arial, Helvetica, sans-serif;
            }

            &__has-unread {
                top: -5px;
                right: -5px;
            }
        }

        &__menuButton {
            margin-left: 4px;
            cursor: pointer;

            &__text {
                font-size: 8px;
                color: var(--app-base-color);
                white-space: nowrap;
                font-family: 'Noto Sans JP Bold', Meiryo, メイリオ, Arial, Helvetica, sans-serif;
                margin-top: 2px;
            }
        }
    }

    .back-left-with-title-center-and-menu-right {
        position: relative;
        &__left {
            min-width: 100px;

            &__button {
                min-height: 42px;
                cursor: pointer;
            }
        }

        &__right {
            min-width: 100px;
        }

        &__infoButton {
            cursor: pointer;

            &__text {
                font-size: 8px;
                color: var(--app-base-color);
                white-space: nowrap;
                font-family: 'Noto Sans JP Bold', Meiryo, メイリオ, Arial, Helvetica, sans-serif;
            }
        }

        &__menuButton {
            margin-left: 4px;
            cursor: pointer;

            &__text {
                font-size: 8px;
                color: var(--app-base-color);
                white-space: nowrap;
                font-family: 'Noto Sans JP Bold', Meiryo, メイリオ, Arial, Helvetica, sans-serif;
                margin-top: 2px;
            }
        }
    }

    .top-text {
        font-size: 8px;
        color: var(--app-base-color);
        white-space: nowrap;
        font-family: 'Noto Sans JP Bold', Meiryo, メイリオ, Arial, Helvetica, sans-serif;
    }

    .has-unread {
        position: absolute;
        top: -12px;
        right: 2px;
    }

    .my-page-container {
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        height: 34px;

        .click-here-user {
            font-family: 'Noto Sans JP', Meiryo, メイリオ, Arial, Helvetica, sans-serif;
            font-size: 10px;
            color: #222222;
            line-height: 1;
            text-align: center;
        }
        .my-page-btn {
            height: 19px;
            width: 80px;
            line-height: 1.25;
            background-color: var(--app-base-color);
            display: flex;
            align-items: center;
            border-radius: 8px;
            padding: 0px 8px;

            p {
                text-align: center;
            }
        }
    }
}
