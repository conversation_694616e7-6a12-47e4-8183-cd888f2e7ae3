import { CommonCallback } from './common';

/**
 * MyNumber008InitGetMcasModel
 */
export interface MyNumber008InitGetMcasModel extends CommonCallback {
    // セッションID
    SessionID: string;
}

/**
 * MyNumber008InitGetMcasResult
 */
export interface MyNumber008InitGetMcasResult extends CommonCallback {
    // 名前
    mynaCardInfoName?: string;
    // 住所
    mynaCardInfoAddress?: string;
    // 生年月日
    mynaCardInfoBirthday?: string;
    // 性別
    mynaCardInfoSex?: string;
    // mynaMailAddress
    mynaMailAddress?: string;
    // fromScreen
    fromScreen?: string;
    // セッションID
    mynaSessionID?: string;
    // ブランドID
    brandID?: string;
    // CheerID
    cheerID?: string;
    // OSフラグ
    osFlag?: string;
    // 検証結果
    mynaValidateResult?: number;
    // 失効理由コード
    mynaRevocationReasonCode?: number;
    // 失効理由メッセージ
    mynaRevocationReasonMessage?: string;
    // 処理結果
    mynaProcessResult?: number;
    // エンドユーザーID
    mynaEnduserID?: string;
}
