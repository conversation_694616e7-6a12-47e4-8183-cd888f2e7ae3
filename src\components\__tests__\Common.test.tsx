import { mount, shallow } from 'enzyme';
import { CommonError, CommonErrorProps, RequiredLabel, RequiredLabelProps } from '../Common';
import { Typography } from '@mui/material';
import Icons from '../Icons/Icons';

/**
 * Unit test for Common component
 */
describe('Unit test for Common component', () => {
    /**
     * Unit test for RequiredLabel component
     */
    describe('Unit test for RequiredLabel component', () => {
        /**
         * setup element
         * @param p RequiredLabelProps
         */
        const setup = (p?: RequiredLabelProps) => {
            const s = shallow(<RequiredLabel {...p} />);
            const m = mount(<RequiredLabel {...p} />);

            return {
                s: s,
                m: m,
                props: p,
            };
        };

        /**
         * should render with default props
         */
        it('should render with default props', () => {
            const { s } = setup();

            expect(s.find('div.common-label-container').length).toBe(1);
        });

        /**
         * should render with label prop inside
         */
        it('should display label prop inside', () => {
            const { s } = setup({
                label: 'this is label',
            });

            expect(s.find('div.common-label-container').length).toBe(1);

            expect(s.find(Typography).text()).toEqual('this is label');
        });

        /**
         * should display [必須] if required is true
         */
        it('should display [必須] if required is true', () => {
            const { s } = setup({
                label: 'this is label',
                required: true,
            });

            expect(s.find(Typography).length).toBe(2);

            expect(s.find(Typography).at(0).text()).toEqual('this is label');
            expect(s.find(Typography).at(1).text()).toEqual('必須');
        });

        /**
         * should display [任意] if optional is true
         */
        it('should display [任意] if required is true', () => {
            const { s } = setup({
                label: 'this is label',
                optional: true,
            });

            expect(s.find(Typography).length).toBe(2);

            expect(s.find(Typography).at(0).text()).toEqual('this is label');
            expect(s.find(Typography).at(1).text()).toEqual('任意');
        });
    });

    /**
     * Unit test for CommonError component
     */
    describe('Unit test for CommonError component', () => {
        /**
         * setup element
         * @param p CommonErrorProps
         */
        const setup = (p?: CommonErrorProps) => {
            const s = shallow(<CommonError {...p} />);
            const m = mount(<CommonError {...p} />);

            return {
                s: s,
                m: m,
                props: p,
            };
        };

        /**
         * should render with default props
         */
        it('should render with default props', () => {
            const { s } = setup();

            expect(s.find('div.common-component').length).toBe(1);
        });

        /**
         * should render with custom props
         */
        it('should render with custom props', () => {
            const { s } = setup({
                error: 'This is error',
                componentId: 'test-component-id',
            });

            expect(s.find('div#test-component-id-error')).toBeTruthy();
        });

        /**
         * should display error when error is passed in props
         */
        it('should display error and icon when error is passed in props', () => {
            const { s } = setup({
                error: 'This is error',
            });

            expect(s.find('div.common-component div.common-error-container').length).toBe(1);

            expect(s.find(Icons.UnreadNotificationIcon)).toBeTruthy();
            expect(s.find('div.common-error-container div.error').text()).toBe('This is error');
        });

        /**
         * should render custom error Icon size
         */
        it('should render custom error Icon size', () => {
            const { s } = setup({
                error: 'This is error',
                iconSize: {
                    w: 20,
                    h: 20,
                },
            });

            const icon = s.find(Icons.UnreadNotificationIcon);
            expect(icon).toBeTruthy();
            expect(icon.props().width).toBe(20);
            expect(icon.props().height).toBe(20);
        });
    });
});
