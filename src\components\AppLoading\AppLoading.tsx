import { Dialog } from '@mui/material';
import ReactLoading from 'react-loading';
import useCommon from '../../hooks/common';
import './styles.scss';
import { useMemo, memo } from 'react';

/**
 * AppLoading
 * @returns React.JSX.Element
 */
const AppLoading = (): React.JSX.Element => {
    const { lockCount = 0 } = useCommon();

    return useMemo(
        () => (
            <Dialog
                open={lockCount > 0}
                className="common-app-loading-component"
                disableScrollLock={false}
                disableAutoFocus={true}
                disableEnforceFocus={true}
            >
                <ReactLoading type="spokes" width={35} height={35} color="#fff" />
            </Dialog>
        ),
        [lockCount]
    );
};

export default memo(AppLoading);
