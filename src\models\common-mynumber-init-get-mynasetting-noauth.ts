import { CommonCallback } from './common';

/**
 * CommonMyNumberInitGetMynaSettingNoauthModel
 */
export interface CommonMyNumberInitGetMynaSettingNoauthModel extends CommonCallback {
    // ブランドID
    BrandID: string;
}

/**
 * CommonMyNumberInitGetMynaSettingNoauthResult
 */
export interface CommonMyNumberInitGetMynaSettingNoauthResult {
    // マイナ有効フラグ
    MynaEnabledFlag: boolean;
    // スタブ使用フラグ
    MynaStubUsedFlag: boolean;
}
