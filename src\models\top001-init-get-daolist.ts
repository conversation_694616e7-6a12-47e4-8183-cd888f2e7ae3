import { CommonCallback } from './common';

/**
 * Top001InitGetDaoListModel
 */
export interface Top001InitGetDaoListModel extends CommonCallback {
    // ブランドID
    BrandID: string;
    // 1ページあたりの取得アイテム数
    PageSize: number;
}

/**
 * Top001InitGetDaoItem
 */
export interface Top001InitGetDaoItem {
    // DAOID
    DaoID: string;
    // DAO名
    DaoName: string;
    // DAO画像
    DaoImage: string;
    // DAO概要
    OverView: string;
}

/**
 * Top001InitGetDaoListResult
 */
export interface Top001InitGetDaoListResult {
    // 地域コミュニティ一覧
    DaoList: Top001InitGetDaoItem[];
}
