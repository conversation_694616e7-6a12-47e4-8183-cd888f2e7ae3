import clsx from 'clsx';
import { useCallback, useEffect, useMemo } from 'react';
import { Mention, MentionsInput, OnChangeHandlerFunc, SuggestionDataItem } from 'react-mentions';
import './styles.scss';

/**
 * InputMentionProps
 */
interface InputMentionProps {
    value: string;
    onChange?: OnChangeHandlerFunc;
    data: SuggestionDataItem[];
    placeholder?: string;
    className?: string;
    inputRef?: React.RefObject<HTMLTextAreaElement>;
}

/**
 * InputMention
 * @param props InputMentionProps
 * @returns JSX.Element
 */
const InputMention = (props: InputMentionProps): JSX.Element => {
    const value = useMemo(() => props.value, [props.value]);
    const onChange = useMemo(() => props.onChange, [props.onChange]);
    const data = useMemo(() => props.data, [props.data]);
    const placeholder = useMemo(() => props?.placeholder, [props?.placeholder]);
    const className = useMemo(() => props?.className, [props?.className]);
    const inputRef = props?.inputRef;

    useEffect(() => {
        if (inputRef?.current?.value) {
            const val = inputRef?.current?.value;
            inputRef.current.value = '';
            inputRef.current.value = val;
        }
        inputRef?.current?.focus();
    }, [inputRef]);

    /**
     * displayTransform
     * @param _ string
     * @param display string
     */
    const displayTransform = useCallback((_: string, display: string) => `@${display}`, []);

    return useMemo(
        () => (
            <div className="comment-input-container">
                <MentionsInput
                    value={value}
                    inputRef={inputRef}
                    onChange={onChange}
                    className={clsx('mentions', className)}
                    allowSuggestionsAboveCursor={true}
                    maxLength={50000}
                    placeholder={placeholder || ''}
                >
                    <Mention data={data} trigger="@" displayTransform={displayTransform} appendSpaceOnAdd />
                </MentionsInput>
            </div>
        ),
        [className, data, displayTransform, inputRef, onChange, placeholder, value]
    );
};

export default InputMention;
