import API from '../constants/api';
import { BaseResponse } from '../models/common';
import { MainExecuteChargeModel } from '../models/menu001-main-revoke-token';
import createAPI from './baseApi';

/**
 * MenuAPI
 */
class MenuAPI {
    /**
     * menu001MainRevokeToken
     * @param data MainExecuteChargeModel
     * @returns Promise<BaseResponse>
     */
    static menu001MainRevokeToken = (data: MainExecuteChargeModel): Promise<BaseResponse> => {
        return createAPI({
            url: API.MENU001_MAIN_REVOKE_TOKEN,
            data: data,
        });
    };
}

export default MenuAPI;
