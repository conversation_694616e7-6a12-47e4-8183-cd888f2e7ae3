import { memo, useCallback, useMemo, useState } from 'react';
import { CURRENT_LANGUAGE } from '../../constants/variables';
import useCommon from '../../hooks/common';
import Utils from '../../utils/utils';
import Dropdown from '../Dropdown';
import Icons from '../Icons';
import './styles.scss';

export type LanguageType = {
    id: string;
    language: string;
};

// dropDownTranslateData
const dropDownTranslateData: LanguageType[] = [
    {
        id: 'jp',
        language: '日本語',
    },
    {
        id: 'en',
        language: 'English',
    },
];

export const defaultLanguage = dropDownTranslateData[0].id;

/**
 * LanguageDropdown
 * @returns React.JSX.Element
 */
export const LanguageDropdown = (): React.JSX.Element => {
    const [lang, setLang] = useState(() => Utils.getCookie(CURRENT_LANGUAGE) || defaultLanguage);
    const { updateLanguage } = useCommon();
    /**
     * handleChangeTranslatePullDown
     * @param event: React.ChangeEvent<HTMLInputElement>
     */
    const handleChangeTranslatePullDown = useCallback(
        (event: React.ChangeEvent<HTMLInputElement>) => {
            const value = event.target.value;

            setLang(value);
            Utils.setCookie(CURRENT_LANGUAGE, value, 365);
            updateLanguage(value);
        },
        [updateLanguage]
    );

    return useMemo(
        () => (
            <div className="dropdown-language">
                <Dropdown
                    value={lang}
                    data={dropDownTranslateData}
                    keyValue="id"
                    keyDisplay="language"
                    name="translatePullDown"
                    onChange={handleChangeTranslatePullDown}
                    categoryOption={<>Language</>}
                    iconOption={<Icons.ArrowRightIcon color="var(--app-base-color)" />}
                />
            </div>
        ),
        [handleChangeTranslatePullDown, lang]
    );
};

export default memo(LanguageDropdown);
