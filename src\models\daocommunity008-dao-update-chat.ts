import { CommonCallback } from './common';

/**
 * DAOCommunity008DaoUpdateChatModel
 */
export interface DAOCommunity008DaoUpdateChatModel extends CommonCallback {
    // DaoID
    DaoID: string;
    // チャネルID
    ChannelID: string;
    // スレッドID
    ThreadID: string;
    // チャットID
    ChatID: string;
    // スレッド種別
    ThreadType: number;
    // 本文タイトル
    Title: string;
    // 投稿内容
    Content: string;
}

/**
 * DAOCommunity008DaoUpdateChatMentionItem
 */
export interface DAOCommunity008DaoUpdateChatMentionItem {
    // 応援者ID
    CheerID: string;
    // ニックネーム
    NickName: string;
    // メッセージ
    Message: string;
    // プロフィール画像ファイル名
    ProfileImageFileName: string;
}

/**
 * DAOCommunity008DaoUpdateChatResult
 */
export interface DAOCommunity008DaoUpdateChatResult {
    // 投稿内容
    Content: string;
    // 編集フラグ
    EditFlag: number;
    // メンションリスト
    MentionList: DAOCommunity008DaoUpdateChatMentionItem[];
}
