import { CommonCallback } from './common';

/**
 * DAOCommunity002DaoGetCategoryChannelModel
 */
export interface DAOCommunity002DaoGetCategoryChannelModel extends CommonCallback {
    // DAOID
    DaoID: string;
}

/**
 * DAOCommunity002ChannelListItem
 */
export interface DAOCommunity002ChannelListItem {
    // チャネルID
    ChannelID: string;
    // チャネル名
    ChannelName: string;
    // カテゴリーID
    CategoryID: string;
    // ソート番号
    SortNo: number;
}

/**
 * DAOCommunity002DaoGetCategoryChannelItem
 */
export interface DAOCommunity002DaoGetCategoryChannelItem {
    // 投票テーマID
    CategoryID: string;
    // カテゴリー名
    CategoryName: string;
    // ソート番号
    SortNo: number;
    // チャネルリスト
    ChannelList: DAOCommunity002ChannelListItem[];
    // カテゴリ画像
    CategoryImageFileName?: string;
}

/**
 * DAOCommunity002DaoGetCategoryChannelResult
 */
export interface DAOCommunity002DaoGetCategoryChannelResult {
    // カテゴリーリスト
    CategoryList: DAOCommunity002DaoGetCategoryChannelItem[];
}
