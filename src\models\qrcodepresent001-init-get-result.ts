import { CommonCallback } from './common';

/**
 * QrCodePresent001InitGetResultModel
 */
export interface QrCodePresent001InitGetResultModel extends CommonCallback {
    // QR認証コード
    QRAuthCode: number;
    // QRコード発行日時
    QRCodePublishDateTime: string;
}

/**
 * QrCodePresent001InitGetResult
 */
export interface QrCodePresent001InitGetResult {
    // 処理結果種別（8:送金（チャージ）, 10:支払い）
    ResultType: number;
    // 決済金額
    HistoryCheerAmount: number;
    // 決済店舗名
    StoreName: string;
    // 有効期限
    ExpirationDate?: string;
    // ポイントデータリスト
    PointResult: PointResultItem[];
}
/**
 * PointResultItem
 */
export interface PointResultItem {
    // ポイントバック金額
    PointHistoryCheer?: number;
    // ポイントバック対象メダル名
    PointMedalName?: string;
}
