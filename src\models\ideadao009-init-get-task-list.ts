import { CommonCallback } from './common';

/**
 * IdeaDao009InitGetTaskListModel
 */
export interface IdeaDao009InitGetTaskListModel extends CommonCallback {
    // DAOID
    DaoID: string;
    // ページサイズ
    PageSize: number;
    // 排他開始キー
    ExclusiveStartKey?: string;
}
/**
 * IdeaDaoTaskItem
 */
export type IdeaDaoTaskItem = {
    // タスクID
    TaskID: string;
    // ステータス
    Status: number;
    // タスク区分
    TaskClass: number;
    // 作成日時
    CreateDateTime: string;
    // 更新日時
    UpdateDateTime: string;
    // タスク名称
    Name: string;
    // 担当者画像
    ProfileImageFileName: string;
    // 担当者
    NickName: string;
    // 報酬
    RewardAmount: number;
};
/**
 * IdeaDao009InitGetTaskListResult
 */
export interface IdeaDao009InitGetTaskListResult {
    // DAO名
    DaoName: string;
    // タスク一覧
    TaskList: IdeaDaoTaskItem[];
    // LastEvaluatedKey
    LastEvaluatedKey?: string;
}
