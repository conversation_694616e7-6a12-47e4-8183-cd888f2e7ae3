import API from '../constants/api';
import { BaseResponse } from '../models/common';
import {
    Ticket001InitGetEventTicketListModel,
    Ticket001InitGetEventTicketListResult,
} from '../models/ticket001-init-get-event-ticket-list';

import {
    Ticket002InitGetEventTicketDetailResult,
    Ticket002InitGetEventTicketDetailModel,
} from '../models/ticket002-init-get-event-ticket-detail';
import { Ticket003MainUseEventTicketModel } from '../models/ticket003-main-use-event-ticket';

import createAPI from './baseApi';

/**
 * TicketAPI
 */
class TicketAPI {
    /**
     * ticket001InitGetEventTicketList
     * @param data Ticket001InitGetEventTicketListModel
     * @returns Promise<BaseResponse<Ticket001InitGetEventTicketListResult>>
     */
    static ticket001InitGetEventTicketList = (
        data: Ticket001InitGetEventTicketListModel
    ): Promise<BaseResponse<Ticket001InitGetEventTicketListResult>> => {
        return createAPI({
            url: API.TICKET001_INIT_GET_EVENT_TICKET_LIST,
            data,
        });
    };

    /**
     * ticket002InitGetEventTicketDetail
     * @param data Ticket002InitGetEventTicketDetailModel
     * @returns Promise<BaseResponse<Ticket002InitGetEventTicketDetailResult>>
     */
    static ticket002InitGetEventTicketDetail = (
        data: Ticket002InitGetEventTicketDetailModel
    ): Promise<BaseResponse<Ticket002InitGetEventTicketDetailResult>> => {
        return createAPI<Ticket002InitGetEventTicketDetailResult>({
            url: API.TICKET002_INIT_GET_EVENT_TICKET_DETAIL,
            data: data,
        });
    };

    /**
     * ticket003MainUseEventTicket
     * @param data Ticket003MainUseEventTicketModel
     * @returns Promise<BaseResponse>
     */
    static ticket003MainUseEventTicket = (data: Ticket003MainUseEventTicketModel): Promise<BaseResponse> => {
        return createAPI({
            url: API.TICKET003_MAIN_USE_EVENT_TICKET,
            data: data,
        });
    };
}

export default TicketAPI;
