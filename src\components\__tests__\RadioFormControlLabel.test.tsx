/* eslint-disable @typescript-eslint/explicit-function-return-type */
import { RadioGroup } from '@mui/material';
import { mount, shallow } from 'enzyme';
import RadioFormControlLabel, { RadioFormControlType } from '../RadioFormControlLabel/RadioFormControlLabel';

/**
 * Unit test for RadioFormControlLabel component
 */
describe('Unit test for RadioFormControlLabel component', () => {
    /**
     * setup element
     * @param props1 RadioFormControlType
     * @param props2 RadioFormControlType
     */
    const setup = (props1: RadioFormControlType, props2: RadioFormControlType) => {
        const s = shallow(
            <RadioGroup name="gender">
                <RadioFormControlLabel {...props1} />
                <RadioFormControlLabel {...props2} />
            </RadioGroup>
        );
        const m = mount(
            <RadioGroup name="gender">
                <RadioFormControlLabel {...props1} />
                <RadioFormControlLabel {...props2} />
            </RadioGroup>
        );

        return {
            shallow: s,
            mount: m,
        };
    };

    /**
     * display enough elements
     */
    describe('display enough elements', () => {
        const { mount } = setup(
            {
                value: 'male',
                label: 'male label',
                name: 'gender',
                className: 'RadioFormControlLabel-className1',
                color: '#f00',
            },
            {
                value: 'female',
                label: 'female label',
                name: 'gender',
                className: 'RadioFormControlLabel-className2',
                color: '#f00',
            }
        );

        /**
         * require value
         */
        it('require value', () => {
            expect(mount.find(RadioFormControlLabel).at(0).props().value).not.toBeUndefined();
            expect(mount.find(RadioFormControlLabel).at(1).props().value).not.toBeUndefined();
        });

        /**
         * add value
         */
        it('add value', () => {
            expect(mount.find(RadioFormControlLabel).at(0).props().value).toEqual('male');
            expect(mount.find(RadioFormControlLabel).at(1).props().value).toEqual('female');
        });

        /**
         * require label
         */
        it('require label', () => {
            expect(mount.find(RadioFormControlLabel).at(0).props().label).not.toBeUndefined();
            expect(mount.find(RadioFormControlLabel).at(1).props().label).not.toBeUndefined();
        });

        /**
         * add label
         */
        it('add label', () => {
            expect(mount.find(RadioFormControlLabel).at(0).props().label).toEqual('male label');
            expect(mount.find(RadioFormControlLabel).at(1).props().label).toEqual('female label');
        });

        /**
         * add name
         */
        it('add name', () => {
            expect(mount.find(RadioFormControlLabel).at(0).props().name).toEqual('gender');
            expect(mount.find(RadioFormControlLabel).at(1).props().name).toEqual('gender');
        });

        /**
         * add className
         */
        it('add className', () => {
            expect(mount.find(RadioFormControlLabel).at(0).props().className).toEqual(
                'RadioFormControlLabel-className1'
            );
            expect(mount.find(RadioFormControlLabel).at(1).props().className).toEqual(
                'RadioFormControlLabel-className2'
            );
        });

        /**
         * check color
         */
        it('check color', () => {
            expect(mount.find(RadioFormControlLabel).at(0).props().color).toEqual('#f00');
            expect(mount.find(RadioFormControlLabel).at(1).props().color).toEqual('#f00');
        });
    });

    /**
     * Actions
     */
    describe('Actions', () => {
        const { mount } = setup(
            {
                value: 'male',
                label: 'male label',
                name: 'gender',
                className: 'RadioFormControlLabel-className1',
            },
            {
                value: 'female',
                label: 'female label',
                name: 'gender',
                className: 'RadioFormControlLabel-className2',
            }
        );

        /**
         * check action on click
         */
        it('check action on click', () => {
            const option1 = mount.find(RadioFormControlLabel).at(0);
            const option2 = mount.find(RadioFormControlLabel).at(1);

            expect(option1.find('span.Mui-checked').length).toEqual(0);
            expect(option2.find('span.Mui-checked').length).toEqual(0);

            option1.simulate('click');
            expect(option1.find('span.MuiRadio-root').hasClass('Mui-checked'));
            expect(option2.find('span.Mui-checked').length).toEqual(0);

            option2.simulate('click');
            expect(option1.find('span.Mui-checked').length).toEqual(0);
            expect(option2.find('span.MuiRadio-root').hasClass('Mui-checked'));
        });
    });
});
