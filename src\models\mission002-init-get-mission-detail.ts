import { CommonCallback } from './common';

/**
 * Mission002InitGetMissionDetailModel
 */
export interface Mission002InitGetMissionDetailModel extends CommonCallback {
    // ブランドID
    BrandID: string;
    // ユーザーID
    CheerID?: string;
    // ミッションID
    MissionID: string;
}

/**
 * MissionDetailItem
 */
export interface MissionDetailItem {
    // ミッション詳細ID
    MissionDetailID: string;
    // 報酬ID
    RewardID: string;
    // タイトル（子ミッション）
    MissionDetailName: string;
    // ミッション詳細対象範囲
    MissionDetailImage?: string;
    // ミッション詳細順路
    Address: string;
    // 条件達成区分
    MissionDetailCategory: number;
    // ミッション詳細順路
    MissionDetailRoute: number;
    // 達成フラグ
    CompleteStatus?: boolean;
    // 達成日
    CompleteDateTime?: string;
    // ミッション条件区分
    ConditionType: string;
}

/**
 * MissionStatusItem
 */
export interface MissionStatusItem {
    // ステータス画
    MissionStatusName: string;
    // ステータス画像
    MissionStatusImage: string;
    // クリアミッション数
    ClearMissionDetailCount: number;
}

/**
 * Mission002InitGetMissionDetailResult
 */
export interface Mission002InitGetMissionDetailResult {
    // 報酬ID
    RewardID: string;
    // タイトル（親ミッション）
    MissionName: string;
    // ミッション説明文
    MissionDescription: string;
    // 終了日時
    EndDateTime: string;
    // 順序制約フラグ
    OrderFlag: number;
    // ステータス画
    MissionStatusName: string;
    // ステータス画像
    MissionStatusImage: string;
    // 達成報酬種別
    IncentiveType?: number;
    // 達成報酬名
    IncentiveName?: string;
    // 達成報酬画像
    IncentiveImage?: string;
    // 達成日
    CompleteDateTime?: string;
    // 達成済みフラグ
    CompleteStatus?: boolean;
    // クリアミッション数
    ClearMissionCount: number;
    // 報酬取得ID
    GetRewardID?: string;
    // 報酬取得日
    GetRewardDateTime?: string;

    MissionStatusList: MissionStatusItem[];

    MissionDetailList: MissionDetailItem[];
}
