import API from '../constants/api';
import { BaseResponse } from '../models/common';
import { StampRally006MainSearchStampRallyModel } from '../models/stamprally006-main-search-stamprally';
import createAPI from './baseApi';
import {
    StampRally004InitGetStampRallyMissionListModel,
    StampRally004InitGetStampRallyMissionListResult,
} from '../models/stamprally004-init-get-stamprally-mission-list';
import { StampRally005InitUpdateStampRallyModel } from '../models/stamprally005-init-update-stamprally';
import { Mission003MainUpdateMissionModel } from '../models/mission003-main-update-mission';

/**
 * StampRallyAPI
 */
class StampRallyAPI {
    /**
     * stampRally006MainSearchStampRally
     * @param data StampRally006MainSearchStampRallyModel
     * @returns Promise<BaseResponse>
     */
    static stampRally006MainSearchStampRally = (
        data: StampRally006MainSearchStampRallyModel
    ): Promise<BaseResponse> => {
        return createAPI({
            url: API.STAMPRALLY006_MAIN_SEARCH_STAMPRALLY,
            data,
        });
    };

    /**
     * stampRally004InitGetStampRallyMissionList
     * @param data StampRally004InitGetStampRallyMissionListModel
     * @returns Promise<BaseResponse<StampRally004InitGetStampRallyMissionListResult>>
     */
    static stampRally004InitGetStampRallyMissionList = (
        data: StampRally004InitGetStampRallyMissionListModel
    ): Promise<BaseResponse<StampRally004InitGetStampRallyMissionListResult>> => {
        return createAPI<StampRally004InitGetStampRallyMissionListResult>({
            url: API.STAMPRALLY004_INIT_GET_STAMPRALLY_MISSION_LIST,
            data,
        });
    };

    /**
     * stampRally005InitUpdateStampRally
     * @param data StampRally005InitUpdateStampRallyModel
     * @returns Promise<BaseResponse>
     */
    static stampRally005InitUpdateStampRally = (
        data: StampRally005InitUpdateStampRallyModel
    ): Promise<BaseResponse> => {
        return createAPI({
            url: API.STAMPRALLY005_INIT_UPDATE_STAMPRALLY,
            data,
        });
    };

    /**
     * mission003MainUpdateMission
     * @param data Mission003MainUpdateMissionModel
     * @returns Promise<BaseResponse>
     */
    static mission003MainUpdateMission = (data: Mission003MainUpdateMissionModel): Promise<BaseResponse> => {
        return createAPI({
            url: API.MISSION003_MAIN_UPDATE_MISSION,
            data,
        });
    };
}

export default StampRallyAPI;
