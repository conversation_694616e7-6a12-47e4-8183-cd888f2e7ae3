import API from '../constants/api';
import { BaseResponse } from '../models/common';
import {
    Paymulti001MainSearchStorePublicCodeModel,
    Paymulti001MainSearchStorePublicCodeResult,
    Paymulti002MainSearchStorePublicCodeModel,
    Paymulti002MainSearchStorePublicCodeResult,
} from '../models/main-search-storepubliccode';

import {
    Paymulti003InitGetAvailableCoinIdModel,
    Paymulti003InitGetAvailableCoinIdResult,
} from '../models/paymulti003-init-get-availablecoinid';
import {
    PayMulti005MainExecutePaymentModel,
    PayMulti005MainExecutePaymentResult,
} from '../models/paymulti005-main-execute-payment';
import createAPI from './baseApi';

/**
 * PaymentAPI
 */
class PaymentAPI {
    /**
     * paymulti001MainSearchStorePublicCode
     * @param data Paymulti001MainSearchStorePublicCodeModel
     * @returns Promise<BaseResponse<Paymulti001MainSearchStorePublicCodeResult>>
     */
    static paymulti001MainSearchStorePublicCode = (
        data: Paymulti001MainSearchStorePublicCodeModel
    ): Promise<BaseResponse<Paymulti001MainSearchStorePublicCodeResult>> => {
        return createAPI<Paymulti001MainSearchStorePublicCodeResult>({
            url: API.PAYMULTI001_MAIN_SEARCH_STOREPUBLICCODE,
            data,
        });
    };

    /**
     * paymulti002MainSearchStorePublicCode
     * @param data Paymulti002MainSearchStorePublicCodeModel
     * @returns Promise<BaseResponse<Paymulti002MainSearchStorePublicCodeResult>>
     */
    static paymulti002MainSearchStorePublicCode = (
        data: Paymulti002MainSearchStorePublicCodeModel
    ): Promise<BaseResponse<Paymulti002MainSearchStorePublicCodeResult>> => {
        return createAPI<Paymulti002MainSearchStorePublicCodeResult>({
            url: API.PAYMULTI002_MAIN_SEARCH_STOREPUBLICCODE,
            data,
        });
    };

    /**
     * paymulti003InitGetAvailableCoinId
     * @param data Paymulti003InitGetAvailableCoinIdModel
     * @returns Promise<BaseResponse<Paymulti003InitGetAvailableCoinIdResult>>
     */
    static paymulti003InitGetAvailableCoinId = (
        data: Paymulti003InitGetAvailableCoinIdModel
    ): Promise<BaseResponse<Paymulti003InitGetAvailableCoinIdResult>> => {
        return createAPI<Paymulti003InitGetAvailableCoinIdResult>({
            url: API.PAYMULTI003_INIT_GET_AVAILABLECOINID,
            data,
        });
    };

    /**
     * paymulti005MainExecutePayment
     * @param data PayMulti005MainExecutePaymentModel
     * @returns Promise<BaseResponse<PayMulti005MainExecutePaymentResult>>
     */
    static paymulti005MainExecutePayment = (
        data: PayMulti005MainExecutePaymentModel
    ): Promise<BaseResponse<PayMulti005MainExecutePaymentResult>> => {
        return createAPI({
            url: API.PAYMULTI005_MAIN_EXECUTE_PAYMENT,
            data,
        });
    };
}
export default PaymentAPI;
