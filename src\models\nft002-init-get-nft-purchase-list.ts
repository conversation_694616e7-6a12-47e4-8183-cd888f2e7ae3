import { CommonCallback } from './common';

/**
 * Nft002InitGetNftPurchaseListModel
 */
export type Nft002InitGetNftPurchaseListModel = CommonCallback & {
    // 前の画面で選択されたNFTCategoryID
    NFTCategoryID: string;
    // sortID
    SortID: number;
    // ExclusiveStartKey
    ExclusiveStartKey?: string;
    BrandID?: string;
    CheerID?: string;
};

/**
 * Nft002InitGetNftPurchaseListItem
 */
export interface Nft002InitGetNftPurchaseListItem {
    // NFTコンテンツID
    NFTContentID: string;
    // コンテンツ画像
    ContentImage: string;
    // 売上金額
    TotalPrice: number;
    // いいね数
    LikeCount: number;
    // いいね押下フラグ
    LikeFlg: boolean;
    // NFT所持フラグ
    NFTPossessionFlg: boolean;
}
/**
 * Nft002InitGetNftPurchaseListResult
 */
export interface Nft002InitGetNftPurchaseListResult {
    NFTContentList: Nft002InitGetNftPurchaseListItem[];
    LastEvaluatedKey: string;
}
