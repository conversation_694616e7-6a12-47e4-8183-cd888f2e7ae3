import ErrorOutlineOutlinedIcon from '@mui/icons-material/ErrorOutlineOutlined';
import InfoOutlinedIcon from '@mui/icons-material/InfoOutlined';
import WarningAmberOutlinedIcon from '@mui/icons-material/WarningAmberOutlined';
import { mount, shallow } from 'enzyme';
import { Provider } from 'react-redux';
import { CommonMessageDialog } from '../../models/common';
import CommonUtils from '../../utils/common';
import TestUtils from '../../utils/test-utils';
import Button from '../Button/Button';
import MessageDialog, { MessageDialogItem } from '../MessageDialog/MessageDialog';

/**
 * Unit test for MessageDialogItem
 */
describe('Unit test for MessageDialogItem', () => {
    /**
     * setup element
     */
    const setup = (p: CommonMessageDialog): React.JSX.Element => {
        return <MessageDialogItem {...p} />;
    };

    /**
     * should render correctly
     */
    it('should render correctly', () => {
        const wrapper = shallow(
            setup({
                id: 'uuid_test_1',
                message: 'message_test',
            })
        );

        expect(wrapper).toBeTruthy();
    });

    /**
     * should render with information icon
     */
    it('should render with information icon', () => {
        const wrapper = shallow(
            setup({
                id: 'uuid_test_1',
                message: 'message_test',
                type: 'INFO',
            })
        );

        expect(wrapper).toBeTruthy();
        expect(wrapper.find(InfoOutlinedIcon)).toBeTruthy();
    });

    /**
     * should render with warning icon
     */
    it('should render with warning icon', () => {
        const wrapper = shallow(
            setup({
                id: 'uuid_test_1',
                message: 'message_test',
                type: 'WARNING',
            })
        );

        expect(wrapper).toBeTruthy();
        expect(wrapper.find(WarningAmberOutlinedIcon)).toBeTruthy();
    });

    /**
     * should render with error icon
     */
    it('should render with error icon', () => {
        const wrapper = shallow(
            setup({
                id: 'uuid_test_1',
                message: 'message_test',
                type: 'ERROR',
            })
        );

        expect(wrapper).toBeTruthy();
        expect(wrapper.find(ErrorOutlineOutlinedIcon)).toBeTruthy();
    });

    /**
     * should display message correctly
     */
    it('should display message correctly', () => {
        const wrapper = shallow(
            setup({
                id: 'uuid_test_1',
                message: 'message_test',
                type: 'ERROR',
            })
        );

        expect(
            wrapper.find(
                <div className="d-flex justify-content-center text-center p-16 fs-16 ff-noto-bold">message_test</div>
            )
        ).toBeTruthy();
    });

    /**
     * should display [とじる] button
     */
    it('should display [とじる] button', () => {
        const wrapper = shallow(
            setup({
                id: 'uuid_test_1',
                message: 'message_test',
                type: 'ERROR',
            })
        );

        expect(wrapper.find(Button)).toBeTruthy();
    });

    /**
     * should close dialog when click on [とじる] button
     */
    it('should close dialog when click on [とじる] button', () => {
        const wrapper = shallow(
            setup({
                id: 'uuid_test_1',
                message: 'message_test',
                type: 'ERROR',
            })
        );

        const button = wrapper.find(Button);

        button.simulate('click');

        const spy = jest.spyOn(CommonUtils, 'closeMessageById');

        CommonUtils.closeMessageById('uuid_test_1');

        expect(spy).toHaveBeenCalledWith('uuid_test_1');
    });

    /**
     * should close dialog and dispatch onClose when click on [とじる] button
     */
    it('should close dialog and dispatch onClose when click on [とじる] button', () => {
        const onClose = jest.fn();
        const wrapper = shallow(
            setup({
                id: 'uuid_test_1',
                message: 'message_test',
                type: 'ERROR',
                onClose,
            })
        );

        const button = wrapper.find(Button);

        button.simulate('click');

        const spy = jest.spyOn(CommonUtils, 'closeMessageById');

        CommonUtils.closeMessageById('uuid_test_1');

        expect(onClose).toHaveBeenCalled();
        expect(spy).toHaveBeenCalledWith('uuid_test_1');
    });
});

/**
 * Unit test for MessageDialog component
 */
describe('Unit test for MessageDialog component', () => {
    /**
     * should display MessageDialog when messageDialogs available
     */
    it('should display MessageDialog when messageDialogs length > 0', () => {
        const prestate = TestUtils.setupStore({
            commonReducer: {
                lockCount: 0,
                messageDialogs: [
                    {
                        message: 'message',
                        id: 'uuid',
                    },
                ],
            },
        });

        const wrapper = mount(
            <Provider store={prestate}>
                <MessageDialog />
            </Provider>
        );

        expect(wrapper.find(MessageDialogItem).length).toBe(1);
    });

    /**
     * should not render when messageDialogs length = 0
     */
    it('should not render when messageDialogs length = 0', () => {
        const prestate = TestUtils.setupStore({
            commonReducer: {
                lockCount: 0,
            },
        });
        const wrapper = mount(
            <Provider store={prestate}>
                <MessageDialog />
            </Provider>
        );
        expect(wrapper.find(MessageDialogItem).length).toBe(0);
    });
});
