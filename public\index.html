<!DOCTYPE html>
<html lang="ja">
    <head>
        <!-- Google tag (gtag.js) -->
        <script async src="https://www.googletagmanager.com/gtag/js?id=G-0KKHF4X4T5"></script>
        <script>
            window.dataLayer = window.dataLayer || [];
            function gtag() {
                dataLayer.push(arguments);
            }
            gtag('js', new Date());

            gtag('config', 'G-0KKHF4X4T5');
        </script>
        <meta charset="utf-8" />
        <link rel="icon" sizes="16x16" href="%PUBLIC_URL%/icon_16.ico" />
        <link rel="icon" sizes="24x24" href="%PUBLIC_URL%/icon_24.ico" />
        <link rel="icon" sizes="32x32" href="%PUBLIC_URL%/icon_32.ico" />
        <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1" />
        <meta name="theme-color" content="#000000" />
        <meta name="description" content="Cheer Template website." />
        <link rel="apple-touch-icon" href="%PUBLIC_URL%/logo192.png" />
        <link rel="manifest" href="%PUBLIC_URL%/manifest.json" />
        <title>Cheer Template</title>
        <style>
            lui-alert {
                display: none;
            }
        </style>

        <script src="https://maps.googleapis.com/maps/api/js?key=AIzaSyBUduV9-oouhm9vnpQkvoUM-oUEGB9AXJs&libraries=places"></script>
    </head>

    <body>
        <noscript>You need to enable JavaScript to run this app.</noscript>
        <div id="ApplicationRoot"></div>
    </body>
</html>
