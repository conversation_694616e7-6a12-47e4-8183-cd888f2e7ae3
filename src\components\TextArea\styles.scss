.common-textarea-component {
    user-select: none;
    -moz-user-select: none;
    -webkit-user-select: none;
    -ms-user-select: none;

    .label {
        font-size: 16px;
        font-family: 'Noto Sans JP Bold', Meiryo, メイリオ, Arial, Helvetica, sans-serif;
        color: #222222;
    }

    .sub-label {
        margin-top: 4px;
        font-size: 14px;
        color: #222222;
    }

    .required {
        margin-left: 4px;
        &-container {
            border-width: 1px;
            border-color: #d31e2d;
            border-style: solid;
            padding-left: 4px;
            padding-right: 4px;
            height: 20px;
        }
        .label {
            font-size: 11px;
            font-family: 'Noto Sans JP Bold', Meiryo, メイリオ, Arial, Helvetica, sans-serif;
            color: #d31e2d;
        }
    }
    .optional {
        margin-left: 4px;
        &-container {
            background-color: #efefef;
            padding-left: 4px;
            padding-right: 4px;
            height: 20px;
        }
        .label {
            font-size: 11px;
            font-family: 'Noto Sans JP Bold', <PERSON><PERSON>, メイリオ, <PERSON><PERSON>, Helvetica, sans-serif;
            color: #666666;
        }
    }
    .main-textarea-container {
        margin-top: 8px;
    }
    .main-textarea {
        flex: 1;
        border: 1px solid #bfbfbf;
        border-radius: 4px;
        overflow: hidden !important;
        position: relative;

        &:has(textarea:hover) {
            border: 1px solid var(--app-base-color);
        }
        &:has(textarea:focus) {
            border: 1px solid var(--app-base-color);
        }
        &.error {
            border-color: #d31e2d !important;
        }
        textarea {
            width: 100%;
            font-size: 14px;
            color: #222222;
            min-height: 40px;
            padding-top: 8px;
            padding-left: 10px !important;
            padding-right: 10px !important;
            height: 100% !important;
            border: none !important;
            &:hover {
                border: none !important;
                outline: none !important;
            }
            &:focus {
                border: none !important;
                outline: none !important;
            }
            &:active {
                border: none !important;
                outline: none !important;
            }
            &::placeholder {
                color: #707070;
                opacity: 1;
            }

            &:-ms-input-placeholder {
                color: #707070;
            }

            &::-ms-input-placeholder {
                color: #707070;
            }
        }
    }

    .error-container {
        min-height: 16px;
        .error {
            font-size: 10px;
            font-family: 'Noto Sans JP', Meiryo, メイリオ, Arial, Helvetica, sans-serif;
            color: #d31e2d;
        }
    }

    .highlighter {
        position: absolute;
        top: 0;
        box-sizing: border-box;
        width: 100%;
        color: transparent;
        overflow: hidden;
        white-space: pre-wrap;
        overflow-wrap: break-word;
        text-align: start;
        z-index: -1000;
    }

}
