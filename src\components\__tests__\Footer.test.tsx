import { Paper, Typography } from '@mui/material';
import { mount, shallow } from 'enzyme';
import Footer, { FooterItem, FooterItemProps } from '../Footer/Footer';
import Icons from '../Icons/Icons';
import { Provider } from 'react-redux';
import store from '../../redux';
import WalletAPI from '../../apis/wallet';
import React from 'react';
import { act } from 'react-dom/test-utils';
import CommonUtils from '../../utils/common';
import { waitFor } from '@testing-library/react';

// mock useNavigate function
const mockedUseNavigate = jest.fn();

const dataInitGetCoin = [
    {
        MedalServiceID: 'mdl0000000000000007',
        MedalServiceType: 4,
        MedalServiceName: 'NFT',
        MedalServiceImage:
            'https://8ccx1u17n9.execute-api.ap-northeast-1.amazonaws.com/0000000-CheerAPI/0000000-cheermedalbucket-dev/Brand/0000000/Icon/NFTコイン.png',
        MedalServiceStartDate: '2026/01/01 00:00:00.000',
        MedalServiceEndDate: '2025/01/01 00:00:00.000',
        PaymentStartDate: '2023/01/01 00:00:00.000',
        PaymentEndDate: '2025/01/01 00:00:00.000',
        ChargeStartDate: '2023/01/01 00:00:00.000',
        ChargeEndDate: '2025/01/01 00:00:00.000',
        PaymentButtonEnableFlag: true,
        ChargeButtonEnableFlag: true,
        MedalServiceBalance: 50000,
        MedalServiceExpirationFlg: false,
        MedalGroupDetails: {
            mdg0000000000000007: {
                ProductGroupName: '',
                StoreGroupName: '',
                UsePriority: 1,
                MedalNameList: ['Premium7', 'Coin7', 'Nft'],
            },
        },
        MedalChargeMethod: ['CreditCard'],
        MedalServiceEnableFlag: true,
    },
];

/**
 * mock for react-router-dom lib
 */
jest.mock('react-router-dom', () => ({
    ...jest.requireActual('react-router-dom'),
    useSearchParams: () => null,
    useLoaderData: () => null,
    useNavigate: () => mockedUseNavigate,
    useLocation: () => ({
        pathname: '/',
    }),
}));

// mock useSelector
const mockUseSelector = jest.fn();
const mockedUseDispatch = jest.fn();
/**
 * mock for react-redux lib
 */
jest.mock('react-redux', () => ({
    ...jest.requireActual('react-redux'),
    useDispatch: () => mockedUseDispatch,
    useSelector: () => mockUseSelector,
}));

/**
 * Unit test for Footer component
 */
describe('Unit test for Footer component', () => {
    beforeEach(() => {
        jest.restoreAllMocks();
        jest.resetAllMocks();

        mockedUseNavigate.mockReset();
    });
    /**
     * setup element
     */
    const setup = (): React.JSX.Element => {
        return <Footer />;
    };

    /**
     * should transition to PAYMENT_HOME when on click item [支払う]
     */
     it('should transition to PAYMENT_HOME when on click item [支払う]', () => {
        // localStorage.setItem('idToken', 'token');
        const wrapper = shallow(setup());

        const item = wrapper.find('div.footer-item-center-container');

        act(() => {
            item.simulate('click');
        })

        expect(mockedUseNavigate).toHaveBeenCalled();
        // expect(wrapper).toBeTruthy();
    });

    /**
     * should display correctly with no props
     */
    it('should display correctly with no props', () => {
        const useEffect = jest.spyOn(React, 'useEffect').mockImplementationOnce((f) => f());
        localStorage.setItem('idToken', 'token');
        jest.spyOn(WalletAPI, 'wallet001InitGetCoin').mockResolvedValueOnce({
            isSuccess: true,
            status: 0,
            result: dataInitGetCoin as any,
        });
        const wrapper = shallow(setup());

        expect(useEffect).toHaveBeenCalled();
        expect(wrapper.find(Paper)).toBeTruthy();
    });

    /**
     * should display enough items: [ウォレット], [さがす], [コレクション], [コミュニティ]
     */
    it('should display enough items: [ウォレット], [さがす], [コレクション], [コミュニティ]', () => {
        localStorage.setItem('idToken', 'token');
        const wrapper = shallow(setup());

        const items = wrapper.find(FooterItem);
        expect(items.at(0).props().label).toBe('ウォレット');
        expect(items.at(1).props().label).toBe('さがす');
        expect(items.at(2).props().label).toBe('コレクション');
        expect(items.at(3).props().label).toBe('コミュニティ');
    });

    /**
     * should display icons
     */
    it('should display icons', () => {
        localStorage.setItem('idToken', 'token');
        const wrapper = shallow(setup());

        expect(wrapper.find(Icons.HomeIcon)).toBeTruthy();
        expect(wrapper.find(Icons.SearchIcon)).toBeTruthy();
        expect(wrapper.find(Icons.PaymentIcon)).toBeTruthy();
        expect(wrapper.find(Icons.NotificationIcon)).toBeTruthy();
        expect(wrapper.find(Icons.MenuIcon)).toBeTruthy();
    });

    /**
     * should transition to PAYMENT_HOME when on click item [支払う]
     */
    it('should transition to PAYMENT_HOME when on click item [支払う]', () => {
        localStorage.setItem('idToken', 'token');
        const wrapper = shallow(setup());

        const item = wrapper.find('div.footer-item-center-container');

        item.simulate('click');

        expect(wrapper).toBeTruthy();
    });
});

describe('Unit test for FooterItem', () => {
    beforeEach(() => {
        jest.restoreAllMocks();
        jest.resetAllMocks();

        mockedUseNavigate.mockReset();
    });
    /**
     * setup element
     */
    const setup = (p: FooterItemProps): React.JSX.Element => {
        return <FooterItem {...p} />;
    };

    /**
     * should render FooterIem
     */
    it('should render FooterIem', () => {
        localStorage.setItem('idToken', 'token');
        const wrapper = shallow(
            setup({
                name: 'footer_item_label',
                label: 'footer_item_label',
                matches: ['/'],
                router: '/',
            })
        );
        expect(wrapper).toBeTruthy();
        expect(wrapper.find(Typography).text()).toBe('footer_item_label');
    });

    /**
     * should render FooterIem with Icon
     */
    it('should render FooterIem with Icon', () => {
        const useMemo = jest.spyOn(React, 'useMemo').mockImplementationOnce((f) => f());
        localStorage.setItem('idToken', 'token');
        const wrapper = shallow(
            setup({
                name: 'wallet',
                label: 'ウォレット',
                matches: ['/test'],
                router: '/test',
                icon: () => <Icons.WalletIcon color={'#707070'} /> as any,
            })
        );
        expect(useMemo).toHaveBeenCalled();
        expect(wrapper).toBeTruthy();
    });

    /**
     * should transition to router when footer item clicked
     */
    it('should transition to router when footer item clicked', () => {
        localStorage.setItem('idToken', 'token');
        const wrapper = shallow(
            setup({
                name: 'footer_item_label',
                label: 'footer_item_label',
                matches: ['/test'],
                router: '/test',
                icon: () => <Icons.HomeIcon />,
            })
        );

        const item = wrapper.find('div.footer-item');

        item.simulate('click');

        expect(mockedUseNavigate).toHaveBeenCalledWith('/test');
    });
});
