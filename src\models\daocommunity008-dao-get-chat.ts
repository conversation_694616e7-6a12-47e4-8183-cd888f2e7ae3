import { CommonCallback } from './common';

/**
 * DAOCommunity008DaoGetChatModel
 */
export interface DAOCommunity008DaoGetChatModel extends CommonCallback {
    // DaoID
    DaoID: string;
    // チャネルID
    ChannelID: string;
    // スレッドID
    ThreadID: string;
    // 前スレッド・次スレッド種別
    PrevNextType: number;
    // 最後の並び順番号（この番号を基準にデータを取得）
    LastVaridSortNo?: number;
    // スレッド取得上限数
    LimitCount: number;
    // チャット投稿登録後フラグ
    IsAfterPutChat?: boolean;
    // 最新の並び順（登録されたチャットのSortNo）
    NewSortNo?: number;
    // タイマー取得フラグ
    IsTimer?: boolean;
    // 最後のタイマー読込み日時
    TimerReadDateTime?: string;
}

/**
 * DAOCommunity008DaoGetChatChatItem
 */
export interface DAOCommunity008DaoGetChatChatItem {
    // チャットID
    ChatID: string;
    // スレッドID
    ThreadID?: string;
    // スレッド種別
    ThreadType: number;
    // 投稿内容
    Content: string;
    // 編集フラグ
    EditFlag: number;
    // 削除フラグ
    DeleteFlag: number;
    // 有効並び順
    VaridSortNo: number;
    // 投稿者ID
    PostCheerID: string;
    // 投稿者名
    PostCheerName: string;
    // 投稿日時
    PostDateTime: string;
    // プロフィールメッセージ
    Message: string;
    // プロフィール画像ファイル名
    ProfileImageFileName: string;
    // リアクションリスト
    ReactionList: DAOCommunity008DaoGetChatReactionItem[];
    // メンションリスト
    MentionList: DAOCommunity008DaoGetChatMentionItem[];
}

/**
 * DAOCommunity008DaoGetChatMentionItem
 */
export interface DAOCommunity008DaoGetChatMentionItem {
    // 応援者ID
    CheerID: string;
    // ニックネーム
    NickName: string;
    // メッセージ
    Message: string;
    // プロフィール画像ファイル名
    ProfileImageFileName: string;
    // optional
    ImageLink?: string;
}

/**
 * DAOCommunity008DaoGetChatReactionItem
 */
export interface DAOCommunity008DaoGetChatReactionItem {
    // スレッドID
    ThreadID: string;
    // チャットID
    ChatID: string;
    // リアクション画像ID
    ReactionImageID: string;
    // リアクション応援者ID
    ReactionCheerID: string;
}

/**
 * DAOCommunity008DaoGetChatResult
 */
export interface DAOCommunity008DaoGetChatResult {
    // 前チャットデータ存在フラグ
    IsPrevChatDataExists: boolean;
    // チャットリスト
    ChatList: DAOCommunity008DaoGetChatChatItem[];
}
