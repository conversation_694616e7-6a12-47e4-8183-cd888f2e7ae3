.comment-input-container {
    border: 1px solid #ebebeb;
    border-radius: 6px;

    .mentions {
        flex: 1;
        border: 1px solid #bfbfbf;
        border-radius: 4px;
        min-height: 80px;

        &:has(textarea:hover) {
            border: 1px solid var(--app-base-color);
        }

        &:has(textarea:focus) {
            border: 1px solid var(--app-base-color);
        }

        &.error {
            border-color: #d31e2d !important;
        }

        textarea {
            width: 100%;
            color: #222222;
            min-height: 40px;

            &:hover {
                border: none !important;
                outline: none !important;
            }

            &:focus {
                border: none !important;
                outline: none !important;
            }

            &:active {
                border: none !important;
                outline: none !important;
            }

            &::placeholder {
                color: #707070;
                opacity: 1;
            }

            &:-ms-input-placeholder {
                color: #707070;
            }

            &::-ms-input-placeholder {
                color: #707070;
            }
        }
    }

    .mentions.placeholder-red {
        textarea {
            &::placeholder {
                color: #d31e2d;
                opacity: 1;
            }
        }
    }

    .mentions.mentions--multiLine {
        .mentions__control {
            .mentions__highlighter {
                padding: 10px !important;
                outline: 0;
                border: none !important;
                font-size: 14px !important;
            }
        }

        .mentions__input {
            padding: 10px !important;
            outline: 0;
            border: 0;
            font-size: 14px !important;
        }
    }
}

.mentions__suggestions {
    max-height: 130px;
    overflow-y: scroll;
    border-radius: 8px;
    border: 1px solid #ccc;
}

.mentions__suggestions__list {
    background-color: white;
    font-size: 10px;
}

.mentions__suggestions__item {
    padding: 8px 16px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.15);
}

.mentions__suggestions__item--focused {
    background-color: rgba(var(--app-base-color), 0.5);
}

.mentions__mention {
    position: relative;
    z-index: 1;
    color: #007bff;
    pointer-events: none;
}
