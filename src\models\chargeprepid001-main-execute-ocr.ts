import { CommonCallback } from './common';

/**
 * ChargePrePid001MainExecuteOcrModel
 */
export interface ChargePrePid001MainExecuteOcrModel extends CommonCallback {
    // 解析対象画像データ（JPEG/PNG形式）
    SdkBytes: string;
}

/**
 * ChargePrePid001MainExecuteOcrItem
 */
export interface ChargePrePid001MainExecuteOcrItem {
    Text: string;
}

/**
 * ChargePrePid001MainExecuteOcrResult
 */
export interface ChargePrePid001MainExecuteOcrResult {
    // OCR解析取得結果
    Blocks: ChargePrePid001MainExecuteOcrItem[];
}
