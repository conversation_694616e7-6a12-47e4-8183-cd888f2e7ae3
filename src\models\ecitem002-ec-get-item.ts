import { CommonCallback } from './common';

/**
 * ECItem002ECGetItemModel
 */
export interface ECItem002ECGetItemModel extends CommonCallback {
    // 商品ID
    ItemID: string;
    BrandID?: string;
    CheerID?: string;
}

export interface ECItem002Image {
    FileName: string;
    Main: number;
}

/**
 * ECItem002ECGetItemResult
 */
export interface ECItem002ECGetItemResult {
    // 商品ID
    ItemID: string;
    // 商品名
    ItemName: string;
    // 見出し
    Caption: string;
    // 商品説明
    Explanation: string;
    // 商品画像
    ItemImageList: ECItem002Image[];
    // 税込み額
    TaxPrice: number;
    // 販売可能在庫数
    AvailableStock: number;
    // 削除フラグ
    DeleteFlag: boolean;
    // 決済状態
    SettlementStatus: string;
    // 在庫数少数表示閾値
    FewStockDispThreshold: number;
    // 決済有効期限
    SettlementExpiryDuration: string;
    // 更新日時
    UpdateDateTime: string;
    // 数量
    Quantity: number;
    // 商品件数
    ItemCount: number;
    // 商品追加済みフラグ // True:対象商品追加済み_False:未追加
    IsAdded: boolean;
    // 加盟店ID
    StoreID: string;
    // 加盟店名称
    StoreName: string;
    // 営業時間
    BusinessHour: string;
    // 都道府県
    StorePrefectures: string;
    // 市区町村
    StoreMunicipalities: string;
    // 町域
    StoreTownArea: string;
    // 番地
    StoreHouseNumber: string;
    // 建物名
    StoreBuildingName: string;
    // 買い物かごデータ存在フラグ
    IsCartExists: boolean;
    // 対象商品の買い物かご追加済みフラグ
    IsItemExists: boolean;
    // 署名付きURL
    Url: string;
    // 署名付きURL - 0:正常 1:商品削除 2:購入不可 3:販売開始前 4:販売終了
    ItemStatus: string;
}
