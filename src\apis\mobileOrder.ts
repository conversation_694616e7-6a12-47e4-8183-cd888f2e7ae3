import API from '../constants/api';
import Environments from '../constants/environments';
import { BaseResponse } from '../models/common';
import {
    MobileOrderCart001MobileOrderCheckBeforeConfirmModel,
    MobileOrderCart001MobileOrderCheckBeforeConfirmResult,
} from '../models/mobileordercart001-mobileorder-check-beforeconfirm';
import { MobileOrderCart001MobileOrderConfirmOrderModel } from '../models/mobileordercart001-mobileorder-confirm-order';
import {
    MobileOrderCart001MobileOrderDeleteItemModel,
    MobileOrderCart001MobileOrderDeleteItemResult,
} from '../models/mobileordercart001-mobileorder-delete-item';
import {
    MobileOrderCart001MobileOrderGetCartModel,
    MobileOrderCart001MobileOrderGetCartResult,
} from '../models/mobileordercart001-mobileorder-get-cart';
import {
    MobileOrderHistory001MobileOrderGetOrderListModel,
    MobileOrderHistory001MobileOrderGetOrderListResult,
} from '../models/mobileorderhistory001-mobileorder-get-orderlist';
import {
    MobileOrderItem001MobileOrderGetItemListModel,
    MobileOrderItem001MobileOrderGetItemListResult,
} from '../models/mobileorderitem001-mobileorder-get-itemlist';
import {
    MobileOrderItem001MobileOrderGetStoreNameModel,
    MobileOrderItem001MobileOrderGetStoreNameResult,
} from '../models/mobileorderitem001-mobileorder-get-storename';
import {
    MobileOrderItem002MobileOrderGetItemModel,
    MobileOrderItem002MobileOrderGetItemResult,
} from '../models/mobileorderitem002-mobileorder-get-item';
import Utils from '../utils/utils';
import createAPI from './baseApi';

class MobileOrderApi {
    /**
     * mobileOrderItem001MobileOrderGetItemList
     * @param data MobileOrderItem001MobileOrderGetItemListModel
     * @returns Promise<BaseResponse<MobileOrderItem001MobileOrderGetItemListResult>>
     */
    static mobileOrderItem001MobileOrderGetItemList = (
        data: MobileOrderItem001MobileOrderGetItemListModel
    ): Promise<BaseResponse<MobileOrderItem001MobileOrderGetItemListResult>> => {
        return createAPI({
            url: API.MOBILEORDERITEM001_MOBILEORDER_GET_ITEMLIST,
            data: {
                BrandID: Environments.brandID,
                ...data,
            },
        });
    };

    /**
     * mobileOrderItem001MobileOrderGetStoreName
     * @param data MobileOrderItem001MobileOrderGetStoreNameModel
     * @returns Promise<BaseResponse<MobileOrderItem001MobileOrderGetStoreNameResult>>
     */
    static mobileOrderItem001MobileOrderGetStoreName = (
        data: MobileOrderItem001MobileOrderGetStoreNameModel
    ): Promise<BaseResponse<MobileOrderItem001MobileOrderGetStoreNameResult>> => {
        return createAPI({
            url: API.MOBILEORDERITEM001_MOBILEORDER_GET_STORENAME,
            data: {
                BrandID: Environments.brandID,
                ...data,
            },
        });
    };

    /**
     * mobileOrderItem002MobileOrderGetItem
     * @param data MobileOrderItem002MobileOrderGetItemModel
     * @returns Promise<BaseResponse<MobileOrderItem002MobileOrderGetItemResult>>
     */
    static mobileOrderItem002MobileOrderGetItem = (
        data: MobileOrderItem002MobileOrderGetItemModel
    ): Promise<BaseResponse<MobileOrderItem002MobileOrderGetItemResult>> => {
        return createAPI({
            url: API.MOBILEORDERITEM002_MOBILEORDER_GET_ITEM,
            data: {
                BrandID: Environments.brandID,
                CheerID: Utils.getCheerID() || undefined,
                ...data,
            },
        });
    };

    /**
     * mobileOrderCart001MobileOrderGetCart
     * @param data MobileOrderCart001MobileOrderGetCartModel
     * @returns Promise<BaseResponse<MobileOrderCart001MobileOrderGetCartResult>>
     */
    static mobileOrderCart001MobileOrderGetCart = (
        data: MobileOrderCart001MobileOrderGetCartModel
    ): Promise<BaseResponse<MobileOrderCart001MobileOrderGetCartResult>> => {
        return createAPI<MobileOrderCart001MobileOrderGetCartResult>({
            url: API.MOBILEORDERCARD001_MOBILEORDER_GET_CART,
            data,
        });
    };

    /**
     * mobileOrderCart001MobileOrderDeleteItem
     * @param data MobileOrderCart001MobileOrderDeleteItemModel
     * @returns Promise<BaseResponse<MobileOrderCart001MobileOrderGetCartResult>>
     */
    static mobileOrderCart001MobileOrderDeleteItem = (
        data: MobileOrderCart001MobileOrderDeleteItemModel
    ): Promise<BaseResponse<MobileOrderCart001MobileOrderDeleteItemResult>> => {
        return createAPI<MobileOrderCart001MobileOrderDeleteItemResult>({
            url: API.MOBILEORDERCART001_MOBILEORDER_DELETE_ITEM,
            data,
        });
    };

    /**
     * mobileOrderCart001MobileOrderCheckBeforeConfirm
     * @param data MobileOrderCart001MobileOrderCheckBeforeConfirmModel
     * @returns Promise<BaseResponse<MobileOrderCart001MobileOrderCheckBeforeConfirmResult>>
     */
    static mobileOrderCart001MobileOrderCheckBeforeConfirm = (
        data: MobileOrderCart001MobileOrderCheckBeforeConfirmModel
    ): Promise<BaseResponse<MobileOrderCart001MobileOrderCheckBeforeConfirmResult>> => {
        return createAPI<MobileOrderCart001MobileOrderCheckBeforeConfirmResult>({
            url: API.MOBILEORDERCART001_MOBILEORDER_CHECK_BEFORECONFIRM,
            data,
        });
    };

    /**
     * mobileOrderCart001MobileOrderConfirmOrder
     * @param data MobileOrderCart001MobileOrderCheckBeforeConfirmModel
     * @returns Promise<BaseResponse<MobileOrderCart001MobileOrderCheckBeforeConfirmResult>>
     */
    static mobileOrderCart001MobileOrderConfirmOrder = (
        data: MobileOrderCart001MobileOrderConfirmOrderModel
    ): Promise<BaseResponse> => {
        return createAPI({
            url: API.MOBILEORDERCART001_MOBILEORDER_CONFIRM_ORDER,
            data,
        });
    };

    /**
     * mobileOrderHistory001MobileOrderGetOrderList
     * @param data MobileOrderHistory001MobileOrderGetOrderListModel
     * @returns Promise<BaseResponse<MobileOrderHistory001MobileOrderGetOrderListResult>>
     */
    static mobileOrderHistory001MobileOrderGetOrderList = (
        data: MobileOrderHistory001MobileOrderGetOrderListModel
    ): Promise<BaseResponse<MobileOrderHistory001MobileOrderGetOrderListResult>> => {
        return createAPI<MobileOrderHistory001MobileOrderGetOrderListResult>({
            url: API.MOBILEORDERHISTORY001_MOBILEORDER_GET_ORDERLIST,
            data,
        });
    };
}

export default MobileOrderApi;
