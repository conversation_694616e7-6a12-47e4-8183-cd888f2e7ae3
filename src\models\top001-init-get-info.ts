import { CommonCallback } from './common';

/**
 * Top001InitGetInfoModel
 */
export interface Top001InitGetInfoModel extends CommonCallback {
    // ブランドID
    BrandID: string;
}

/**
 * TopPageSpecialFeatureImageItem
 */
export interface TopPageSpecialFeatureImageItem {
    // トップページ用特集画像
    Image: string;
    // トップページ用特集URL
    Link: string;
}

/**
 * TopPageSubImagesItem
 */
export interface TopPageSubImagesItem {
    // トップページ用特集画像
    Image: string;
    // トップページ用特集URL
    Link: string;
}

/**
 * Top001InfoItem
 */
export interface Top001InfoItem {
    // トップページ用メイン画像
    TopPageMainImage: string;
    // トップページ用特集リスト
    TopPageSpecialFeatureImages: TopPageSpecialFeatureImageItem[];
    // 特設サイトURL
    TopPageExternalLink: string;
    // トップページ用サブ画像
    TopPageSubImages: TopPageSubImagesItem[];
    // ブランド名
    BrandName: string;
}

/**
 * Top001InitGetInfoResult
 */
export interface Top001InitGetInfoResult {
    // ブランドID
    TopInfoList: Top001InfoItem;
}
