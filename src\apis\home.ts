import API from '../constants/api';
import { BaseResponse } from '../models/common';
import { Home001InitGetBannerListModel, Home001InitGetBannerListResult } from '../models/home001-init-get-banner-list';
import { Home001InitGetCoinItem, Home001InitGetCoinModel } from '../models/home001-init-get-coin';
import { Home001InitGetCouponListModel, Home001InitGetCouponListResult } from '../models/home001-init-get-coupon-list';
import {
    Home001InitGetEventTicketListModel,
    Home001InitGetEventTicketListResult,
} from '../models/home001-init-get-event-ticket-list';
import {
    Home001InitGetNewsUnreadFlagModel,
    Home001InitGetNewsUnreadFlagResult,
} from '../models/home001-init-get-news-unread-flag';
import {
    Home001InitGetNewsUnreadLatestModel,
    Home001InitGetNewsUnreadLatestResult,
} from '../models/home001-init-get-news-unread-latest';
import { Home001InitGetUserInfoModel, Home001InitGetUserInfoResult } from '../models/home001-init-get-userinfo';
import createAPI from './baseApi';

/**
 * HomeAPI
 */
class HomeAPI {
    /**
     * home001InitGetNewsUnreadLatest
     * @param data Home001InitGetNewsUnreadLatestModel
     * @returns Promise<BaseResponse<Home001InitGetNewsUnreadLatestResult>>
     */
    static home001InitGetNewsUnreadLatest = (
        data: Home001InitGetNewsUnreadLatestModel
    ): Promise<BaseResponse<Home001InitGetNewsUnreadLatestResult>> => {
        return createAPI<Home001InitGetNewsUnreadLatestResult>({
            url: API.HOME001_INIT_GET_NEWS_UNREAD_LATEST,
            data,
        });
    };

    /**
     * home001InitGetCoin
     * @param data Home001InitGetCoinModel
     * @returns Promise<BaseResponse<Home001InitGetCoinItem[]>>
     */
    static home001InitGetCoin = (data: Home001InitGetCoinModel): Promise<BaseResponse<Home001InitGetCoinItem[]>> => {
        return createAPI<Home001InitGetCoinItem[]>({
            url: API.HOME001_INIT_GET_COIN,
            data,
        });
    };

    /**
     * home001InitGetCouponList
     * @param data Home001InitGetCouponListModel
     * @returns Promise<BaseResponse<Home001InitGetCouponListResult>>
     */
    static home001InitGetCouponList = (
        data: Home001InitGetCouponListModel
    ): Promise<BaseResponse<Home001InitGetCouponListResult>> => {
        return createAPI<Home001InitGetCouponListResult>({
            url: API.HOME001_INIT_GET_COUPON_LIST,
            data,
        });
    };

    /**
     * home001InitGetEventTicketList
     * @param data Home001InitGetEventTicketListModel
     * @returns Promise<BaseResponse<Home001InitGetEventTicketListResult>>
     */
    static home001InitGetEventTicketList = (
        data: Home001InitGetEventTicketListModel
    ): Promise<BaseResponse<Home001InitGetEventTicketListResult>> => {
        return createAPI<Home001InitGetEventTicketListResult>({
            url: API.HOME001_INIT_GET_EVENT_TICKET_LIST,
            data,
        });
    };

    /**
     * home001InitGetBannerList
     * @param data Home001InitGetBannerListModel
     * @returns Promise<BaseResponse<Home001InitGetBannerListResult>>
     */
    static home001InitGetBannerList = (
        data: Home001InitGetBannerListModel
    ): Promise<BaseResponse<Home001InitGetBannerListResult>> => {
        return createAPI<Home001InitGetBannerListResult>({
            url: API.HOME001_INIT_GET_BANNER_LIST,
            data,
        });
    };

    /**
     * home001InitGetNewsUnreadFlag
     * @param data Home001InitGetNewsUnreadFlagModel
     * @returns Promise<BaseResponse<Home001InitGetNewsUnreadFlagResult>>
     */
    static home001InitGetNewsUnreadFlag = (
        data: Home001InitGetNewsUnreadFlagModel
    ): Promise<BaseResponse<Home001InitGetNewsUnreadFlagResult>> => {
        return createAPI<Home001InitGetNewsUnreadFlagResult>({
            url: API.HOME001_INIT_GET_NEWS_UNREAD_FLAG,
            data,
        });
    };

    /**
     * home001InitGetUserInfo
     * @param data Home001InitGetUserInfoModel
     * @returns Promise<BaseResponse<Home001InitGetUserInfoResult>>
     */
    static home001InitGetUserInfo = (
        data: Home001InitGetUserInfoModel
    ): Promise<BaseResponse<Home001InitGetUserInfoResult>> => {
        return createAPI<Home001InitGetUserInfoResult>({
            url: API.HOME001_INIT_GET_USERINFO,
            data,
        });
    };
}

export default HomeAPI;
