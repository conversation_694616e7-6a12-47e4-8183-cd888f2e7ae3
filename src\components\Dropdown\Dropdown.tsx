/* eslint-disable @typescript-eslint/no-explicit-any */
import { MenuItem, Select, SelectChangeEvent, SelectProps } from '@mui/material';
import clsx from 'clsx';
import React, { ReactNode, useCallback, useEffect, useMemo, useState } from 'react';
import { CommonError, RequiredLabel, RequiredLabelProps } from '../Common';
import Icons from '../Icons/Icons';
import './styles.scss';

// OmittedSelectProps
type OmittedSelectProps = Omit<SelectProps, 'value' | 'onChange' | 'label' | 'error' | 'placeholder' | 'variant'>;

/**
 * DropdownProps
 */
export interface DropdownProps extends RequiredLabelProps, OmittedSelectProps {
    /**
     * dropdown's value
     */
    value?: string;
    /**
     * dropdown's data
     */
    data: any[];
    /**
     * dropdown's key value. Value (from data) return on onChange function
     */
    keyValue: string;
    /**
     * dropdown's key display. Value (from data) display on screen
     */
    keyDisplay: string;
    /**
     * dropdown's placeholder
     */
    placeholder?: ReactNode;
    /**
     * categoryOption
     */
    categoryOption?: ReactNode;
    /**
     * iconOption
     */
    iconOption?: ReactNode;
    /**
     * disabledOption
     */
    disabledOption?: ReactNode;
    /**
     * dropdown's error
     */
    error?: string | boolean;
    /**
     * onChange
     * @param event React.ChangeEvent<any>
     * @returns void
     */
    onChange?: (event: React.ChangeEvent<any>) => void;
    /**
     * formatKeyDisplay
     */
    formatKeyDisplay?: (value?: string) => string;
}

/**
 * DropdownIcon
 * @param props any
 * @returns React.JSX.Element
 */
export const DropdownIcon = (props: any): React.JSX.Element => {
    return (
        <div {...props}>
            <Icons.ArrowDownIcon />
        </div>
    );
};

/**
 * Dropdown
 * @returns React.JSX.Element
 */
const Dropdown = (props: DropdownProps): React.JSX.Element => {
    const {
        value: inputValue = '',
        label,
        required,
        data,
        keyValue,
        keyDisplay,
        placeholder = '選択してください。',
        error,
        disabledOption,
        categoryOption,
        iconOption,
        onChange,
        formatKeyDisplay,
        ...rest
    } = useMemo(() => props, [props]);

    const [value, setValue] = useState<string>(inputValue);

    useEffect(() => {
        setValue(inputValue);
    }, [inputValue]);

    /**
     * handleChange
     * @param event SelectChangeEvent
     */
    const handleChange = useCallback(
        (event: SelectChangeEvent<unknown>) => {
            // get selected value
            const value = event.target.value as string;

            // set display value
            setValue(value);

            // dispatch value to parent
            onChange?.(event as React.ChangeEvent);
        },
        [onChange]
    );

    /**
     * renderDropdownItem
     * @param item any (temp)
     * @param index number
     */
    const renderDropdownItem = useCallback(
        (item: any, index: number) => {
            // key display value
            const value = item[keyDisplay];
            return (
                <MenuItem
                    className={clsx('dropdown-component-menu-item', {
                        'with-icon': Boolean(iconOption),
                    })}
                    key={index}
                    value={item[keyValue]}
                    TouchRippleProps={{ className: 'dropdown-ripple' }}
                >
                    {formatKeyDisplay ? formatKeyDisplay(value) : value}
                    {iconOption}
                </MenuItem>
            );
        },
        [formatKeyDisplay, iconOption, keyDisplay, keyValue]
    );

    return useMemo(
        () => (
            <div className="common-dropdown-component">
                {label && <RequiredLabel label={label} required={required} />}
                <div className="dropdown-container">
                    <Select
                        {...rest}
                        value={value}
                        onChange={handleChange}
                        displayEmpty
                        variant="standard"
                        disableUnderline
                        fullWidth
                        inputProps={{ 'aria-label': 'Without label' }}
                        IconComponent={DropdownIcon}
                        className={clsx({
                            error: error,
                        })}
                        MenuProps={{
                            className: 'dropdown-component-menu-content-props',
                        }}
                    >
                        <MenuItem value="" hidden>
                            {placeholder}
                        </MenuItem>
                        {categoryOption && (
                            <MenuItem disabled value="disabled" style={{ color: 'var(--app-base-color)', opacity: 1 }}>
                                {categoryOption}
                            </MenuItem>
                        )}
                        {data.length === 0 && disabledOption && (
                            <MenuItem disabled value="disabled">
                                {disabledOption}
                            </MenuItem>
                        )}
                        {(data || []).map(renderDropdownItem)}
                    </Select>
                    <CommonError error={error} componentId={rest.name} />
                </div>
            </div>
        ),
        [
            categoryOption,
            data,
            disabledOption,
            error,
            handleChange,
            label,
            placeholder,
            renderDropdownItem,
            required,
            rest,
            value,
        ]
    );
};

export default Dropdown;
