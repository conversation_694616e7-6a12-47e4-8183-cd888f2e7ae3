import { shallow } from 'enzyme';
import { createRef } from 'react';
import Button from '../Button';
import Zodiac, { ZodiacForwardRef, ZodiacProps } from '../Zodiac/Zodiac';

/**
 * Unit test for Zodiac component
 */
describe('Unit test for Zodiac component', () => {
    /**
     * setup element
     * @param props ZodiacProps
     */
    const setup = (props?: ZodiacProps) => {
        const ref = createRef<ZodiacForwardRef>();
        const s = shallow(<Zodiac {...props} ref={ref} />);

        return {
            shallow: s,
            props,
            ref,
        };
    };

    /**
     * display enough elements
     */
    describe('display enough elements', () => {
        const { shallow } = setup();

        /**
         * display image
         */
        it('display image', () => {
            expect(shallow.find('img').length).toEqual(24);
        });

        /**
         * display button next
         */
        it('display button next', () => {
            expect(shallow.find(Button).length).toEqual(1);
            expect(shallow.find(Button).text()).toEqual('次へ');
        });

        /**
         * display button next with custom text
         */
        it('display button next with custom text', () => {
            const { shallow } = setup({
                buttonText: ['text step 1', 'text step 2'],
            });
            expect(shallow.find(Button).length).toEqual(1);
            expect(shallow.find(Button).text()).toEqual('text step 1');
        });

        /**
         * do not display button next
         */
        it('do not display button next', () => {
            const { shallow } = setup({ autoNextStep: true });

            expect(shallow.find(Button).length).toEqual(0);
        });
    });

    /**
     * Actions
     */
    describe('Actions', () => {
        /**
         * check auto next step
         */
        it('check auto next step', () => {
            const setState = jest.fn();
            const { shallow } = setup({ zodiacValues: [undefined, setState], autoNextStep: true, autoNextDelay: 100 });
            const picture = shallow.find('div.zodiac-item')?.at(0);

            picture.simulate('click');
        });

        /**
         * click button next and finish in flow
         */
        it('click button next and finish in flow', () => {
            const setState = jest.fn();
            const onSubmit = jest.fn();
            const { shallow, ref } = setup({ zodiacValues: [undefined, setState], onSubmit });
            ref.current?.initState();

            // in step 1: click to step 2
            const buttonNext = shallow.find(Button).at(0);
            buttonNext.simulate('click');
            expect(setState).toBeCalledTimes(1);

            // in step 2: click to finish
            const buttonFinish = shallow.find(Button).at(0);
            buttonFinish.simulate('click');
            expect(onSubmit).toBeCalledTimes(1);
        });
    });
});
