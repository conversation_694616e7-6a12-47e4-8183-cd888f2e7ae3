/* eslint-disable max-len */
import { Paper, Typography } from '@mui/material';
import clsx from 'clsx';
import _ from 'lodash';
import { memo, useCallback, useEffect, useMemo, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import CommonAPI from '../../apis/common';
import API from '../../constants/api';
import Environments from '../../constants/environments';
import Screens from '../../constants/screens';
import AuthUtils from '../../utils/auth';
import CommonUtils from '../../utils/common';
import Utils from '../../utils/utils';
import Icons from '../Icons/Icons';
import Image from '../Image';
import LanguageDropdown from '../LanguageDropdown';
import './styles.scss';
import { isBrowser, isTablet } from 'react-device-detect';

/**
 * HeaderStyle
 */
interface HeaderStyle {
    /**
     * container
     */
    container?: React.CSSProperties;
    /**
     * title
     */
    title?: React.CSSProperties;
}

/**
 * headerStyles
 */
const headerStyles: { [key: string]: HeaderStyle } = {
    // main style
    main: {
        container: {
            height: 48,
            backgroundColor: '#fff',
            justifyContent: 'center',
            paddingLeft: 16,
            paddingRight: 16,
        },
    },
    // mainWithQrCode style
    mainWithQrCode: {
        container: {
            height: 48,
            backgroundColor: '#fff',
            justifyContent: 'center',
            paddingLeft: 16,
            paddingRight: 16,
        },
    },
    // back icon on the left side and logo
    backLeftWithMainLogo: {
        container: {
            backgroundColor: '#fff',
            minHeight: 48,
            paddingLeft: 0,
            paddingRight: 16,
        },
    },
    // back icon on the left side and title
    backLeftWithTitle: {
        container: {
            backgroundColor: '#fff',
            minHeight: 53,
            paddingLeft: 0,
            paddingRight: 0,
        },
    },
    // back icon on the left side and title; and noti menu right
    backLeftWithTitleNotiMenu: {
        container: {
            backgroundColor: '#fff',
            minHeight: 53,
            paddingLeft: 0,
            paddingRight: 0,
        },
    },
    // back icon on the left side and title; and menu right
    backLeftWithTitleMenu: {
        container: {
            backgroundColor: '#fff',
            minHeight: 53,
            paddingLeft: 0,
            paddingRight: 0,
        },
    },
    // close icon on the right side and title
    closeRightWithTitle: {
        container: {
            backgroundColor: '#fff',
            minHeight: 53,
            paddingLeft: 0,
            paddingRight: 0,
        },
    },
    // only title
    withTitle: {
        container: {
            backgroundColor: '#fff',
            minHeight: 53,
            paddingLeft: 16,
            paddingRight: 16,
        },
    },
    // title only
    withTitleOnly: {
        container: {
            backgroundColor: '#fff',
            minHeight: 53,
            paddingLeft: 16,
            paddingRight: 16,
        },
    },
    // only title and translate
    withTitleAndTranslate: {
        container: {
            backgroundColor: '#fff',
            minHeight: 53,
            paddingLeft: 16,
            paddingRight: 16,
        },
    },
    // main style + button translate right
    mainTranslate: {
        container: {
            height: 48,
            backgroundColor: '#fff',
            justifyContent: 'center',
            paddingLeft: 16,
            paddingRight: 16,
        },
    },
    // backLeft TitleCenter And MenuRight
    backLeftTitleCenterAndMenuRight: {
        container: {
            height: 48,
            backgroundColor: '#fff',
            justifyContent: 'center',
            paddingLeft: 0,
            paddingRight: 0,
        },
    },
    titleCenterAndMenuRight: {
        container: {
            height: 48,
            backgroundColor: '#fff',
            justifyContent: 'center',
            paddingLeft: 16,
            paddingRight: 16,
        },
    },
    // main style + button translate right
    withMyPage: {
        container: {
            height: 48,
            backgroundColor: '#fff',
            justifyContent: 'center',
            paddingLeft: 16,
            paddingRight: 16,
        },
    },
    // fullsize logo only
    withFullSizeLogoOnly: {
        container: {
            backgroundColor: '#fff',
            minHeight: 53,
            paddingLeft: 16,
            paddingRight: 16,
        },
    },
};

// header type
type HeaderType =
    | 'main'
    | 'mainWithQrCode'
    | 'backLeftWithTitle'
    | 'backLeftWithTitleNotiMenu'
    | 'backLeftWithTitleMenu'
    | 'closeRightWithTitle'
    | 'withTitle'
    | 'withTitleOnly'
    | 'withTitleAndTranslate'
    | 'mainTranslate'
    | 'backLeftTitleCenterAndMenuRight'
    | 'logoCenterAndMenuRight'
    | 'titleCenterAndMenuRight'
    | 'withMyPage'
    | 'withFullSizeLogoOnly';

/**
 * LogoProps
 */
export interface LogoProps {
    /**
     * onLogoClick
     */
    onLogoClick?: () => void;
}

/**
 * HeaderProps
 */
export interface HeaderProps extends LogoProps {
    /**
     * type
     */
    type?: HeaderType;
    /**
     * title
     */
    title?: string | React.ReactNode;
    /**
     * onLeftButtonClick
     */
    onLeftButtonClick?: () => void;
    /**
     * onRightButtonClick
     */
    onRightButtonClick?: () => void;
    /**
     * action key
     */
    actionKey?: string;
    /**
     * classNameCustom
     */
    classNameCustom?: string;
    /**
     * isHiddenMenuNotification
     */
    isHiddenMenuNotification?: boolean;
    /**
     * isHiddenBackButton
     */
    isHiddenBackButton?: boolean;
}

/**
 * FullSizeLogo
 * For full size app logo
 */
export const FullSizeLogo = (props: LogoProps): React.JSX.Element => {
    const { onLogoClick } = useMemo(() => props, [props]);

    return useMemo(
        () => (
            <div className="d-flex align-items-center justify-content-center logo logo-full" onClick={onLogoClick}>
                <Image
                    style={{
                        height: '100%',
                    }}
                    // eslint-disable-next-line max-len
                    src={`${Environments.baseUrl}/${Environments.brandID}-cheermedalbucket-${Environments.env}/Logo/BrandLogo.png`}
                    alt="BrandLogo"
                />
            </div>
        ),
        [onLogoClick]
    );
};

/**
 * HalfSizeLogo
 * For half size app logo
 */
export const HalfSizeLogo = (props: LogoProps): React.JSX.Element => {
    const { onLogoClick } = useMemo(() => props, [props]);

    return useMemo(
        () => (
            <div className="d-flex align-items-center justify-content-center logo logo-half" onClick={onLogoClick}>
                50 x 30
            </div>
        ),
        [onLogoClick]
    );
};

/**
 * Header
 * @param props HeaderProps
 * @returns React.JSX.Element
 */
const Header = (props: HeaderProps): React.JSX.Element => {
    const navigate = useNavigate();

    const idToken = AuthUtils.retrieveToken('idToken');

    const [unreadFlag, setUnreadFlag] = useState<boolean>();

    const {
        onLogoClick,
        onLeftButtonClick,
        onRightButtonClick,
        classNameCustom,
        isHiddenMenuNotification = false,
        isHiddenBackButton,
    } = useMemo(() => props, [props]);

    /**
     * commonHeader001InitGetNewsUnreadFlag
     */
    const commonHeader001InitGetNewsUnreadFlag = useCallback(async () => {
        const {
            status,
            result,
            Message = 'api.common.unknown_error',
        } = await CommonAPI.commonHeader001InitGetNewsUnreadFlag({});
        if (status === API.STATUS_CODE.SUCCESS) {
            setUnreadFlag(result?.UnreadFlag);
        } else {
            CommonUtils.showMessage({
                message: Utils.t(Message),
                type: 'ERROR',
            });
        }
    }, []);

    useEffect(() => {
        if (idToken) {
            commonHeader001InitGetNewsUnreadFlag();
        }
    }, [commonHeader001InitGetNewsUnreadFlag, idToken]);

    /**
     * Handle left button click
     */
    const handleOnLeftButtonClick = useCallback(() => {
        onLeftButtonClick && onLeftButtonClick();
    }, [onLeftButtonClick]);

    /**
     * handleClickTopIcon
     */
    const handleClickTopIcon = useCallback(() => {
        CommonAPI.commonUserTransitionHistory({
            OriginScreenName: window.location.pathname,
            DestinationScreenName: Screens.HOME,
            ElementName: Utils.t('common.text.top')?.replaceAll('\n', ''),
            DeviceID: isBrowser ? 'PC' : isTablet ? 'Tablet' : 'mobile',
        });
        navigate(Screens.HOME);
    }, [navigate]);

    /**
     * Handle right button click
     */
    const handleOnRightButtonClick = useCallback(() => {
        onRightButtonClick && onRightButtonClick();
    }, [onRightButtonClick]);

    /**
     * handleClickInfoButton
     * Handle click on [お知らせボタン] button
     */
    const handleClickInfoButton = useCallback(() => {
        CommonAPI.commonUserTransitionHistory({
            OriginScreenName: window.location.pathname,
            DestinationScreenName: Screens.NEWS,
            ElementName: Utils.t('header.news')?.replaceAll('\n', ''),
            DeviceID: isBrowser ? 'PC' : isTablet ? 'Tablet' : 'mobile',
        });
        navigate(Screens.NEWS);
    }, [navigate]);

    /**
     * handleClickMenuButton
     * Handle click on [メニューボタン] button
     */
    const handleClickMenuButton = useCallback(() => {
        CommonAPI.commonUserTransitionHistory({
            OriginScreenName: window.location.pathname,
            DestinationScreenName: Screens.MENU,
            ElementName: Utils.t('header.menu')?.replaceAll('\n', ''),
            DeviceID: isBrowser ? 'PC' : isTablet ? 'Tablet' : 'mobile',
        });
        navigate(Screens.MENU);
    }, [navigate]);

    /**
     * handleClickMyPageButton
     */
    const handleClickMyPageButton = useCallback(() => {
        CommonAPI.commonUserTransitionHistory({
            OriginScreenName: window.location.pathname,
            DestinationScreenName: idToken ? Screens.WALLET001 : Screens.LOGIN,
            ElementName: Utils.t('common.text.login')?.replaceAll('\n', ''),
            DeviceID: isBrowser ? 'PC' : isTablet ? 'Tablet' : 'mobile',
        });
        if (idToken) {
            navigate(Screens.WALLET001);
        } else {
            navigate(Screens.LOGIN);
        }
    }, [idToken, navigate]);

    /**
     * handleClickQrCode
     */
    const handleClickQrCode = useCallback(() => {
        CommonAPI.commonUserTransitionHistory({
            OriginScreenName: window.location.pathname,
            DestinationScreenName: Screens.QRCODE_PRESENT001,
            ElementName: Utils.t('header.qr_code')?.replaceAll('\n', ''),
            DeviceID: isBrowser ? 'PC' : isTablet ? 'Tablet' : 'mobile',
        });
        navigate(Screens.QRCODE_PRESENT001);
    }, [navigate]);

    const headerType = useMemo(() => props.type || 'main', [props.type]);
    const headerStyle = useMemo(() => headerStyles[headerType], [headerType]);

    return useMemo(
        () => (
            <Paper
                sx={{
                    position: 'fixed',
                    top: 0,
                    left: 0,
                    right: 0,
                    boxShadow: 'none',
                    background: 'transparent',
                    zIndex: 999,
                    marginLeft: 'calc(100vw - 100%)',
                }}
            >
                <div
                    id="AppHeader"
                    className={`container d-flex align-items-center ${classNameCustom}`}
                    style={{
                        ...headerStyle.container,
                    }}
                >
                    {headerType === 'main' && (
                        <div className="d-flex w-100 align-items-center main-container">
                            <div className="d-flex flex-row back-left-with-title-center-and-menu-right__left">
                                <div
                                    className={clsx(
                                        'd-flex flex-column justify-content-between align-items-center cursor-pointer',
                                        {
                                            invisible: !idToken,
                                        }
                                    )}
                                    onClick={handleClickTopIcon}
                                >
                                    <Icons.HomeIcon width={17.889} height={20} />
                                    <span className="top-text">{Utils.t('common.text.top')}</span>
                                </div>
                            </div>
                            <div className="logo">
                                <FullSizeLogo onLogoClick={onLogoClick} />
                            </div>
                            <div className="d-flex gap-1 position-absolute end-0">
                                <div
                                    className="info-button d-flex flex-column justify-content-between align-items-center cursor-pointer"
                                    onClick={handleClickInfoButton}
                                >
                                    <div className="position-relative d-flex flex-column justify-content-between align-items-center">
                                        <Icons.NotificationIcon />
                                        {unreadFlag && (
                                            <div className={clsx('has-unread')}>
                                                <Icons.UnreadNotificationIcon />
                                            </div>
                                        )}
                                        <span className="top-text">{Utils.t('header.news')}</span>
                                    </div>
                                </div>
                                <div
                                    className="menu-button d-flex flex-column justify-content-between align-items-center cursor-pointer"
                                    onClick={handleClickMenuButton}
                                >
                                    <Icons.MenuIcon height={20} />
                                    <span className="top-text">{Utils.t('header.menu')}</span>
                                </div>
                            </div>
                        </div>
                    )}
                    {headerType === 'closeRightWithTitle' && (
                        <div className="d-flex w-100 align-items-center">
                            <div className="close-icon" />
                            <div
                                className="d-flex w-100 justify-content-center mx-16 header-title title"
                                style={{
                                    ...headerStyle.title,
                                }}
                            >
                                {props.title}
                            </div>
                            <div className="close-icon" onClick={handleOnRightButtonClick}>
                                <Icons.CloseIcon />
                            </div>
                        </div>
                    )}
                    {(headerType === 'withTitle' || headerType === 'titleCenterAndMenuRight') && (
                        <div className="d-flex w-100 align-items-center main-container">
                            <div
                                className="d-flex flex-column justify-content-between align-items-center cursor-pointer"
                                onClick={handleClickTopIcon}
                            >
                                <Icons.HomeIcon width={17.889} height={20} />
                                <span className="top-text">{Utils.t('common.text.top')}</span>
                            </div>
                            <div
                                className="d-flex w-100 justify-content-center title"
                                style={{
                                    ...headerStyle.title,
                                }}
                            >
                                {props.title}
                            </div>
                            <div className="d-flex gap-1 position-absolute end-0">
                                <div
                                    className="info-button d-flex flex-column justify-content-between align-items-center cursor-pointer"
                                    onClick={handleClickInfoButton}
                                >
                                    <div className="position-relative d-flex flex-column justify-content-between align-items-center">
                                        <Icons.NotificationIcon />
                                        {unreadFlag && (
                                            <div className={clsx('has-unread')}>
                                                <Icons.UnreadNotificationIcon />
                                            </div>
                                        )}
                                        <span className="top-text">{Utils.t('header.news')}</span>
                                    </div>
                                </div>
                                <div
                                    className="menu-button d-flex flex-column justify-content-between align-items-center cursor-pointer"
                                    onClick={handleClickMenuButton}
                                >
                                    <Icons.MenuIcon height={20} />
                                    <span className="top-text">{Utils.t('header.menu')}</span>
                                </div>
                            </div>
                        </div>
                    )}
                    {headerType === 'withTitleOnly' && (
                        <div className="d-flex w-100 align-items-center main-container">
                            <div
                                className="d-flex w-100 justify-content-center title"
                                style={{
                                    ...headerStyle.title,
                                }}
                            >
                                {props.title}
                            </div>
                        </div>
                    )}
                    {headerType === 'withTitleAndTranslate' && (
                        <div className="d-flex w-100 align-items-center translate-container">
                            <div
                                className="d-flex flex-column justify-content-between align-items-center cursor-pointer"
                                onClick={handleClickTopIcon}
                            >
                                <Icons.HomeIcon width={17.889} height={20} />
                                <span className="top-text">{Utils.t('common.text.top')}</span>
                            </div>
                            <div
                                className="d-flex w-100 justify-content-center title"
                                style={{
                                    ...headerStyle.title,
                                }}
                            >
                                {props.title}
                            </div>
                            <div className="d-flex gap-1 position-absolute end-0">
                                <div className="button-translate cursor-pointer">
                                    <div className="d-flex flex-column justify-content-between align-items-center">
                                        <Icons.LanguageIcon width={20} height={20} />
                                        <span className="top-text">{Utils.t('common.text.language_choice')}</span>
                                    </div>
                                    <div className="position-absolute top-0" style={{ width: 32 }}>
                                        <LanguageDropdown />
                                    </div>
                                </div>
                                <div
                                    className="info-button d-flex flex-column justify-content-between align-items-center cursor-pointer"
                                    onClick={handleClickInfoButton}
                                >
                                    <div className="position-relative d-flex flex-column justify-content-between align-items-center">
                                        <Icons.NotificationIcon />
                                        {unreadFlag && (
                                            <div className={clsx('has-unread')}>
                                                <Icons.UnreadNotificationIcon />
                                            </div>
                                        )}
                                        <span className="top-text">{Utils.t('header.news')}</span>
                                    </div>
                                </div>
                                <div
                                    className="menu-button d-flex flex-column justify-content-between align-items-center cursor-pointer"
                                    onClick={handleClickMenuButton}
                                >
                                    <Icons.MenuIcon height={20} />
                                    <span className="top-text">{Utils.t('header.menu')}</span>
                                </div>
                            </div>
                        </div>
                    )}
                    {headerType === 'mainTranslate' && (
                        <div className="d-flex w-100 align-items-center translate-container">
                            <div
                                className="d-flex flex-column justify-content-between align-items-center cursor-pointer"
                                onClick={handleClickTopIcon}
                            >
                                <Icons.HomeIcon width={17.889} height={20} />
                                <span className="top-text">{Utils.t('common.text.top')}</span>
                            </div>
                            <div className="logo">
                                <FullSizeLogo onLogoClick={onLogoClick} />
                            </div>
                            <div className="d-flex gap-1 position-absolute end-0">
                                <div className="button-translate cursor-pointer">
                                    <div className="d-flex flex-column justify-content-between align-items-center">
                                        <Icons.LanguageIcon width={20} height={20} />
                                        <span className="top-text">{Utils.t('common.text.language_choice')}</span>
                                    </div>
                                    <div className="position-absolute top-0" style={{ width: 32 }}>
                                        <LanguageDropdown />
                                    </div>
                                </div>
                                <div
                                    className="info-button d-flex flex-column justify-content-between align-items-center cursor-pointer"
                                    onClick={handleClickInfoButton}
                                >
                                    <div className="position-relative d-flex flex-column justify-content-between align-items-center">
                                        <Icons.NotificationIcon />
                                        {unreadFlag && (
                                            <div className={clsx('has-unread')}>
                                                <Icons.UnreadNotificationIcon />
                                            </div>
                                        )}
                                        <span className="top-text">{Utils.t('header.news')}</span>
                                    </div>
                                </div>
                                <div
                                    className="menu-button d-flex flex-column justify-content-between align-items-center cursor-pointer"
                                    onClick={handleClickMenuButton}
                                >
                                    <Icons.MenuIcon height={20} />
                                    <span className="top-text">{Utils.t('header.menu')}</span>
                                </div>
                            </div>
                        </div>
                    )}
                    {(headerType === 'backLeftWithTitle' || headerType === 'backLeftTitleCenterAndMenuRight') && (
                        <div className="d-flex w-100 align-items-center main-container">
                            {!isHiddenBackButton && (
                                <div
                                    className="back-icon d-flex justify-content-center align-items-center px-16 cursor-pointer"
                                    onClick={handleOnLeftButtonClick}
                                >
                                    <Icons.BackIcon />
                                </div>
                            )}
                            <div
                                className="d-flex justify-content-center title position-absolute"
                                style={{
                                    ...headerStyle.title,
                                }}
                            >
                                {props.title}
                            </div>
                            {!isHiddenMenuNotification && (
                                <div className="d-flex gap-1 position-absolute end-0 pr-16 position-relative">
                                    <div
                                        className="info-button d-flex flex-column justify-content-between align-items-center cursor-pointer"
                                        onClick={handleClickInfoButton}
                                    >
                                        <div className="position-relative d-flex flex-column justify-content-between align-items-center">
                                            <Icons.NotificationIcon />
                                            {unreadFlag && (
                                                <div className={clsx('has-unread')}>
                                                    <Icons.UnreadNotificationIcon />
                                                </div>
                                            )}
                                            <span className="top-text">{Utils.t('header.news')}</span>
                                        </div>
                                    </div>
                                    <div
                                        className="menu-button d-flex flex-column justify-content-between align-items-center cursor-pointer"
                                        onClick={handleClickMenuButton}
                                    >
                                        <Icons.MenuIcon height={20} />
                                        <span className="top-text">{Utils.t('header.menu')}</span>
                                    </div>
                                </div>
                            )}
                        </div>
                    )}
                    {headerType === 'withMyPage' && (
                        <div className="d-flex w-100 align-items-center justify-content-between position-relative">
                            <FullSizeLogo onLogoClick={onLogoClick} />

                            <div className="d-flex align-items-center gap-3 position-absolute end-0">
                                <div className="button-translate cursor-pointer">
                                    <div className="d-flex flex-column justify-content-between align-items-center">
                                        <Icons.LanguageIcon width={21} height={21} />
                                        <span className="top-text">{Utils.t('common.text.language_choice')}</span>
                                    </div>
                                    <div className="position-absolute top-0" style={{ width: 32 }}>
                                        <LanguageDropdown />
                                    </div>
                                </div>
                                <div className="my-page-container">
                                    <span className="click-here-user">
                                        {Utils.t('common.text.click_here_for_user')}
                                    </span>
                                    <div className="my-page-btn cursor-pointer" onClick={handleClickMyPageButton}>
                                        <div className="d-flex align-items-center ">
                                            <Icons.MyPage />
                                        </div>
                                        <Typography className="ff-noto-bold w-100" fontSize={10} color={'#ffffff'}>
                                            {Utils.t('common.text.login')}
                                        </Typography>
                                    </div>
                                </div>
                            </div>
                        </div>
                    )}
                    {headerType === 'backLeftWithTitleNotiMenu' && (
                        <div className="d-flex w-100 align-items-center main-container">
                            <div
                                className="back-icon d-flex justify-content-center align-items-center px-16 cursor-pointer"
                                onClick={onLeftButtonClick}
                            >
                                <Icons.BackIcon />
                            </div>
                            <div
                                className="d-flex justify-content-center title position-absolute"
                                style={{
                                    ...headerStyle.title,
                                }}
                            >
                                {props.title}
                            </div>
                            <div className="d-flex gap-1 position-absolute end-0 pr-16 position-relative">
                                <div className="d-flex flex-column justify-content-between align-items-center cursor-pointer">
                                    <div
                                        className="position-relative d-flex flex-column justify-content-between align-items-center"
                                        onClick={handleClickInfoButton}
                                    >
                                        <Icons.NotificationIcon />
                                        {unreadFlag && (
                                            <div className={clsx('has-unread')}>
                                                <Icons.UnreadNotificationIcon />
                                            </div>
                                        )}
                                        <span className="top-text">{Utils.t('header.news')}</span>
                                    </div>
                                </div>
                                <div
                                    className="d-flex flex-column justify-content-between align-items-center cursor-pointer"
                                    onClick={handleClickMenuButton}
                                >
                                    <Icons.MenuIcon height={20} />
                                    <span className="top-text">{Utils.t('header.menu')}</span>
                                </div>
                            </div>
                        </div>
                    )}
                    {headerType === 'backLeftWithTitleMenu' && (
                        <div className="d-flex w-100 align-items-center main-container">
                            <div
                                className="back-icon d-flex justify-content-center align-items-center px-16 cursor-pointer"
                                onClick={onLeftButtonClick}
                            >
                                <Icons.BackIcon />
                            </div>
                            <div
                                className="d-flex justify-content-center title position-absolute"
                                style={{
                                    ...headerStyle.title,
                                }}
                            >
                                {props.title}
                            </div>
                            <div className="d-flex gap-1 position-absolute end-0 pr-16 position-relative">
                                <div
                                    className="d-flex flex-column justify-content-between align-items-center cursor-pointer"
                                    onClick={handleClickMenuButton}
                                >
                                    <Icons.MenuIcon height={20} />
                                    <span className="top-text">{Utils.t('header.menu')}</span>
                                </div>
                            </div>
                        </div>
                    )}
                    {headerType === 'mainWithQrCode' && (
                        <div className="d-flex w-100 align-items-center main-container">
                            <div className="d-flex gap-1 position-absolute start-0 gap-3">
                                <div
                                    className={clsx(
                                        'd-flex flex-column justify-content-between align-items-center cursor-pointer',
                                        {
                                            invisible: !idToken,
                                        }
                                    )}
                                    onClick={handleClickTopIcon}
                                >
                                    <Icons.HomeIcon width={17.889} height={20} />
                                    <span className="top-text">{Utils.t('common.text.top')}</span>
                                </div>
                                <div
                                    className={clsx(
                                        'qr-code-container d-flex flex-column justify-content-between align-items-center cursor-pointer',
                                        {
                                            invisible: !idToken,
                                        }
                                    )}
                                    onClick={handleClickQrCode}
                                >
                                    <Icons.HeaderQrCode width={24} height={24} />
                                    <span className="top-text">{Utils.t('header.qr_code')}</span>
                                </div>
                            </div>
                            <div className="logo">
                                <FullSizeLogo onLogoClick={onLogoClick} />
                            </div>
                            <div
                                className={clsx('d-flex gap-1 position-absolute end-0', {
                                    invisible: !idToken,
                                })}
                            >
                                <div
                                    className="info-button d-flex flex-column justify-content-between align-items-center cursor-pointer"
                                    onClick={handleClickInfoButton}
                                >
                                    <div className="position-relative d-flex flex-column justify-content-between align-items-center">
                                        <Icons.NotificationIcon />
                                        {unreadFlag && (
                                            <div className={clsx('has-unread')}>
                                                <Icons.UnreadNotificationIcon />
                                            </div>
                                        )}
                                        <span className="top-text">{Utils.t('header.news')}</span>
                                    </div>
                                </div>
                                <div
                                    className="menu-button d-flex flex-column justify-content-between align-items-center cursor-pointer"
                                    onClick={handleClickMenuButton}
                                >
                                    <Icons.MenuIcon height={20} />
                                    <span className="top-text">{Utils.t('header.menu')}</span>
                                </div>
                            </div>
                        </div>
                    )}
                    {headerType === 'withFullSizeLogoOnly' && (
                        <div className="d-flex w-100 align-items-center main-container">
                            <div className="logo">
                                <FullSizeLogo onLogoClick={onLogoClick} />
                            </div>
                        </div>
                    )}
                </div>
            </Paper>
        ),
        [
            classNameCustom,
            headerStyle.container,
            headerStyle.title,
            headerType,
            idToken,
            handleClickTopIcon,
            onLogoClick,
            handleClickInfoButton,
            unreadFlag,
            handleClickMenuButton,
            props.title,
            handleOnRightButtonClick,
            isHiddenBackButton,
            handleOnLeftButtonClick,
            isHiddenMenuNotification,
            handleClickMyPageButton,
            onLeftButtonClick,
            handleClickQrCode,
        ]
    );
};

export default memo(Header);

/**
 * useCommonHeader
 * @param useHeader boolean
 */
export const useCommonHeader = (useHeader?: boolean): { height?: number } => {
    const [height, setHeight] = useState<number | undefined>();

    useEffect(() => {
        const target = document.getElementById('AppHeader');
        if (target && useHeader) {
            Utils.createAppObserver((entries) => {
                const { contentRect } = _.last(entries) as ResizeObserverEntry;
                if (contentRect) {
                    setHeight(contentRect.height);
                }
            }).observe(target);
        }
    }, [useHeader]);

    return useMemo(
        () => ({
            height,
        }),
        [height]
    );
};
