import { CommonCallback } from './common';

/**
 * PayMulti005MainExecutePaymentItems
 */
export interface PayMulti005MainExecutePaymentItems {
    // メダルサービスID
    MedalServiceID: string;
    // 支払い金額
    PaymentAmount: number;
}

/**
 * PayMulti005MainExecutePaymentModel
 */
export interface PayMulti005MainExecutePaymentModel extends CommonCallback {
    // 加盟店ID
    StoreID: string;
    // 支払い合計金額
    TotalPaymentAmount: number;
    // Items
    Items: PayMulti005MainExecutePaymentItems[];
    // 売上区分
    SaleType?: string;
    // その他付与データ
    ExtendParameter?: {
        SalesID?: string;
    };
    // オーダーID
    OrderID: string;
}

/**
 * PayMulti005MainExecutePaymentResult
 */
export interface PayMulti005MainExecutePaymentResult {
    LinkedMedalList: LinkedMedalListItem[];
}

/**
 * LinkedMedalListItem
 */
export interface LinkedMedalListItem {
    // ポイント名
    LinkedMedalName: string;
    // 付与ポイント
    LinkedMedalAmount: number;
}
