import API from '../constants/api';
import Environments from '../constants/environments';
import { BaseResponse } from '../models/common';
import { ECCart001ECDeleteItemModel } from '../models/eccart001-ec-delete-item';
import { ECCart001ECGetCartModel, ECCart001ECGetCartResult } from '../models/eccart001-ec-get-cart';
import { ECItem001ECGetItemListModel, ECItem001ECGetItemListResult } from '../models/ecitem001-ec-get-itemlist';
import { ECItem002ECGetItemModel, ECItem002ECGetItemResult } from '../models/ecitem002-ec-get-item';
import { ECItem002ECUpdateCartModel } from '../models/ecitem002-ec-update-cart';
import {
    ECPayment001ECCheckBeforePaymentModel,
    ECPayment001ECCheckBeforePaymentResult,
} from '../models/ecpayment001-ec-check-beforepayment';
import {
    ECPayment001ECGetUpdateDateTimeModel,
    ECPayment001ECGetUpdateDateTimeResult,
} from '../models/ecpayment001-ec-get-updatedatetime';
import {
    ECPurchase001ECGetPurchaseProcessModel,
    ECPurchase001ECGetPurchaseProcessResult,
} from '../models/ecpurchase001-ec-get-purchaseprocess';
import { ECPurchase001ECUpdatePurchaseProcessModel } from '../models/ecpurchase001-ec-update-purchaseprocess';
import {
    ECPurchase002ECGetPurchaseHistoryListModel,
    ECPurchase002ECGetPurchaseHistoryListResult,
} from '../models/ecpurchase002-ec-get-purchasehistorylist';
import createAPI from './baseApi';

class ECApi {
    /**
     * ecItem001ECGetItemList
     * @param data ECItem001ECGetItemListModel
     * @returns Promise<BaseResponse<ECItem001ECGetItemListResult>>
     */
    static ecItem001ECGetItemList = (
        data: ECItem001ECGetItemListModel
    ): Promise<BaseResponse<ECItem001ECGetItemListResult>> => {
        return createAPI({
            url: API.ECITEM001_EC_GET_ITEMLIST,
            data: {
                // set default brandID
                BrandID: Environments.brandID,
                ...data,
            },
        });
    };

    /**
     * ecItem002ECGetItem
     * @param data ECItem002ECGetItemModel
     * @returns Promise<BaseResponse<ECItem002ECGetItemResult>>
     */
    static ecItem002ECGetItem = (data: ECItem002ECGetItemModel): Promise<BaseResponse<ECItem002ECGetItemResult>> => {
        return createAPI({
            url: API.ECITEM002_EC_GET_ITEM,
            data: {
                // set default brandID
                BrandID: Environments.brandID,
                ...data,
            },
        });
    };

    /**
     * ecItem002ECUpdateCart
     * @param data ECItem002ECUpdateCartModel
     * @returns Promise<BaseResponse>
     */
    static ecItem002ECUpdateCart = (data: ECItem002ECUpdateCartModel): Promise<BaseResponse> => {
        return createAPI({
            url: API.ECITEM002_EC_UPDATE_CART,
            data,
        });
    };

    /**
     * ecCart001ECGetCart
     * @param data ECCart001ECGetCartModel
     * @returns Promise<BaseResponse<ECCart001ECGetCartResult>>
     */
    static ecCart001ECGetCart = (data?: ECCart001ECGetCartModel): Promise<BaseResponse<ECCart001ECGetCartResult>> => {
        return createAPI({
            url: API.ECCART001_EC_GET_CART,
            data,
        });
    };

    /**
     * ecPayment001ECCheckBeforePayment
     * @param data ECPayment001ECCheckBeforePaymentModel
     * @returns Promise<BaseResponse<ECPayment001ECCheckBeforePaymentResult>>
     */
    static ecPayment001ECCheckBeforePayment = (
        data: ECPayment001ECCheckBeforePaymentModel
    ): Promise<BaseResponse<ECPayment001ECCheckBeforePaymentResult>> => {
        return createAPI({
            url: API.ECPAYMENT001_EC_CHECK_BEFOREPAYMENT,
            data,
        });
    };

    /**
     * ecCart001ECDeleteItem
     * @param data ECCart001ECDeleteItemModel
     * @returns Promise<BaseResponse>
     */
    static ecCart001ECDeleteItem = (data: ECCart001ECDeleteItemModel): Promise<BaseResponse> => {
        return createAPI({
            url: API.ECCART001_EC_DELETE_ITEM,
            data,
        });
    };

    /**
     * ecPurchase001ECGetPurchaseProcess
     * @param data ECPurchase001ECGetPurchaseProcessModel
     * @returns Promise<BaseResponse<ECPurchase001ECGetPurchaseProcessResult>
     */
    static ecPurchase001ECGetPurchaseProcess = (
        data: ECPurchase001ECGetPurchaseProcessModel
    ): Promise<BaseResponse<ECPurchase001ECGetPurchaseProcessResult>> => {
        return createAPI({
            url: API.ECPURCHASE001_EC_GET_PURCHASEPROCESS,
            data,
        });
    };

    /**
     * ecPayment001ECGetUpdateDateTime
     * @param data ECPayment001ECGetUpdateDateTimeModel
     * @returns Promise<BaseResponse<ECPayment001ECGetUpdateDateTimeResult>>
     */
    static ecPayment001ECGetUpdateDateTime = (
        data: ECPayment001ECGetUpdateDateTimeModel
    ): Promise<BaseResponse<ECPayment001ECGetUpdateDateTimeResult>> => {
        return createAPI({
            url: API.ECPAYMENT001_EC_GET_UPDATEDATETIME,
            data,
        });
    };

    /**
     * ecPurchase001ECUpdatePurchaseProcess
     * @param data ECPurchase001ECUpdatePurchaseProcessModel
     * @returns Promise<BaseResponse>
     */
    static ecPurchase001ECUpdatePurchaseProcess = (
        data: ECPurchase001ECUpdatePurchaseProcessModel
    ): Promise<BaseResponse> => {
        return createAPI({
            url: API.ECPURCHASE001_EC_UPDATE_PURCHASEPROCESS,
            data,
        });
    };

    /**
     * ecPurchase002ECGetPurchaseHistoryList
     * @param data ECPurchase002ECGetPurchaseHistoryListModel
     * @returns Promise<BaseResponse<ECPurchase002ECGetPurchaseHistoryListResult>>
     */
    static ecPurchase002ECGetPurchaseHistoryList = (
        data: ECPurchase002ECGetPurchaseHistoryListModel
    ): Promise<BaseResponse<ECPurchase002ECGetPurchaseHistoryListResult>> => {
        return createAPI({
            url: API.ECPURCHASE002_EC_GET_PURCHASEHISTORYLIST,
            data,
        });
    };
}

export default ECApi;
