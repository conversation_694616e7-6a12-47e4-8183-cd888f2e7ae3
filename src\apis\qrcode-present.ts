import { GenericAbortSignal } from 'axios';
import API from '../constants/api';
import { BaseResponse } from '../models/common';
import {
    QrCodePresent001InitCreateTransferCodeModel,
    QrCodePresent001InitCreateTransferCodeResult,
} from '../models/qrcodepresent001-init-create-transfercode';
import {
    QrCodePresent001InitGetResult,
    QrCodePresent001InitGetResultModel,
} from '../models/qrcodepresent001-init-get-result';
import createAPI from './baseApi';

class QrCodePresent {
    /**
     * qrCodePresent001InitCreateTransferCode
     * @param data QrCodePresent001InitCreateTransferCodeModel
     * @returns Promise<BaseResponse<QrCodePresent001InitCreateTransferCodeResult>>
     */
    static qrCodePresent001InitCreateTransferCode = (
        data: QrCodePresent001InitCreateTransferCodeModel
    ): Promise<BaseResponse<QrCodePresent001InitCreateTransferCodeResult>> => {
        return createAPI({
            url: API.QRCODEPRESENT001_INIT_CREATE_TRANSFERCODE,
            data,
        });
    };

    /**
     * qrCodePresent001InitGetResult
     * @param data QrCodePresent001InitGetResultModel
     * @returns Promise<BaseResponse<QrCodePresent001InitGetResult>>
     */
    static qrCodePresent001InitGetResult = (
        data: QrCodePresent001InitGetResultModel,
        signal?: GenericAbortSignal
    ): Promise<BaseResponse<QrCodePresent001InitGetResult>> => {
        return createAPI({
            url: API.QRCODEPRESENT001_INIT_GET_RESULT,
            data,
            signal,
        });
    };
}

export default QrCodePresent;
