import { CommonCallback } from './common';

/**
 * ECItem001ECGetItemListModel
 */
export interface ECItem001ECGetItemListModel extends CommonCallback {
    LastSortNo: number;
    SearchText?: string;
    LimitCount: number;
    BrandID?: string;
}

/**
 * ECItem001ECGetItem
 */
export interface ECItem001ECGetItem {
    ItemID: string;
    SortNo: number;
    ImageFileName: string;
    ItemName: string;
    TaxPrice: number;
}

/**
 * ECItem001ECGetItemListResult
 */
export interface ECItem001ECGetItemListResult {
    ItemList: ECItem001ECGetItem[];
    IsNextDataExists: boolean;
}
