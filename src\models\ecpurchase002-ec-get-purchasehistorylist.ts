import { CommonCallback } from './common';

/**
 * ECPurchase002ECGetPurchaseHistoryListModel
 */
export interface ECPurchase002ECGetPurchaseHistoryListModel extends CommonCallback {
    LastSalesDateTime?: string;
    LimitCount: number;
}

/**
 * SalesDetailItem
 */
export interface SalesDetailItem {
    ItemID: string;
    ItemName: string;
    ItemImage: string;
    SalesDateTime: string;
}

/**
 * ECPurchase002ECGetPurchaseHistoryListResult
 */
export interface ECPurchase002ECGetPurchaseHistoryListResult {
    IsNextDataExists: boolean;
    SalesDetailList: SalesDetailItem[];
}
