import { CommonCallback } from './common';

/**
 * Wallet001MainGetPayableFlagModel
 */
export interface Wallet001MainGetPayableFlagModel extends CommonCallback {
    MedalServiceIDList?: MedalServiceIDItems[];
}
/**
 * MedalServiceIDItems
 */
export interface MedalServiceIDItems {
    MedalServiceID: string;
}

/**
 * Wallet001MainGetPayableFlagResult
 */
export interface Wallet001MainGetPayableFlagResult {
    // 支払いボタンの有効フラグ
    PaymentButtonEnableFlag: boolean;
    // 支払い開始日
    PaymentStartDate: string;
    // 支払い終了日
    PaymentEndDate: string;
    // メダルサービス開始日
    MedalServiceStartDate: string;
    // メダルサービス終了日
    MedalServiceEndDate: string;
}
