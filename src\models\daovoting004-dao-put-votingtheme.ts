import { CommonCallback } from './common';

/**
 * DAOVoting004DaoPutVotingThemeModel
 */
export interface DAOVoting004DaoPutVotingThemeModel extends CommonCallback {
    // DAOID
    DaoID: string;
    // タイトル
    Title: string;
    // 内容説明
    Explanation: string;
    // 投票形式コード
    VotingFormatCode: number;
    // 投票開始日時
    VotingStartDateTime: string;
    // 投票終了日時
    VotingEndDateTime: string;
    // 投票成立数（0の場合もあり）
    VotingSuccessCount: number;
    // 1票換算GT
    ConversionGT: number;
    // 通常画像ファイル名
    NormalVotingImageFileName?: string;
    // 投票候補リスト
    VotingCandidateList?: DAOVoting004VotingCandidateItem[];
}

/**
 * DAOVoting004VotingCandidateItem
 */
export interface DAOVoting004VotingCandidateItem {
    // 投票候補内容
    CandidateContent: string;
    // 画像ファイル名
    ImageFileName?: string;
}

/**
 * DAOVoting004DaoPutVotingThemeResult
 */
export interface DAOVoting004DaoPutVotingThemeResult {
    // テーマID
    ThemeID: string;
}
