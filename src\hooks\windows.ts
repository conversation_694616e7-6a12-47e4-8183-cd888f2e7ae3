import { useCallback, useEffect, useMemo, useState } from 'react';

/**
 * WindowSize
 */
interface WindowSize {
    /**
     * width
     */
    width: number;
    /**
     * height
     */
    height: number;
}

/**
 * useWindows
 */
const useWindows = (): { isOnline: boolean; windowSize: WindowSize } => {
    const [isOnline, setOnline] = useState<boolean>(navigator?.onLine);

    const [windowSize, setWindowSize] = useState<WindowSize>({
        width: window.innerWidth,
        height: window.innerHeight,
    });

    /**
     * onWindowResizeHandler
     */
    const onWindowResizeHandler = useCallback(() => {
        setWindowSize({
            width: window.innerWidth,
            height: window.innerHeight,
        });
    }, []);

    /**
     * onOnlineEventHandler
     */
    const onOnlineEventHandler = useCallback(() => {
        setOnline(true);
    }, []);

    /**
     * onOfflineEventHandler
     */
    const onOfflineEventHandler = useCallback(() => {
        setOnline(false);
    }, []);

    useEffect(() => {
        onWindowResizeHandler();

        // add event listener for online, offline event
        window.addEventListener('online', onOnlineEventHandler);
        window.addEventListener('offline', onOfflineEventHandler);

        // add listener for window resize
        window.addEventListener('resize', onWindowResizeHandler);

        return (): void => {
            // remove listener
            window.removeEventListener('resize', onWindowResizeHandler);
            window.removeEventListener('online', onOnlineEventHandler);
            window.removeEventListener('offline', onOfflineEventHandler);
        };
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);

    return useMemo(
        () => ({
            isOnline,
            windowSize,
        }),
        [isOnline, windowSize]
    );
};

export default useWindows;
