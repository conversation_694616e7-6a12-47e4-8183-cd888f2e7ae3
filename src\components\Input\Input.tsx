import clsx from 'clsx';
import _ from 'lodash';
import React, {
    ChangeEvent,
    FocusEvent,
    FormEvent,
    ForwardedRef,
    InputHTMLAttributes,
    forwardRef,
    useCallback,
    useEffect,
    useImperativeHandle,
    useMemo,
    useRef,
    useState,
} from 'react';
import Utils from '../../utils/utils';
import { CommonError, RequiredLabel, RequiredLabelProps } from '../Common';
import Icons from '../Icons/Icons';
import './styles.scss';

/**
 * UseNumberFormat
 */
interface UseNumberFormat {
    /**
     * separator
     */
    separator?: ',' | '.';
    /**
     * maxValue
     */
    maxValue?: number;
    /**
     * equalMaxValue
     * available when maxValue is input
     */
    equalMaxValue?: boolean;
    /**
     * allowLeadingZero
     */
    allowLeadingZero?: boolean;
    /**
     * multiple to 1000
     */
    x1000?: boolean;
}

// OmittedInputElement
type OmittedInputElement = Omit<InputHTMLAttributes<HTMLInputElement>, 'onChange' | 'prefix'>;

/**
 * InputProps
 */
export interface InputProps extends Partial<RequiredLabelProps>, OmittedInputElement {
    /**
     * input's sub-label (under label)
     */
    subLabel?: string | React.ReactNode;
    /**
     *  text under input
     */
    textUnderneath?: string | React.ReactNode;
    /**
     * input's variant
     */
    variant?: string;
    /**
     * error
     */
    error?: string | boolean;
    /**
     * showError
     */
    showError?: boolean;
    /**
     * input's right component (outside input)
     */
    rightComponent?: React.ReactNode;
    /**
     * input's bottom component (outside input)
     */
    bottomComponent?: React.ReactNode;
    /**
     * useNumberFormat
     */
    useNumberFormat?: boolean | UseNumberFormat;
    /**
     * input's prefix (inside input)
     */
    prefix?: React.ReactNode;
    /**
     * input's suffix (inside input)
     */
    suffix?: React.ReactNode;
    /**
     * onChange
     * @param event ChangeEvent<HTMLInputElement> | undefined
     * @returns void
     */
    onChange?: (event: ChangeEvent<HTMLInputElement>) => void;
}

/**
 * InputForwardedRef
 */
export interface InputForwardedRef {
    /**
     * inputRef
     * input's references
     */
    inputRef: React.RefObject<HTMLInputElement>;
}

/**
 * Common Input component
 */
const Input = forwardRef((props: InputProps, ref: ForwardedRef<InputForwardedRef>): React.JSX.Element => {
    const {
        value: inputValue = '',
        name,
        label,
        subLabel,
        textUnderneath,
        required,
        type,
        error,
        showError = true,
        rightComponent,
        bottomComponent,
        useNumberFormat,
        prefix,
        suffix,
        pattern,
        onChange,
        onBlur,
        maxLength,
        ...rest
    } = props || {};

    // input ref
    const inputRef = useRef<HTMLInputElement>(null);

    // input type
    const [inputType, setInputType] = useState(type);

    // current value of input
    const [value, setValue] = useState(inputValue);

    const multiplyRef = useRef<typeof inputValue>(0);

    // numeric value
    const numericValue = useRef<string>(inputValue as string);
    const previousNumberValue = useRef<string>(inputValue as string);

    useEffect(() => {
        if (useNumberFormat) {
            const { x1000 } = useNumberFormat as UseNumberFormat;

            if (x1000) {
                multiplyRef.current = Number(Utils.removeNotDigit(String(inputValue))) / 1000;
            }
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [useNumberFormat]);

    useEffect(() => {
        setValue(inputValue);
    }, [inputValue]);

    useImperativeHandle(
        ref,
        () => ({
            inputRef,
        }),
        []
    );

    /**
     * toggleShowPassword
     */
    const toggleShowPassword = useCallback(() => {
        if (type === 'password') {
            if (inputType === 'password') {
                setInputType('text');
            } else {
                setInputType('password');
            }
        }
    }, [inputType, type]);

    /**
     * handleChange
     * @param event ChangeEvent<HTMLInputElement>
     */
    const handleChange = useCallback(
        (event: ChangeEvent<HTMLInputElement>) => {
            if (useNumberFormat) {
                const nextValue = numericValue.current;

                // update value
                setValue(nextValue);
                event.target.value = nextValue;
                event.currentTarget.value = nextValue;

                // emit change event
                onChange?.({
                    ...event,
                });

                return;
            }

            // get value from target
            const nextValue = event.target.value;

            // if pattern is input
            if (pattern) {
                // create regex pattern
                const regex = new RegExp(pattern, 'g');

                // remove character not match with regex
                const value = (nextValue.match(regex) || []).join('');

                // update value
                setValue(value.slice(0, maxLength));

                // update value in event target
                event.target.value = value;
                event.currentTarget.value = value;
            } else {
                // otherwise, set next value
                setValue(nextValue.slice(0, maxLength));
            }

            event.target.value = event.target.value?.slice(0, maxLength);
            event.currentTarget.value = event.currentTarget.value?.slice(0, maxLength);
            // dispatch value and target to parent view
            onChange?.(event);
        },
        [maxLength, onChange, pattern, useNumberFormat]
    );

    /**
     * handleBlur
     * handle blur input field
     */
    const handleBlur = useCallback(
        (event: FocusEvent<HTMLInputElement, Element>) => {
            onBlur?.(event);
        },
        [onBlur]
    );

    /**
     * handleBeforeInput
     * @param event FormEvent<HTMLInputElement> & { nativeEvent: InputEvent }
     */
    const handleBeforeInput = useCallback(
        (event: FormEvent<HTMLInputElement> & { nativeEvent: InputEvent }): void => {
            // currently, handle only for input number
            if (useNumberFormat) {
                const {
                    separator,
                    maxValue,
                    equalMaxValue,
                    allowLeadingZero = true,
                    x1000,
                } = useNumberFormat as UseNumberFormat;

                let currentValue = event.currentTarget.value;
                const inputValue = (event.nativeEvent as InputEvent)?.data ?? '';

                // if x1000 is passed and currentValue is available
                if (x1000 && currentValue) {
                    const value = Number(Utils.removeNotDigit(currentValue));
                    currentValue = String(value / 1000);
                }

                // keep previous number value
                previousNumberValue.current = currentValue;

                /**
                 * /^\d+$/gm: Check only number (0-9) can input
                 * if maxLength is passed, prevent input out of length
                 */
                if (/^\d+$/gm.test(inputValue) && (!maxLength || (maxLength && currentValue.length < maxLength))) {
                    const posStart = event.currentTarget.selectionStart || 0;
                    const posEnd = event.currentTarget.selectionEnd || posStart;

                    let value = `${currentValue.slice(0, posStart)}${inputValue}${currentValue.slice(posEnd)}`;

                    // when allowLeadingZero false
                    if (Boolean(allowLeadingZero) === false) {
                        // remove first 0
                        value = value.replace(/^(0)/g, '');
                    }

                    if (x1000) {
                        value = String(Number(Utils.removeNotDigit(value)) * 1000);
                    }

                    // when maxValue is passed
                    if (!_.isNil(maxValue) && Number(Utils.removeNotDigit(value)) > maxValue) {
                        if (equalMaxValue) {
                            value = String(maxValue);
                        } else {
                            value = previousNumberValue.current;
                        }
                    }

                    // update value
                    numericValue.current = separator ? Utils.formatNumber(value, separator) : value;
                }
            }
        },
        [maxLength, useNumberFormat]
    );

    /**
     * handleKeyDown
     * @param event React.KeyboardEvent<HTMLInputElement>
     */
    const handleKeyDown = useCallback(
        (event: React.KeyboardEvent<HTMLInputElement>) => {
            if (useNumberFormat) {
                const { separator, x1000 } = useNumberFormat as UseNumberFormat;
                if (['Backspace'].includes(event.key)) {
                    const currentValue = numericValue.current;

                    const initPos = x1000 ? 3 : 0;
                    const length = currentValue.length;

                    let posStart = event.currentTarget.selectionStart || 0;
                    let posEnd = event.currentTarget.selectionEnd || posStart;

                    posStart = Math.min(posStart, length - initPos);
                    posEnd = Math.min(posEnd, length - initPos);

                    if (posStart === posEnd) {
                        // to prevent slice from zero to negative position
                        posStart = Math.max(posStart - 1, 0);
                    }
                    const c = currentValue.slice(posStart, posEnd);
                    if (c === separator) {
                        posStart -= 1;
                        posEnd -= 1;
                    }
                    const value = `${currentValue.slice(0, posStart)}${currentValue.slice(posEnd)}`;
                    let nValue = Utils.removeNotDigit(value);
                    if (nValue?.length <= 3 && x1000) {
                        nValue = '';
                    }
                    numericValue.current = separator ? Utils.formatNumber(nValue, separator) : nValue;
                }
            }
        },
        [useNumberFormat]
    );

    return useMemo(
        () => (
            <div className="d-flex flex-column common-input-component">
                {(label || subLabel) && (
                    <div className="d-flex flex-column label-session">
                        {label && <RequiredLabel label={label} required={required} optional={!required} />}
                        {subLabel && <div className="sub-label">{subLabel}</div>}
                    </div>
                )}

                <div className="d-flex flex-column">
                    <div className="d-flex flex-row align-items-center">
                        <div
                            className={clsx('main-input d-flex align-items-center', {
                                error: Boolean(error),
                            })}
                        >
                            {prefix}
                            <input
                                {...rest}
                                name={name}
                                ref={inputRef}
                                value={type === 'password' ? undefined : value}
                                type={inputType}
                                onChange={handleChange}
                                onBlur={handleBlur}
                                autoComplete="off"
                                onBeforeInput={handleBeforeInput}
                                onKeyDown={handleKeyDown}
                                inputMode={useNumberFormat ? 'tel' : 'text'}
                                maxLength={maxLength}
                            />
                            {suffix}
                            {type === 'password' && (
                                <div
                                    onClick={toggleShowPassword}
                                    className="d-flex justify-content-center align-items-center password-field"
                                >
                                    {inputType === 'password' ? <Icons.OpenEyeIcon /> : <Icons.ClosedEyeIcon />}
                                </div>
                            )}
                        </div>
                        {rightComponent}
                    </div>
                    {bottomComponent}
                    <div className="d-flex justify-content-between align-items-baseline">
                        {showError && <CommonError error={error} componentId={name} />}
                        {textUnderneath && <div className="text-underneath">{textUnderneath}</div>}
                    </div>
                </div>
            </div>
        ),
        [
            label,
            subLabel,
            required,
            error,
            prefix,
            rest,
            name,
            type,
            value,
            inputType,
            handleChange,
            handleBlur,
            handleBeforeInput,
            handleKeyDown,
            useNumberFormat,
            suffix,
            toggleShowPassword,
            rightComponent,
            bottomComponent,
            maxLength,
            textUnderneath,
            showError,
        ]
    );
});

Input.displayName = 'Input';

export default React.memo(Input);
