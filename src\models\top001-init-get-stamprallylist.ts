import { CommonCallback } from './common';

/**
 * Top001InitGetStampRallyListModel
 */
export interface Top001InitGetStampRallyListModel extends CommonCallback {
    // ブランドID
    BrandID: string;
    // 1ページあたりの取得アイテム数
    PageSize: number;
}

/**
 * Top001InitGetStampRallyItem
 */
export interface Top001InitGetStampRallyItem {
    // スタンプラリーID
    StampRallyID: string;
    // スタンプラリー画像
    StampRallyImage: string;
    // スタンプラリー名
    StampRallyName: string;
    // スタンプラリー開始日
    StartDateTime: string;
    // スタンプラリー終了日
    EndDateTime: string;
    // スタンプラリー説明
    StampRallyDescription: string;
}

/**
 * Top001InitGetStampRallyListResult
 */
export interface Top001InitGetStampRallyListResult {
    // スタンプラリー一覧
    StampRallyList: Top001InitGetStampRallyItem[];
}
