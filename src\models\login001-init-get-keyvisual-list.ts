import { CommonCallback } from './common';

/**
 * Login001InitGetKeyVisualListModel
 */
export interface Login001InitGetKeyVisualListModel extends CommonCallback {
    // ブランドID
    BrandID?: string;
}

/**
 * Login001InitGetKeyVisualListItem
 */
export interface Login001InitGetKeyVisualListItem {
    // キービジュアルID
    KeyVisualID?: string;
    // 画像ファイル名
    FileName: string;
    // リンク先URL
    LinkURL?: string;
}

/**
 * Login001InitGetKeyVisualListResult
 */
export interface Login001InitGetKeyVisualListResult {
    // お知らせ一覧
    KeyVisualList: Login001InitGetKeyVisualListItem[];
}
