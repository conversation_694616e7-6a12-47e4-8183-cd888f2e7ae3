/* eslint-disable @typescript-eslint/explicit-function-return-type */
import { mount, shallow } from 'enzyme';
import { CommonError, RequiredLabel } from '../Common';
import TextArea, { TextAreaProps } from '../TextArea/TextArea';

/**
 * Unit test for Textarea component
 */
describe('Unit test for Textarea component', () => {
    /**
     * setup element
     * @param props TextareaProps
     */
    const setup = (props?: TextAreaProps) => {
        const s = shallow(<TextArea {...props} />);
        const m = mount(<TextArea {...props} />);

        return {
            shallow: s,
            mount: m,
            props,
        };
    };

    /**
     * display enough elements
     */
    describe('display enough elements', () => {
        const { shallow, mount } = setup({
            name: 'testTextarea',
            label: 'textarea label',
            subLabel: 'textarea sub label',
            placeholder: 'textarea placeholder',
            value: 'textarea value',
            error: 'textarea error',
            className: 'textarea-class',
            rows: 3,
        });

        /**
         * require name
         */
        it('require name', () => {
            expect(mount.at(0).props().name).toEqual('testTextarea');
        });

        /**
         * display label
         */
        it('display label', () => {
            expect(shallow.find(RequiredLabel).length).toEqual(1);
            expect(mount.find(RequiredLabel).find('p.label').at(0).text()).toEqual('textarea label');
        });

        /**
         * display 必須 when pass required
         */
        it('display 必須 when pass required', () => {
            const { mount } = setup({
                label: 'label',
                required: true,
            });
            expect(mount.find('.required-container').text()).toEqual('必須');
        });

        /**
         * display 任意 when required is false or empty
         */
        it('display 任意 when required is false or empty', () => {
            const { mount: mount1 } = setup({
                label: 'label',
                required: false,
            });
            const { mount: mount2 } = setup({
                label: 'label',
            });
            expect(mount1.find('.optional-container').text()).toEqual('任意');
            expect(mount2.find('.optional-container').text()).toEqual('任意');
        });

        /**
         * display sub label
         */
        it('display sub label', () => {
            expect(mount.find('div.sub-label').length).toEqual(1);
            expect(mount.find('div.sub-label').text()).toEqual('textarea sub label');
        });

        /**
         * added placeholder
         */
        it('added placeholder', () => {
            expect(mount.at(0).props().placeholder).toEqual('textarea placeholder');
        });

        /**
         * display value
         */
        it('display value', () => {
            expect(mount.find('textarea').text()).toEqual('textarea value');
        });

        /**
         * display empty value
         */
        it('display empty value', () => {
            const { shallow } = setup({
                label: 'label',
            });
            expect(shallow.find('textarea').length).toEqual(1);
            expect(shallow.find('textarea').text()).toEqual('');
        });

        /**
         * display error
         */
        it('display error', () => {
            expect(mount.find(CommonError).text()).toEqual('textarea error');
        });

        /**
         * added className
         */
        it('added className', () => {
            expect(mount.at(0).props().className).toEqual('textarea-class');
        });

        /**
         * added rows
         */
        it('added rows', () => {
            expect(mount.at(0).props().rows).toEqual(3);
        });
    });

    /**
     * Action
     */
    describe('Action', () => {
        /**
         * call handleChange when input value changes
         */
        describe('call handleChange when input value changes', () => {
            /**
             * do not pass onChange
             */
            it('do not pass onChange', () => {
                const { mount } = setup();

                const onChange = jest.fn();
                const textArea = mount.find('textarea');

                textArea.simulate('change', {
                    target: {
                        value: 'new value',
                    },
                });
                expect(mount.find('textarea').props().value).toBe('new value');
                expect(onChange).not.toHaveBeenCalled();
            });

            /**
             * pass onChange
             */
            it('pass onChange', () => {
                const onChange = jest.fn();

                const { mount } = setup({ onChange });

                const textArea = mount.find('textarea');

                textArea.simulate('change', {
                    target: {
                        value: 'new value',
                    },
                });
                expect(mount.find('textarea').props().value).toBe('new value');
                expect(onChange).toHaveBeenCalled();
            });
        });

        /**
         * call handleBlur when blur
         */
        describe('call handleBlur when blur', () => {
            /**
             * do not pass onBlur
             */
            it('do not pass onBlur', () => {
                const { mount } = setup();

                const onBlur = jest.fn();
                const textArea = mount.find('textarea');

                textArea.simulate('focus');
                textArea.simulate('blur');

                expect(onBlur).not.toHaveBeenCalled();
            });

            /**
             * pass onBlur
             */
            it('pass onBlur', () => {
                const onBlur = jest.fn();

                const { mount } = setup({ onBlur });

                const textArea = mount.find('textarea');

                textArea.simulate('focus');
                textArea.simulate('blur');

                expect(onBlur).toHaveBeenCalled();
            });
        });
    });
});
