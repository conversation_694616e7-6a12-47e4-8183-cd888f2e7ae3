import API from '../constants/api';
import { BaseResponse } from '../models/common';
import { Top001InitGetCouponListModel, Top001InitGetCouponListResult } from '../models/top001-init-get-couponlist';
import { Top001InitGetDaoListModel, Top001InitGetDaoListResult } from '../models/top001-init-get-daolist';
import { Top001InitGetEcItemListModel, Top001InitGetEcItemListResult } from '../models/top001-init-get-ecitemlist';
import { Top001InitGetEventListModel, Top001InitGetEventListResult } from '../models/top001-init-get-eventlist';
import { Top001InitGetInfoModel, Top001InitGetInfoResult } from '../models/top001-init-get-info';
import { Top001InitGetMissionListModel, Top001InitGetMissionListResult } from '../models/top001-init-get-missionlist';
import {
    Top001InitGetNftContentListModel,
    Top001InitGetNftContentListResult,
} from '../models/top001-init-get-nftcontentlist';
import {
    Top001InitGetStampRallyListModel,
    Top001InitGetStampRallyListResult,
} from '../models/top001-init-get-stamprallylist';
import { Top001InitGetStoreListModel, Top001InitGetStoreListResult } from '../models/top001-init-get-storelist';
import createAPI from './baseApi';

/**
 * TopAPI
 */
class TopAPI {
    /**
     * top001InitGetInfo
     * @param data Top001InitGetInfoModel
     * @returns Promise<BaseResponse<Top001InitGetInfoResult>>
     */
    static top001InitGetInfo = (data: Top001InitGetInfoModel): Promise<BaseResponse<Top001InitGetInfoResult>> => {
        return createAPI({
            url: API.TOP001_INIT_GET_INFO,
            data,
        });
    };

    /**
     * top001InitGetStoreList
     * @param data Top001InitGetStoreListModel
     * @returns Promise<BaseResponse<Top001InitGetStoreListResult>>
     */
    static top001InitGetStoreList = (
        data: Top001InitGetStoreListModel
    ): Promise<BaseResponse<Top001InitGetStoreListResult>> => {
        return createAPI({
            url: API.TOP001_INIT_GET_STORELIST,
            data,
        });
    };

    /**
     * top001InitGetEventList
     * @param data Top001InitGetEventListModel
     * @returns Promise<BaseResponse<Top001InitGetEventListResult>>
     */
    static top001InitGetEventList = (
        data: Top001InitGetEventListModel
    ): Promise<BaseResponse<Top001InitGetEventListResult>> => {
        return createAPI({
            url: API.TOP001_INIT_GET_EVENTLIST,
            data,
        });
    };

    /**
     * top001InitGetStampRallyList
     * @param data Top001InitGetStampRallyListModel
     * @returns Promise<BaseResponse<Top001InitGetStampRallyListResult>>
     */
    static top001InitGetStampRallyList = (
        data: Top001InitGetStampRallyListModel
    ): Promise<BaseResponse<Top001InitGetStampRallyListResult>> => {
        return createAPI({
            url: API.TOP001_INIT_GET_STAMPRALLYLIST,
            data,
        });
    };

    /**
     * top001InitGetMissionList
     * @param data Top001InitGetMissionListModel
     * @returns Promise<BaseResponse<Top001InitGetMissionListResult>>
     */
    static top001InitGetMissionList = (
        data: Top001InitGetMissionListModel
    ): Promise<BaseResponse<Top001InitGetMissionListResult>> => {
        return createAPI({
            url: API.TOP001_INIT_GET_MISSIONLIST,
            data,
        });
    };

    /**
     * top001InitGetEcItemList
     * @param data Top001InitGetEcItemListModel
     * @returns Promise<BaseResponse<Top001InitGetEcItemListResult>>
     */
    static top001InitGetEcItemList = (
        data: Top001InitGetEcItemListModel
    ): Promise<BaseResponse<Top001InitGetEcItemListResult>> => {
        return createAPI({
            url: API.TOP001_INIT_GET_ECITEMLIST,
            data,
        });
    };

    /**
     * top001InitGetNftContentList
     * @param data Top001InitGetNftContentListModel
     * @returns Promise<BaseResponse<Top001InitGetNftContentListResult>>
     */
    static top001InitGetNftContentList = (
        data: Top001InitGetNftContentListModel
    ): Promise<BaseResponse<Top001InitGetNftContentListResult>> => {
        return createAPI({
            url: API.TOP001_INIT_GET_NFTCONTENTLIST,
            data,
        });
    };

    /**
     * top001InitGetDaoList
     * @param data Top001InitGetDaoListModel
     * @returns Promise<BaseResponse<Top001InitGetDaoListResult>>
     */
    static top001InitGetDaoList = (
        data: Top001InitGetDaoListModel
    ): Promise<BaseResponse<Top001InitGetDaoListResult>> => {
        return createAPI({
            url: API.TOP001_INIT_GET_DAOLIST,
            data,
        });
    };

    /**
     * top001InitGetCouponList
     * @param data Top001InitGetCouponListModel
     * @returns Promise<BaseResponse<Top001InitGetCouponListResult>>
     */
    static top001InitGetCouponList = (
        data: Top001InitGetCouponListModel
    ): Promise<BaseResponse<Top001InitGetCouponListResult>> => {
        return createAPI({
            url: API.TOP001_INIT_GET_COUPONLIST,
            data,
        });
    };
}

export default TopAPI;
