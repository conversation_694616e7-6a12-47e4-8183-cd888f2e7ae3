/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable max-len */
import clsx from 'clsx';
import React, { memo, useCallback, useMemo, useRef } from 'react';
import { Pagination, Swiper as SwiperClass } from 'swiper';
import { Swiper, SwiperSlide } from 'swiper/react';
import Icons from '../Icons/Icons';
// import Image from '../Image/Image';
import './styles.scss';

/**
 * CarouselCustomProps
 */
export type CarouselCustomProps = {
    data: string[];
};

/**
 * ID: CarouselCustom
 * Name: CarouselCustom common
 * @returns React.JSX.Element
 */
function CarouselCustom(props: CarouselCustomProps): React.JSX.Element {
    const { data } = useMemo(() => props, [props]);

    // refs
    const swiperRef = useRef<SwiperClass>();

    /**
     * handleOnSwiper
     * @param swiper SwiperClass
     */
    const handleOnSwiper = useCallback((swiper: SwiperClass): void => {
        swiperRef.current = swiper;
    }, []);

    /**
     * handleClickSwiperLeftButton
     * slide [キービジュアル] to prev item
     */
    const handleClickSwiperLeftButton = useCallback(() => {
        // slide to prev item
        swiperRef.current?.slidePrev();
    }, []);

    /**
     * handleClickSwiperRightButton
     * slide [キービジュアル] to next item
     */
    const handleClickSwiperRightButton = useCallback(() => {
        // slide to next item
        swiperRef.current?.slideNext();
    }, []);

    /**
     * renderKeyVisualItem
     * @param item item image
     * @param index number
     */
    const renderKeyVisualItem = useCallback(
        (item: string, index: number) => (
            <SwiperSlide key={index}>
                <img className="w-100 image-carousel" src={item} alt="" />
            </SwiperSlide>
        ),
        []
    );

    return React.useMemo(
        () => (
            <div id="carousel-common-custom" className="position-relative key-visual-container">
                <Swiper pagination loop modules={[Pagination]} onSwiper={handleOnSwiper}>
                    {data.map(renderKeyVisualItem)}
                </Swiper>
                {data.length > 1 && (
                    <div
                        className={clsx(
                            'd-flex justify-content-center align-items-center',
                            'position-absolute key-visual-control-button left'
                        )}
                        onClick={handleClickSwiperLeftButton}
                    >
                        <Icons.ArrowLeft />
                    </div>
                )}
                {data.length > 1 && (
                    <div
                        className={clsx(
                            'd-flex justify-content-center align-items-center',
                            'position-absolute key-visual-control-button right'
                        )}
                        onClick={handleClickSwiperRightButton}
                    >
                        <Icons.ArrowRight />
                    </div>
                )}
            </div>
        ),
        [handleClickSwiperLeftButton, handleClickSwiperRightButton, handleOnSwiper, data, renderKeyVisualItem]
    );
}

export default memo(CarouselCustom);
