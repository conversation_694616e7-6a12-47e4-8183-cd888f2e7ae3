import API from '../constants/api';
import {
    ChargeIntBnkPay001InitGetBankInfoModel,
    ChargeIntBnkPay001InitGetBankInfoResult,
} from '../models/chargeintbnkpay001-init-get-bankinfo';
import { ChargeIntBnkPay001MainDeleteBankAccountModel } from '../models/chargeintbnkpay001-main-delete-bankaccount';
import { ChargeIntBnkPay001MainExecuteChargeModel } from '../models/chargeintbnkpay001-main-execute-charge';
import {
    ChargeIntBnkPay001MainRegisterBankAccountModel,
    ChargeIntBnkPay001MainRegisterBankAccountResult,
} from '../models/chargeintbnkpay001-main-register-bankaccount';
import { ChargeIntBnkPay001MainRegisterBankPayModel } from '../models/chargeintbnkpay001-main-register-bankpay';
import {
    ChargeIntBnkPay002InitGetBankNameModel,
    ChargeIntBnkPay002InitGetBankNameResult,
} from '../models/chargeintbnkpay002-init-get-bankname';
import { ChargeIntBnkPay002MainRegisterBankAccountModel } from '../models/chargeintbnkpay002-main-register-bankaccount';
import {
    ChargeIntBnkPay005InitGetBankInfo,
    ChargeIntBnkPay005InitGetBankInfoResult,
} from '../models/chargeintbnkpay005-init-get-bankinfo';
import { ChargeIntBnkPay006MainExecuteChargeModel } from '../models/chargeintbnkpay006-main-execute-charge';
import {
    ChargeIntBnkPay008InitGetBankInfo,
    ChargeIntBnkPay008InitGetBankInfoResult,
} from '../models/chargeintbnkpay008-init-get-bankinfo';
import {
    ChargeIntBnkPay009InitGetInfoModel,
    ChargeIntBnkPay009InitGetInfoResult,
} from '../models/chargeintbnkpay009-init-get-info';
import {
    ChargeIntBnkPay009MainRegisterBankAccountModel,
    ChargeIntBnkPay009MainRegisterBankAccountResult,
} from '../models/chargeintbnkpay009-main-register-bankaccount';
import {
    ChargePrePid001MainExecuteChargeModel,
    ChargePrePid001MainExecuteChargeResult,
} from '../models/chargeprepid001-main-execute-charge';
import {
    ChargePrePid001MainExecuteOcrModel,
    ChargePrePid001MainExecuteOcrResult,
} from '../models/chargeprepid001-main-execute-ocr';
import {
    ChargeQrCodePresent001InitCreateTransferCodeModel,
    ChargeQrCodePresent001InitCreateTransferCodeResult,
} from '../models/chargeqrcodepresent001-init-create-transfercode';
import {
    ChargeQrCodeRead001MainExecuteChargeModel,
    ChargeQrCodeRead001MainExecuteChargeResult,
} from '../models/chargeqrcoderead001-main-execute-charge';
import {
    ChargeQrCodeRead002MainExecuteChargeModel,
    ChargeQrCodeRead002MainExecuteChargeResult,
} from '../models/chargeqrcoderead002-main-execute-charge';
import {
    ChargeSelect001InitGetClientKeyModel,
    ChargeSelect001InitGetClientKeyResult,
} from '../models/chargeselect001-init-get-clientkey';
import { ChargeSelect001MainGetBankPayRegisterFlag } from '../models/chargeselect001-main-get-bankpayregisterflag';
import {
    ChargeVouchr002InitGetClientKeyModel,
    ChargeVouchr002InitGetClientKeyResult,
} from '../models/chargevouchr002-init-get-clientkey';
import {
    ChargeVouchr002MainExecuteChargeModel,
    ChargeVouchr002MainExecuteChargeResult,
} from '../models/chargevouchr002-main-execute-charge';
import { BaseResponse, CommonCallback } from '../models/common';
import {
    ChargeCnveni001InitGetChargeLimitModel,
    ChargeCnveni001InitGetChargeLimitResult,
    ChargeCredit001InitGetChargeLimitModel,
    ChargeCredit001InitGetChargeLimitResult,
    ChargeIntBnk001InitGetChargeLimitModel,
    ChargeIntBnk001InitGetChargeLimitResult,
    InitGetChargeLimitModel,
    InitGetChargeLimitResult,
} from '../models/init-get-chargelimit';
import {
    ChargeCnveni002MainExecuteChargeModel,
    ChargeCnveni002MainExecuteChargeResult,
    ChargeCredit002MainExecuteChargeModel,
    ChargeCredit002MainExecuteChargeResult,
    ChargeIntBnk002MainExecuteChargeModel,
    ChargeIntBnk002MainExecuteChargeResult,
} from '../models/main-execute-charge';
import createAPI from './baseApi';

/**
 * ChargeAPI
 */
class ChargeAPI {
    /**
     * chargeCredit001InitGetChargeLimit
     * @param data ChargeCredit001InitGetChargeLimitModel
     * @returns Promise<BaseResponse<ChargeCredit001InitGetChargeLimitResult>>
     */
    static chargeCredit001InitGetChargeLimit = (
        data: ChargeCredit001InitGetChargeLimitModel
    ): Promise<BaseResponse<ChargeCredit001InitGetChargeLimitResult>> => {
        return createAPI({
            url: API.CHARGE_CREDIT_001_INIT_GET_CHARGE_LIMIT,
            data,
        });
    };

    /**
     * chargeCredit002MainExecuteCharge
     * @param data ChargeCredit002MainExecuteChargeModel
     * @returns Promise<BaseResponse<ChargeCredit002MainExecuteChargeResult>>
     */
    static chargeCredit002MainExecuteCharge = (
        data: ChargeCredit002MainExecuteChargeModel
    ): Promise<BaseResponse<ChargeCredit002MainExecuteChargeResult>> => {
        return createAPI({
            url: API.CHARGE_CREDIT_002_MAIN_EXECUTE_CHARGE,
            data,
        });
    };

    /**
     * chargeCnveni001InitGetChargeLimit
     * @param data ChargeCnveni001InitGetChargeLimitModel
     * @returns Promise<BaseResponse<ChargeCnveni001InitGetChargeLimitResult>>
     */
    static chargeCnveni001InitGetChargeLimit = (
        data: ChargeCnveni001InitGetChargeLimitModel
    ): Promise<BaseResponse<ChargeCnveni001InitGetChargeLimitResult>> => {
        return createAPI({
            url: API.CHARGE_CNVENI_001_INIT_GET_CHARGE_LIMIT,
            data,
        });
    };

    /**
     * chargeCnveni002MainExecuteCharge
     * @param data ChargeCnveni002MainExecuteChargeModel
     * @returns Promise<BaseResponse<ChargeCnveni002MainExecuteChargeResult>>
     */
    static chargeCnveni002MainExecuteCharge = (
        data: ChargeCnveni002MainExecuteChargeModel
    ): Promise<BaseResponse<ChargeCnveni002MainExecuteChargeResult>> => {
        return createAPI({
            url: API.CHARGE_CNVENI_002_MAIN_EXECUTE_CHARGE,
            data,
        });
    };

    /**
     * chargeIntBnk001InitGetChargeLimit
     * @param data ChargeIntBnk001InitGetChargeLimitModel
     * @returns Promise<BaseResponse<ChargeIntBnk001InitGetChargeLimitResult>>
     */
    static chargeIntBnk001InitGetChargeLimit = (
        data: ChargeIntBnk001InitGetChargeLimitModel
    ): Promise<BaseResponse<ChargeIntBnk001InitGetChargeLimitResult>> => {
        return createAPI({
            url: API.CHARGE_INTBNK_001_INIT_GET_CHARGE_LIMIT,
            data,
        });
    };

    /**
     * chargeIntBnkPay001MainRegisterBankPay
     * @param data ChargeIntBnkPay001MainRegisterBankPayModel
     * @returns Promise<BaseResponse>
     */
    static chargeIntBnkPay001MainRegisterBankPay = (
        data: ChargeIntBnkPay001MainRegisterBankPayModel
    ): Promise<BaseResponse> => {
        return createAPI({
            url: API.CHARGE_INTBNK_PAY_001_MAIN_REGISTER_BANKPAY,
            data,
        });
    };

    /**
     * chargeIntBnkPay001InitGetBankInfo
     * @param data ChargeIntBnkPay001InitGetBankInfoModel
     * @returns Promise<BaseResponse<ChargeIntBnkPay001InitGetBankInfoResult>>
     */
    static chargeIntBnkPay001InitGetBankInfo = (
        data: ChargeIntBnkPay001InitGetBankInfoModel
    ): Promise<BaseResponse<ChargeIntBnkPay001InitGetBankInfoResult>> => {
        return createAPI({
            url: API.CHARGE_INTBNK_PAY_001_INIT_GET_BANKINFO,
            data,
        });
    };

    /**
     * chargeIntBnkPay001MainRegisterBankAccount
     * @param data ChargeIntBnkPay001MainRegisterBankAccountModel
     * @returns Promise<BaseResponse>
     */
    static chargeIntBnkPay001MainRegisterBankAccount = (
        data: ChargeIntBnkPay001MainRegisterBankAccountModel
    ): Promise<BaseResponse<ChargeIntBnkPay001MainRegisterBankAccountResult>> => {
        return createAPI({
            url: API.CHARGE_INTBNK_PAY_001_MAIN_REGISTER_BANKACCOUNT,
            data,
        });
    };

    /**
     * chargeIntBnkPay001MainExecuteCharge
     * @param data ChargeIntBnkPay001MainExecuteChargeModel
     * @returns Promise<BaseResponse>
     */
    static chargeIntBnkPay001MainExecuteCharge = (
        data: ChargeIntBnkPay001MainExecuteChargeModel
    ): Promise<BaseResponse> => {
        return createAPI({
            url: API.CHARGE_INTBNK_PAY_001_MAIN_EXECUTE_CHARGE,
            data,
        });
    };

    /**
     * chargeIntBnkPay001MainDeleteBankAccount
     * @param data ChargeIntBnkPay001MainDeleteBankAccountModel
     * @returns Promise<BaseResponse>
     */
    static chargeIntBnkPay001MainDeleteBankAccount = (
        data: ChargeIntBnkPay001MainDeleteBankAccountModel
    ): Promise<BaseResponse> => {
        return createAPI({
            url: API.CHARGE_INTBNK_PAY_001_MAIN_DELETE_BANKACCOUNT,
            data,
        });
    };

    /**
     * chargeIntBnk002MainExecuteCharge
     * @param data ChargeIntBnk002MainExecuteChargeModel
     * @returns Promise<BaseResponse<ChargeIntBnk002MainExecuteChargeResult>>
     */
    static chargeIntBnk002MainExecuteCharge = (
        data: ChargeIntBnk002MainExecuteChargeModel
    ): Promise<BaseResponse<ChargeIntBnk002MainExecuteChargeResult>> => {
        return createAPI({
            url: API.CHARGE_INTBNK_002_MAIN_EXECUTE_CHARGE,
            data,
        });
    };

    /**
     * chargePrePid001MainExecuteCharge
     * @param data ChargePrePid001MainExecuteChargeModel
     * @returns Promise<BaseResponse<ChargePrePid001MainExecuteChargeResult>>
     */
    static chargePrePid001MainExecuteCharge = (
        data: ChargePrePid001MainExecuteChargeModel
    ): Promise<BaseResponse<ChargePrePid001MainExecuteChargeResult>> => {
        return createAPI({
            url: API.CHARGE_PREPID_001_MAIN_EXECUTE_CHARGE,
            data,
        });
    };

    /**
     * chargePrePid001MainExecuteOcr
     * @param data ChargePrePid001MainExecuteOcrModel
     * @returns Promise<BaseResponse<ChargePrePid001MainExecuteOcrResult>>
     */
    static chargePrePid001MainExecuteOcr = (
        data: ChargePrePid001MainExecuteOcrModel
    ): Promise<BaseResponse<ChargePrePid001MainExecuteOcrResult>> => {
        return createAPI({
            url: API.CHARGE_PREPID_001_MAIN_EXECUTE_OCR,
            data,
        });
    };

    /**
     * chargeVouchr002MainExecuteCharge
     * @param data ChargeVouchr002MainExecuteChargeModel
     * @returns Promise<BaseResponse<ChargeVouchr002MainExecuteChargeResult>>
     */
    static chargeVouchr002MainExecuteCharge = (
        data: ChargeVouchr002MainExecuteChargeModel
    ): Promise<BaseResponse<ChargeVouchr002MainExecuteChargeResult>> => {
        return createAPI({
            url: API.CHARGEVOUCHR002_MAIN_EXECUTE_CHARGE,
            data,
        });
    };

    /**
     * chargeVouchr002InitGetClientKey
     * @param data ChargeVouchr002InitGetClientKeyModel
     * @returns Promise<BaseResponse<ChargeVouchr002InitGetClientKeyResult>>
     */
    static chargeVouchr002InitGetClientKey = (
        data: ChargeVouchr002InitGetClientKeyModel
    ): Promise<BaseResponse<ChargeVouchr002InitGetClientKeyResult>> => {
        return createAPI({
            url: API.CHARGEVOUCHR002_INIT_GET_CLIENT_KEY,
            data,
        });
    };

    /**
     * chargeSelect001InitGetClientKey
     * @param data ChargeSelect001InitGetClientKeyModel
     * @returns Promise<BaseResponse<ChargeSelect001InitGetClientKeyResult>>
     */
    static chargeSelect001InitGetClientKey = (
        data: ChargeSelect001InitGetClientKeyModel
    ): Promise<BaseResponse<ChargeSelect001InitGetClientKeyResult>> => {
        return createAPI({
            url: API.CHARGESELECT001_INIT_GET_CLIENT_KEY,
            data,
        });
    };

    /**
     * chargeQrCodePresent001InitCreateTransferCode
     * @param data ChargeQrCodePresent001InitCreateTransferCodeModel
     * @returns Promise<BaseResponse<ChargeQrCodePresent001InitCreateTransferCodeResult>>
     */
    static chargeQrCodePresent001InitCreateTransferCode = (
        data: ChargeQrCodePresent001InitCreateTransferCodeModel
    ): Promise<BaseResponse<ChargeQrCodePresent001InitCreateTransferCodeResult>> => {
        return createAPI({
            url: API.CHARGEQRCODEPRESENT001_INIT_CREATE_TRANSFERCODE,
            data,
        });
    };

    /**
     * chargeQrCodeRead001MainExecuteCharge
     * @param data ChargeQrCodeRead001MainExecuteChargeModel
     * @returns Promise<BaseResponse<ChargeQrCodeRead001MainExecuteChargeResult>>
     */
    static chargeQrCodeRead001MainExecuteCharge = (
        data: ChargeQrCodeRead001MainExecuteChargeModel
    ): Promise<BaseResponse<ChargeQrCodeRead001MainExecuteChargeResult>> => {
        return createAPI({
            url: API.CHARGE_QR_CODE_READ001_MAIN_EXECUTE_CHARGE,
            data,
        });
    };

    /**
     * chargeQrCodeRead002MainExecuteCharge
     * @param data ChargeQrCodeRead002MainExecuteChargeModel
     * @returns Promise<BaseResponse<ChargeQrCodeRead002MainExecuteChargeResult>>
     */
    static chargeQrCodeRead002MainExecuteCharge = (
        data: ChargeQrCodeRead002MainExecuteChargeModel
    ): Promise<BaseResponse<ChargeQrCodeRead002MainExecuteChargeResult>> => {
        return createAPI({
            url: API.CHARGE_QR_CODE_READ002_MAIN_EXECUTE_CHARGE,
            data,
        });
    };

    /**
     * chargeSelect001MainGetBankPayRegisterFlag
     * @returns Promise<BaseResponse<ChargeSelect001MainGetBankPayRegisterFlag>>
     */
    static chargeSelect001MainGetBankPayRegisterFlag = (): Promise<
        BaseResponse<ChargeSelect001MainGetBankPayRegisterFlag>
    > => {
        return createAPI({
            url: API.CHARGESELECT001_MAIN_GET_BANKPAYREGISTERFLAG,
        });
    };

    // -------------------- START API CHARGE INT BANK PAY -------------------- //
    /**
     * chargeIntBnkPay002InitGetBankName
     * @returns Promise<BaseResponse<ChargeIntBnkPay002InitGetBankNameResult>>
     */
    static chargeIntBnkPay002InitGetBankName = (
        data: ChargeIntBnkPay002InitGetBankNameModel
    ): Promise<BaseResponse<ChargeIntBnkPay002InitGetBankNameResult>> => {
        return createAPI({
            url: API.CHARGEINTBNKPAY002_INIT_GET_BANKNAME,
            data,
        });
    };

    /**
     * chargeIntBnkPay002MainRegisterBankAccount
     * @param data ChargeIntBnkPay002MainRegisterBankAccountModel
     * @returns Promise<BaseResponse>
     */
    static chargeIntBnkPay002MainRegisterBankAccount = (
        data: ChargeIntBnkPay002MainRegisterBankAccountModel
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
    ): Promise<BaseResponse<any>> => {
        return createAPI({
            url: API.CHARGEINTBNKPAY002_MAIN_REGISTER_BANKACCOUNT,
            data,
        });
    };

    /**
     * chargeIntBnkPay005InitGetChargeLimit
     * @param data InitGetChargeLimitModel
     * @returns Promise<BaseResponse<InitGetChargeLimitResult>>
     */
    static chargeIntBnkPay005InitGetChargeLimit = (
        data: InitGetChargeLimitModel
    ): Promise<BaseResponse<InitGetChargeLimitResult>> => {
        return createAPI({
            url: API.CHARGEINTBNKPAY005_INIT_GET_CHARGE_LIMIT,
            data,
        });
    };

    /**
     * chargeIntBnkPay001InitGetBankInfo
     * @param data ChargeIntBnkPay005InitGetBankInfo
     * @returns Promise<BaseResponse<ChargeIntBnkPay005InitGetBankInfoResult>>
     */
    static chargeIntBnkPay005InitGetBankInfo = (
        data: ChargeIntBnkPay005InitGetBankInfo
    ): Promise<BaseResponse<ChargeIntBnkPay005InitGetBankInfoResult>> => {
        return createAPI({
            url: API.CHARGEINTBNKPAY005_INIT_GET_BANKINFO,
            data,
        });
    };

    /**
     * chargeIntBnkPay006MainExecuteCharge
     * @param data ChargeIntBnkPay006MainExecuteChargeModel
     * @returns Promise<BaseResponse>
     */
    static chargeIntBnkPay006MainExecuteCharge = (
        data: ChargeIntBnkPay006MainExecuteChargeModel
    ): Promise<BaseResponse> => {
        return createAPI({
            url: API.CHARGEINTBNKPAY006_MAIN_EXECUTE_CHARGE,
            data,
        });
    };

    /**
     * chargeIntBnkPay008InitGetBankInfo
     * @param data ChargeIntBnkPay008InitGetBankInfo
     * @returns Promise<BaseResponse<ChargeIntBnkPay008InitGetBankInfoResult>>
     */
    static chargeIntBnkPay008InitGetBankInfo = (
        data: ChargeIntBnkPay008InitGetBankInfo
    ): Promise<BaseResponse<ChargeIntBnkPay008InitGetBankInfoResult>> => {
        return createAPI({
            url: API.CHARGEINTBNKPAY008_INIT_GET_BANKINFO,
            data,
        });
    };

    /**
     * chargeIntBnkPay008MainDeleteBankAccount
     * @param data CommonCallback
     * @returns Promise<BaseResponse>
     */
    static chargeIntBnkPay008MainDeleteBankAccount = (data: CommonCallback): Promise<BaseResponse> => {
        return createAPI({
            url: API.CHARGEINTBNKPAY008_MAIN_DELETE_BANKACCOUNT,
            data,
        });
    };

    /**
     * chargeIntBnkPay009InitGetInfo
     * @param data ChargeIntBnkPay009InitGetInfoModel
     * @returns Promise<BaseResponse<ChargeIntBnkPay009InitGetInfoResult>>
     */
    static chargeIntBnkPay009InitGetInfo = (
        data: ChargeIntBnkPay009InitGetInfoModel
    ): Promise<BaseResponse<ChargeIntBnkPay009InitGetInfoResult>> => {
        return createAPI({
            url: API.CHARGEINTBNKPAY009_INIT_GET_INFO,
            data,
        });
    };

    /**
     * chargeintbnkpay009MainRegisterBankAccount
     * @param data ChargeIntBnkPay009MainRegisterBankAccountModel
     * @returns Promise<BaseResponse<ChargeIntBnkPay009MainRegisterBankAccountResult>>
     */
    static chargeintbnkpay009MainRegisterBankAccount = (
        data: ChargeIntBnkPay009MainRegisterBankAccountModel
    ): Promise<BaseResponse<ChargeIntBnkPay009MainRegisterBankAccountResult>> => {
        return createAPI({
            url: API.CHARGEINTBNKPAY009_MAIN_REGISTER_BANKACCOUNT,
            data,
        });
    };

    // -------------------- END API CHARGE INT BANK PAY -------------------- //
}

export default ChargeAPI;
