import { CommonCallback } from './common';

/**
 * Top001InitGetEcItemListModel
 */
export interface Top001InitGetEcItemListModel extends CommonCallback {
    // ブランドID
    BrandID: string;
    // 1ページあたりの取得アイテム数
    PageSize: number;
}

/**
 * Top001InitGetEcItem
 */
export interface Top001InitGetEcItem {
    // EC商品ID
    ItemID: string;
    // EC商品画像
    ItemImage: string;
    // EC商品名
    ItemName: string;
    // EC商品価格
    TaxPrice: string;
    // EC商品説明
    Explanation: string;
    // EC商品取扱店舗名
    StoreName: string;
}

/**
 * Top001InitGetEcItemListResult
 */
export interface Top001InitGetEcItemListResult {
    // EC商品情報一覧
    EcItemList: Top001InitGetEcItem[];
}
