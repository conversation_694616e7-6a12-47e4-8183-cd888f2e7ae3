/**
 * Define Action list
 */
export const Actions = {
    LOGIN001: 'LOGIN001',
    ENTRY001: 'ENTRY001',
    RESET001: 'RESET001',
    HOME001: 'HOME001',
    WALLET001: 'WALLET001',
    NFT_COLLECTION001: 'NFT_COLLECTION001',
    STORE002: 'STORE002',
    NEWS001: 'NEWS001',
    MENU001: 'MENU001',

    NOTICE: 'SCR000004',
    COUPON_LIST: 'SCR000007',
    COUPON_LIST_AVAILABLE: 'SCR000008',
    COUPON_LIST_USED: 'SCR000009',
    COUPON_DETAIL: 'SCR000010',
    COUPON_USE_DETAIL: 'SCR000011',
    COUPON_AVAILABLE: 'SCR000012',
    COUPON_USED: 'SCR000013',
};

/**
 * Define ScreenInfo list
 */
export const ScreenInfos: IScreenInfos = {
    LOGIN001: {
        ScreenID: 'LOGIN',
        ScreenValue: 'ログイン',
    },
    ENTRY001: {
        ScreenID: 'ENTRY001',
        ScreenValue: '新規登録 (利用規約)',
    },
    RESET001: {
        ScreenID: 'RESET001',
        ScreenValue: 'パスワードリセット (認証コード送信)',
    },
    HOME001: {
        ScreenID: 'HOME001',
        ScreenValue: 'ホーム',
    },
    WALLET001: {
        ScreenID: 'WALLET001',
        ScreenValue: 'ウォレット',
    },
    NFT_COLLECTION001: {
        ScreenID: 'NFT_COLLECTION001',
        ScreenValue: 'コレクション',
    },
    STORE002: {
        ScreenID: 'STORE002',
        ScreenValue: '店舗情報 (一覧)',
    },
    NEWS001: {
        ScreenID: 'NEWS001',
        ScreenValue: 'お知らせ (一覧)',
    },
    MENU001: {
        ScreenID: 'MENU001',
        ScreenValue: 'メニュー',
    },
};

/**
 * Interface for ScreenInfo list
 */
interface IScreenInfos {
    [key: string]: IScreenInfo;
}

/**
 * Interface for ScreenInfo item
 */
interface IScreenInfo {
    ScreenID: string;
    ScreenValue: string;
}
