import { CommonCallback } from './common';

/**
 * Event001InitGetEventTicketQueryModel
 */
export interface Event001InitGetEventTicketQueryModel extends CommonCallback {
    BrandID?: string;
}

/**
 * Event001InitGetEventTicketQueryResult
 */
export interface Event001InitGetEventTicketQueryResult {
    Areas: AreaItem[];
    LastEvaluatedKey?: string;
}

export interface AreaItem {
    /**
     * エリアID
     */
    AreaID: string;
    /**
     * エリア名
     */
    AreaName: string;
}
