import { CommonCallback } from './common';

/**
 * MainSearchStorePublicCodeModel
 */
export interface MainSearchStorePublicCodeModel extends CommonCallback {
    StorePublicCode: string;
}

/**
 * Paymulti001MainSearchStorePublicCodeModel
 */
export type Paymulti001MainSearchStorePublicCodeModel = MainSearchStorePublicCodeModel;

/**
 * Paymulti002MainSearchStorePublicCodeModel
 */
export type Paymulti002MainSearchStorePublicCodeModel = MainSearchStorePublicCodeModel;

/**
 * MainSearchStorePublicCodeResult
 */
export interface MainSearchStorePublicCodeResult {
    StoreID: string;
    StoreName: string;
    StoreNameKana: string;
    DefaultPaymentAmount: number;
}

/**
 * Paymulti001MainSearchStorePublicCodeResult
 */
export interface Paymulti001MainSearchStorePublicCodeResult extends MainSearchStorePublicCodeResult {
    // オーダーID
    OrderID: string;
}

/**
 * Paymulti002MainSearchStorePublicCodeResult
 */
export interface Paymulti002MainSearchStorePublicCodeResult extends MainSearchStorePublicCodeResult {
    // オーダーID
    OrderID: string;
}
