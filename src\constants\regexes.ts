/* eslint-disable max-len */
export const Regexes = {
    Email: /^(([^<>()[\]\\.,;:\s@"]+(\.[^<>()[\]\\,;:\s@"]+)*)|(".+"))@((\[\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/,
    EXP_MESSAGE:
        /(\b(https?|ftp|file):\/\/([-A-Z0-9+&@#%?=~_|!:,.;]*)([-A-Z0-9+&@#%?/=~_|!:,.;]*)[-A-Z0-9+&@#/%=~_|])/gi,
    PHONE_NUMBER: /^\d{11}$/,
    NAME: /^[ぁ-んァ-ン一-龥a-zA-Z〃々ヽヾゝゞ〻〳〵〴]{1,30}$/,
    PASSWORD_HAS_LETTER_AND_DIGIT: /^(?=.*?[A-Za-z])(?=.*?\d).{2,}$/,
    PASSWORD_CHECK: /^(?=.*?[A-Za-z])(?=.*?\d)(?=.*?[!-/:-@[-`{-~]).{8,20}$/,
    PHONE_FIRST_THREE_DIGIT: /^(070|080|090)+\d{8}$/g,
    POSTCODE: /^(\d{3})(\d{4})$/g,
    MOBILE_DEVICE_CHECK: /android|iphone|kindle|ipad/i,
    // eslint-disable-next-line no-useless-escape
    LINKIFY: /(http|ftp|https):\/\/([\w_-]+(?:(?:\.[\w_-]+)+))([\w.,@?^=%&:\/~+#-]*[\w@?^=%&\/~+#-])/gm,
    URL: /(http|ftp|https):\/\/([\w_-]+(?:(?:\.[\w_-]+)+))([\w.,@?^=%&:\\/~+#-]*[\w@?^=%&\\/~+#-])/g,
    RFC: /^(?=.{1,255}$)(?!.*\.{2,})(?![.])(?!.*\.$)([a-zA-Z0-9-_+?/]+(\.[a-zA-Z0-9-_+?/]+)*)@[a-zA-Z0-9-]+(\.[a-zA-Z0-9-]+)*\.[a-zA-Z]{2,}$/g,
    CELLPHONE_NO: /^[0-9]+$/,
};
