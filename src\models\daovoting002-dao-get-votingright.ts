import { CommonCallback } from './common';

/**
 * DAOVoting002DaoGetVotingRightModel
 */
export interface DAOVoting002DaoGetVotingRightModel extends CommonCallback {
    // DAOID
    DaoID: string;
    // 投票テーマID
    ThemeID: string;
}

/**
 * DAOVoting002DaoGetVotingRightResult
 */
export interface DAOVoting002DaoGetVotingRightResult {
    // ガバナンストークン
    GovernanceToken: number;
    // 票の重み
    VotingWeight: number;
    // 1票換算GT
    ConversionGT: number;
    // 投票権フラグ
    IsVotingRight: boolean;
    // 投票日時
    VotingDateTime: string;
}
