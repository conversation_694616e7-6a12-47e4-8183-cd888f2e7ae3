import { CommonCallback } from './common';
/**
 * CheerCommonProfileModel
 */
export interface CheerCommonProfileModel extends CommonCallback {
    // ブランドID
    BrandID: string;
    // 応援者ID
    CheerID: number;
}

/**
 * CheerAttribute enum
 */
export enum CheerAttribute {
    USER = 'User',
    STORE = 'Store',
    OPERATOR = 'Operator',
}

export enum SellerStatus {
    // 0:未申請
    NOT_APPLIED = 0,
    // 1：申請中
    APPLIED = 1,
    // 2：ekyc確認中
    CHECKING_EKYC = 2,
    // 3：ekyc承認
    E_KYC_APPROVED = 3,
    // 4：ekyc否認
    E_KYC_DENIED = 4,
    // 5：運営承認待ち
    PENDING_OPERATIONAL_APPROVAL = 5,
    // 6：運営承認
    OPERATION_APPROVED = 6,
    // 7：運営否認
    OPERATION_DENIAL = 7,
}

/**
 * CheerCommonProfileItem
 */
export interface CheerCommonProfileItem {
    // 応援者名
    CheerName: string;
    // アバターアイコンファイルURI
    AvatarIconFileURI: string;
    // 通知受領ステータス
    CheerPushStatus: number;
    // CheerAttribute
    CheerAttribute: CheerAttribute;
    // CheerDisplayID
    CheerDisplayID: string;
    // VoiceType すべてのボイス：1、温泉地ごとのボイス：2)
    VoiceType?: number;
    // DenialReason
    DenialReason?: string;
    // SellerStatus
    SellerStatus?: SellerStatus;
    // 開催ID（友だち紹介）
    FriendCampaignID?: number;
    // 紹介コメント（友だち紹介）
    FriendComment?: string;
    // 開催ID（友だち紹介）
    FriendURL?: string;
    // CheerQRNumber
    CheerQRNumber?: string;
    // CheerMailAddress
    CheerMailAddress?: string;
    // TelephoneNumber
    TelephoneNumber?: string;
}
