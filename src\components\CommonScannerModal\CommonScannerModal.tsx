import clsx from 'clsx';
import { ForwardedRef, forwardRef, useMemo } from 'react';
import CommonDialog, { CommonDialogProps, CommonDialogRef } from '../CommonDialog/CommonDialog';
import { HeaderProps } from '../Header/Header';
import './styles.scss';

/**
 * CommonScannerModalProps
 */
export interface CommonScannerModalProps extends CommonDialogProps {
    /**
     * useHeader
     */
    useHeader?: boolean | HeaderProps;
    /**
     * ScreenName
     */
    screenName?: string;
}

/**
 * CommonScannerModal
 * @param props CommonScannerModalProps
 * @param ref ForwardedRef<CommonDialogRef>
 */
const CommonScannerModal = forwardRef((props: CommonScannerModalProps, ref: ForwardedRef<CommonDialogRef>) => {
    const { children, useDialogCloseButton, useBackdropDismiss, childrenClassName, useTransaction } = useMemo(
        () => props,
        [props]
    );

    return useMemo(
        () => (
            <CommonDialog
                ref={ref}
                xCenter
                yCenter
                useBackdropDismiss={useBackdropDismiss}
                useDialogCloseButton={useDialogCloseButton}
                childrenClassName={`p-0 ${childrenClassName}`}
                useTransaction={useTransaction}
            >
                <div className={clsx('common-scanner-modal d-flex flex-column', childrenClassName)}>{children}</div>
            </CommonDialog>
        ),
        [children, childrenClassName, ref, useBackdropDismiss, useDialogCloseButton, useTransaction]
    );
});

CommonScannerModal.displayName = 'CommonScannerModal';

export default CommonScannerModal;
