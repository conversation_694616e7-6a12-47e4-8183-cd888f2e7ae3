import { CommonCallback } from './common';

/**
 * Nft008InitGetCoin
 */
export type Nft008InitGetCoinModel = CommonCallback;

/**
 * MedalChargeMethodItem
 */
export interface MedalGroupDetailItem {
    ProductGroupName: string;
    ProductGroupImage: string;
    StoreGroupName: string;
    StoreGroupImage: string;
    UsePriority: number;
    MedalNameList: string[];
}

/**
 * Nft008InitGetCoinResult
 */
export interface Nft008InitGetCoinResult {
    // メダルサービスID
    MedalServiceID: string;
    // メダルサービス種別(1:コイン 2:商品券)
    MedalServiceType: number;
    // メダルサービス名
    MedalServiceName: string;
    // メダルサービス画像
    MedalServiceImage: string;
    // メダルサービスURL
    MedalServiceURL: string;
    // メダルサービス開始日
    MedalServiceStartDate: string;
    // メダルサービス終了日
    MedalServiceEndDate: string;
    // 支払い開始日
    PaymentStartDate: string;
    // 支払い終了日
    PaymentEndDate: string;
    // チャージ開始日
    ChargeStartDate: string;
    // チャージ終了日
    ChargeEndDate: string;
    // 支払いボタンの有効フラグ
    PaymentButtonEnableFlag: boolean;
    // チャージボタンの有効フラグ
    ChargeButtonEnableFlag: boolean;
    // 当選口数(商品券購入最大口数)
    WinningCount?: number;
    // 購入済ステータス(0:未購入)
    PurchaseStatus?: number;
    // 商品ID
    ProductID: number;
    // 商品金額
    ProductPrice: number;
    // 商品取引レート
    ProductRate: number;
    // メダルサービス残高
    MedalServiceBalance: number;
    // 有効期限が近い残高有りフラグ
    MedalServiceExpirationFlg: boolean;
    // メダルグループ詳細
    MedalGroupDetails?: {
        [key: string]: MedalGroupDetailItem;
    };
    // メダルチャージ方法
    MedalChargeMethod: string[];
    // custom CategoryID
    NFTCategoryID?: string;
    // custom NFTContentID
    NFTContentID?: string;
}
