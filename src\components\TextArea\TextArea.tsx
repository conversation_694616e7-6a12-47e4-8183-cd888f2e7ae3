import clsx from 'clsx';
import React, {
    ChangeEvent,
    FocusEvent,
    ForwardedRef,
    InputHTMLAttributes,
    forwardRef,
    useCallback,
    useEffect,
    useMemo,
    useState,
} from 'react';
import { CommonError, RequiredLabel, RequiredLabelProps } from '../Common';
import './styles.scss';

// OmittedTextAreaType
type OmittedTextAreaType = Omit<InputHTMLAttributes<HTMLTextAreaElement>, 'onChange' | 'prefix'>;

/**
 * TextAreaProps
 */
export interface TextAreaProps extends Partial<RequiredLabelProps>, OmittedTextAreaType {
    /**
     * input's sub-label (under label)
     */
    subLabel?: string | React.ReactNode;
    /**
     *  text under input
     */
    textUnderneath?: string | React.ReactNode;
    /**
     * input's variant
     */
    variant?: string;
    /**
     * error
     */
    error?: string | boolean;
    /**
     * input's right component (outside input)
     */
    rightComponent?: React.ReactNode;
    /**
     * input's prefix (inside input)
     */
    prefix?: React.ReactNode;
    /**
     * input's suffix (inside input)
     */
    suffix?: React.ReactNode;
    /**
     * input's rows
     */
    rows?: number;
    /**
     * onChange
     * @param event ChangeEvent<HTMLTextAreaElement>
     * @returns void
     */
    onChange?: (event: ChangeEvent<HTMLTextAreaElement>) => void;
    /**
     * autoAdjustHeight
     */
    autoAdjustHeight?: boolean;
}

/**
 * Common Textarea component
 */
const TextArea = forwardRef((props: TextAreaProps, ref: ForwardedRef<unknown>): React.JSX.Element => {
    const {
        value: inputValue = '',
        label,
        subLabel,
        textUnderneath,
        required,
        error,
        rightComponent,
        prefix,
        suffix,
        rows,
        onChange,
        onBlur,
        autoAdjustHeight,
        id,
        maxLength,
        ...rest
    } = props || {};

    // current value of textarea
    const [value, setValue] = useState(inputValue);

    useEffect(() => {
        setValue(inputValue);
    }, [inputValue]);

    /**
     * handleValueChange
     */
    const handleValueChange = useCallback(
        (event: ChangeEvent<HTMLTextAreaElement>) => {
            setValue(event.target.value.slice(0, maxLength));

            // dispatch value and target to parent view
            event.target.value = event.target.value?.slice(0, maxLength);
            onChange?.(event);
        },
        [maxLength, onChange]
    );

    /**
     * handleBlur
     * handle blur textarea field
     */
    const handleBlur = useCallback(
        (event: FocusEvent<HTMLTextAreaElement, Element>) => {
            onBlur?.(event);
        },
        [onBlur]
    );

    useEffect(() => {
        const highlighter = document.getElementById('highlighter');
        if (id && document.getElementById(id)) {
            document.getElementById(id)?.setAttribute('style', `height:${highlighter?.offsetHeight}px !important`);
        }
    });

    return useMemo(
        () => (
            <div className="d-flex flex-column common-textarea-component">
                {label && <RequiredLabel label={label} required={required} optional={!required} />}
                {subLabel && <div className="sub-label">{subLabel}</div>}

                <div className="d-flex flex-column main-textarea-container">
                    <div className="d-flex flex-row align-items-center">
                        <div
                            className={clsx('main-textarea d-flex align-items-center', {
                                error: Boolean(error),
                            })}
                        >
                            {autoAdjustHeight && (
                                <div id="highlighter" className="highlighter">
                                    {value}
                                </div>
                            )}

                            {prefix}
                            <textarea
                                {...rest}
                                id={id}
                                value={value}
                                onChange={handleValueChange}
                                onBlur={handleBlur}
                                autoComplete="off"
                                rows={rows}
                                maxLength={maxLength}
                            />
                            {suffix}
                        </div>
                        {rightComponent}
                    </div>
                    {textUnderneath && <div className="text-underneath">{textUnderneath}</div>}
                    <CommonError error={error} componentId={rest.name} />
                </div>
            </div>
        ),
        [
            autoAdjustHeight,
            error,
            handleBlur,
            handleValueChange,
            id,
            label,
            maxLength,
            prefix,
            required,
            rest,
            rightComponent,
            rows,
            subLabel,
            suffix,
            textUnderneath,
            value,
        ]
    );
});

TextArea.displayName = 'TextArea';

export default React.memo(TextArea);
