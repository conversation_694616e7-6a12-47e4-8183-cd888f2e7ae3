/* eslint-disable max-len */
import { mount, render, shallow } from 'enzyme';
import React from 'react';
import { RequiredLabel } from '../Common';
import Icons from '../Icons/Icons';
import Input, { InputForwardedRef, InputProps } from '../Input/Input';

/**
 * Unit test for Input component
 */
describe('Unit test for Input component', () => {
    /**
     * setup element
     * @param p InputProps
     */
    const setup = (p?: InputProps) => {
        const ref = React.createRef<InputForwardedRef>();
        const r = render(<Input {...p} ref={ref} />);
        const s = shallow(<Input {...p} ref={ref} />);
        const m = mount(<Input {...p} ref={ref} />);

        return {
            r: r,
            s: s,
            m: m,
            props: p,
        };
    };

    describe('should display enough elements', () => {
        const { s, m } = setup({
            name: 'testInput',
            label: 'label',
            subLabel: 'subLabel',
            placeholder: 'placeholder',
            error: 'input error',
            type: 'text',
        });

        it('should display without props', () => {
            const { s } = setup();

            // find by component name
            const el = s.find('input');
            expect(el.length).toEqual(1);
        });

        it('should display label', () => {
            // find by component name
            const el = s.find(RequiredLabel);
            expect(el.length).toEqual(1);
        });

        it('should display sub label', () => {
            const { s } = setup({
                subLabel: 'subLabel',
            });
            // find by selector
            const el = s.find('div.sub-label');
            expect(el.length).toEqual(1);

            expect(el.text()).toEqual('subLabel');
        });

        it('should display error', () => {
            // find by selector
            const el = m.find('div#testInput-error');

            expect(el.length).toEqual(1);

            expect(el.text()).toEqual('input error');
        });

        it('should display placeholder', () => {
            // find by selector
            const el = s.find('input');
            expect(el.length).toEqual(1);

            expect(el.props().placeholder).toEqual('placeholder');
        });

        it('should input display with empty value', () => {
            // find by selector
            const el = s.find('input');
            expect(el.length).toEqual(1);

            expect(el.props().value).toEqual('');
        });

        it('should input display value when init', () => {
            const { s } = setup({
                label: 'label',
                value: 'this is text value',
            });
            // find by selector
            const el = s.find('input');
            expect(el.length).toEqual(1);

            expect(el.props().value).toEqual('this is text value');
        });

        it('should display input with type "text"', () => {
            // find by selector
            const el = s.find('input');
            expect(el.length).toEqual(1);
            expect(el.props().type).toEqual('text');
        });

        it('should display input with type "password"', () => {
            const { s } = setup({
                label: 'label',
                required: true,
                type: 'password',
            });

            // find by selector
            const el = s.find('input');
            expect(el.length).toEqual(1);
            expect(el.props().type).toEqual('password');
        });

        it('should display OpenEyeIcon when input type is "password"', () => {
            const { s } = setup({
                label: 'label',
                required: true,
                type: 'password',
            });

            const el = s.find(Icons.OpenEyeIcon);

            expect(el.length).toEqual(1);
        });

        it('input type should be "tel" when useNumberFormat is passed', () => {
            const { s } = setup({
                label: 'label',
                required: true,
                useNumberFormat: true,
            });
            // find by selector
            const el = s.find('input');
            expect(el.length).toEqual(1);
            expect(el.props().type).toEqual('tel');
        });

        it('should display label [必須] if required is passed', () => {
            const { m } = setup({
                label: 'label',
                required: true,
            });

            const el = m.find('.required-container');

            expect(el.length).toEqual(1);

            expect(el.text()).toEqual('必須');
        });

        it('should display label [任意] if required is not passed (or false)', () => {
            const { m } = setup({
                label: 'label',
                required: false,
            });

            const el = m.find('.optional-container');

            expect(el.length).toEqual(1);

            expect(el.text()).toEqual('任意');
        });
    });

    describe('Actions', () => {
        it('should change input type when initial with type is password and click on toggle button', async () => {
            const { m } = setup({
                type: 'password',
            });

            // init with type password
            expect(m.find('input').props().type).toEqual('password');

            // find toggle button
            const toggleButton = m.find('div.password-field');

            // simulate click action
            toggleButton.simulate('click');

            // input type should be changed to text
            expect(m.find('input').props().type).toEqual('text');
            // <Icons.ClosedEyeIcon /> should be display
            const closedEyeIcon = m.find(Icons.ClosedEyeIcon);
            expect(closedEyeIcon.length).toEqual(1);

            // click on it
            toggleButton.simulate('click');

            // input type should be changed to text
            expect(m.find('input').props().type).toEqual('password');
            // <Icons.OpenEyeIcon /> should be display
            const openEyeIcon = m.find(Icons.OpenEyeIcon);
            expect(openEyeIcon.length).toEqual(1);
        });

        describe('should call handleBlur when input is blur', () => {
            it('should not call onBlur when there is not onBlur function is passed on props', () => {
                const onBlur = jest.fn();
                const { m } = setup();

                const input = m.find('input');
                input.simulate('focus');

                input.simulate('blur');

                expect(onBlur).not.toHaveBeenCalled();
            });

            it('should call onBlur function when input is blur and onblur is passed on props', () => {
                const onBlur = jest.fn();
                const { m } = setup({
                    onBlur,
                });

                const input = m.find('input');
                input.simulate('focus');

                input.simulate('blur');

                expect(onBlur).toHaveBeenCalledTimes(1);
            });
        });

        describe('should call handleChange when input change value', () => {
            it('should not call onChange when input value change and there is not onChange is passed on props', () => {
                const onChange = jest.fn();

                const { m } = setup();

                // find input
                const input = m.find('input');
                // simulate input value change
                input.simulate('change', {
                    target: { value: 'this is value' },
                });

                expect(m.find('input').props().value).toBe('this is value');
                expect(onChange).not.toHaveBeenCalled();
            });

            it('should call onChange when input value change and onChange is passed on props', () => {
                const onChange = jest.fn();

                const { m } = setup({
                    onChange,
                });

                // find input
                const input = m.find('input');
                // simulate input value change
                input.simulate('change', {
                    target: { value: 'this is value' },
                });

                expect(m.find('input').props().value).toBe('this is value');
                expect(onChange).toHaveBeenCalled();
            });

            describe('when useNumberFormat is passed on props', () => {
                it('should value of input is number', () => {
                    const { m } = setup({
                        useNumberFormat: true,
                    });

                    // find input
                    const input = m.find('input');
                    // simulate input value change
                    input.simulate('change', {
                        target: { value: '12this345' },
                    });
                    expect(m.find('input').props().value).toBe('12345');
                });

                it('should format value when separator is passed in useNumberFormat', () => {
                    const { m } = setup({
                        useNumberFormat: {
                            separator: ',',
                        },
                    });

                    // find input
                    const input = m.find('input');
                    // simulate input value change
                    input.simulate('change', {
                        target: { value: '123456789' },
                    });
                    expect(m.find('input').props().value).toBe('123,456,789');
                });

                it('should display old value when new value greater than maxValue passed in useNumberFormat', () => {
                    const { m } = setup({
                        useNumberFormat: {
                            maxValue: 10000,
                        },
                    });

                    // find input
                    const input = m.find('input');
                    // change input's value to 9000
                    input.simulate('change', {
                        target: { value: '9000' },
                    });

                    // change input's value 90000
                    input.simulate('change', {
                        target: { value: '90000' },
                    });

                    // expect input's value is still 9000 because new value (90000) is greater than maxValue (10000)
                    expect(m.find('input').props().value).toBe('9000');
                });

                it('should not allow enter 0 at head when allowLeadingZero passed in useNumberFormat is false', () => {
                    const { m } = setup({
                        useNumberFormat: {
                            allowLeadingZero: false,
                        },
                    });

                    // find input
                    const input = m.find('input');
                    // change input's value to 1000
                    input.simulate('change', {
                        target: { value: '1000' },
                    });

                    // insert 0 at head of input'value
                    input.simulate('change', {
                        target: { value: '01000' },
                    });

                    // expect input's value is still 1000 because of allowLeadingZero is false
                    expect(m.find('input').props().value).toBe('1000');
                });

                describe('when multiply is passed in useNumberFormat is true', () => {
                    describe('case inputType is insertText', () => {
                        describe('case multiplyValue is passed in useNumberFormat', () => {
                            it('should return empty when data is null', () => {
                                const onChange = jest.fn().mockImplementation((e: any) => {
                                    // expect value is empty
                                    expect(e.target.value).toBe('');
                                });
                                const { s } = setup({
                                    onChange,
                                    value: '',
                                    useNumberFormat: {
                                        x1000: true,
                                    },
                                });

                                const input = s.find('input').at(0);

                                input.simulate('change', {
                                    target: {
                                        value: '',
                                    },
                                    nativeEvent: { data: null, inputType: 'insertText' },
                                });

                                expect(onChange).toHaveBeenCalled();
                            });

                            it('should return value equal value of input * multiplyValue', () => {
                                const onChange = jest.fn().mockImplementation((e: any) => {
                                    // expect value is 1000
                                    expect(e.target.value).toBe('1000');
                                });
                                const { s } = setup({
                                    onChange,
                                    value: '',
                                    useNumberFormat: {
                                        x1000: true,
                                    },
                                });

                                const input = s.find('input').at(0);

                                input.simulate('change', {
                                    target: {
                                        value: '1',
                                    },
                                    nativeEvent: { data: '1', inputType: 'insertText' },
                                });

                                expect(onChange).toHaveBeenCalled();
                            });

                            it('should return value equal value of input * multiplyValue', () => {
                                const onChange = jest.fn().mockImplementation((e: any) => {
                                    // expect value is 1000
                                    expect(e.target.value).toBe('1000');
                                });
                                const { s } = setup({
                                    onChange,
                                    useNumberFormat: {
                                        x1000: true,
                                        maxValue: 50000,
                                    },
                                });

                                const input = s.find('input').at(0);

                                input.simulate('change', {
                                    target: {
                                        value: '1000',
                                    },
                                    nativeEvent: { data: '0', inputType: 'insertText' },
                                });

                                expect(onChange).toHaveBeenCalled();
                            });

                            it('should return value equal value of input * multiplyValue', () => {
                                const onChange = jest.fn().mockImplementation((e: any) => {
                                    // expect value is 1000
                                    expect(e.target.value).toBe('1000');
                                });
                                const { s } = setup({
                                    onChange,
                                    useNumberFormat: {
                                        x1000: true,
                                    },
                                });

                                const input = s.find('input').at(0);

                                input.simulate('change', {
                                    target: {
                                        value: '1000',
                                    },
                                    nativeEvent: { data: '0', inputType: 'insertText' },
                                });

                                expect(onChange).toHaveBeenCalled();
                            });

                            it('should return maxValue', () => {
                                const onChange = jest.fn().mockImplementation((e: any) => {
                                    // expect value is 1000
                                    expect(e.target.value).toBe('1000');
                                });
                                const { s } = setup({
                                    onChange,
                                    useNumberFormat: {
                                        x1000: true,
                                        maxValue: 1000,
                                    },
                                });

                                const input = s.find('input').at(0);

                                input.simulate('change', {
                                    target: {
                                        value: '2',
                                    },
                                    nativeEvent: { data: '2', inputType: 'insertText' },
                                });

                                expect(onChange).toHaveBeenCalled();
                            });
                        });
                    });

                    describe('case inputType is deleteContentBackward', () => {
                        it('should return empty when delete backward', () => {
                            const onChange = jest.fn().mockImplementation((e: any) => {
                                // expect value is empty
                                expect(e.target.value).toBe('');
                            });
                            const { s } = setup({
                                onChange,
                                value: '1000',
                                useNumberFormat: {
                                    x1000: true,
                                },
                            });

                            const input = s.find('input').at(0);

                            input.simulate('change', {
                                target: {
                                    value: '100',
                                },
                                nativeEvent: { data: null, inputType: 'deleteContentBackward' },
                            });

                            expect(onChange).toHaveBeenCalled();
                        });

                        it('should delete number at thousand unit', () => {
                            const onChange = jest.fn().mockImplementation((e: any) => {
                                // expect value is 12000
                                expect(e.target.value).toBe('12000');
                            });
                            const { s } = setup({
                                onChange,
                                value: '123000',
                                useNumberFormat: {
                                    x1000: true,
                                },
                            });

                            const input = s.find('input').at(0);

                            input.simulate('change', {
                                target: {
                                    value: '12300',
                                },
                                nativeEvent: { data: null, inputType: 'deleteContentBackward' },
                            });

                            expect(onChange).toHaveBeenCalled();
                        });

                        it('should delete number at unit', () => {
                            const onChange = jest.fn().mockImplementation((e: any) => {
                                // expect value is 1000
                                expect(e.target.value).toBe('100');
                            });
                            const { s } = setup({
                                onChange,
                                value: '1000',
                                useNumberFormat: {
                                    x1000: true,
                                },
                            });

                            const input = s.find('input').at(0);

                            input.simulate('change', {
                                target: {
                                    value: '100',
                                },
                                nativeEvent: { data: null, inputType: 'deleteContentBackward' },
                            });

                            expect(onChange).toHaveBeenCalled();
                        });
                    });
                });
            });
        });
    });
});
