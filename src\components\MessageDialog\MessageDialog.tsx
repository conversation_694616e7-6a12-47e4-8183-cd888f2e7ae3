import InfoOutlinedIcon from '@mui/icons-material/InfoOutlined';
import WarningAmberOutlinedIcon from '@mui/icons-material/WarningAmberOutlined';
import { Dialog } from '@mui/material';
import clsx from 'clsx';
import { ReactNode, memo, useCallback, useEffect, useMemo } from 'react';
import useCommon from '../../hooks/common';
import { CommonMessageDialog, DialogCallback } from '../../models/common';
import CommonUtils from '../../utils/common';
import Button from '../Button/Button';
import Icons from '../Icons/Icons';
import './styles.scss';

/**
 * MessageDialogItem
 * @param item CommonMessageDialog
 * @returns React.JSX.Element
 */
export const MessageDialogItem = (item: CommonMessageDialog): React.JSX.Element => {
    // get dialog information
    const {
        id,
        type = 'INFO',
        title,
        message,
        detail,
        onClose,
        actions,
        useDialogCloseButton,
        useBackdropDismiss,
        autoClose,
        iconClass,
    } = useMemo(() => item, [item]);

    // dialog icon
    const Icon = useMemo((): React.JSX.Element => {
        switch (type) {
            case 'WARNING':
                return <WarningAmberOutlinedIcon className="icon text-warning" />;
            case 'ERROR':
                return <Icons.UnreadNotificationIcon width={70} height={70} />;
            case 'SUCCESS':
                return <Icons.SuccessIcon width={70} height={70} />;
            case 'INFO':
            default:
                return <InfoOutlinedIcon className="icon text-success" />;
        }
    }, [type]);

    /**
     * onCloseButtonClickHandler
     */
    const onCloseButtonClickHandler = useCallback(() => {
        // execute onClose function if available
        onClose?.();

        // close message by id
        CommonUtils.closeMessageById(id);
    }, [id, onClose]);

    /**
     * renderActionItem
     * @param item ReactNode
     */
    const renderActionItem = useCallback((item: ReactNode) => {
        return item;
    }, []);

    /**
     * onBackdropClickHandler
     */
    const onBackdropClickHandler = useCallback(() => {
        if (useBackdropDismiss) {
            const { callback } = useBackdropDismiss as DialogCallback;

            // close message by id
            CommonUtils.closeMessageById(id);

            // execute callback function if available
            callback?.();
        }
    }, [id, useBackdropDismiss]);

    /**
     * onCloseIconClickHandler
     */
    const onCloseIconClickHandler = useCallback(() => {
        if (useDialogCloseButton) {
            const { callback } = useDialogCloseButton as DialogCallback;

            // close message by id
            CommonUtils.closeMessageById(id);

            // execute callback function if available
            callback?.();
        }
    }, [id, useDialogCloseButton]);

    // eslint-disable-next-line consistent-return
    useEffect(() => {
        if (autoClose) {
            const timer = setTimeout(() => {
                CommonUtils.closeMessageById(id);
                onClose?.();
            }, autoClose * 1000);

            return () => clearTimeout(timer);
        }
    }, [autoClose, id, onClose]);

    return useMemo(
        () => (
            <>
                <div className="message-dialog-backdrop" onClick={onBackdropClickHandler} />
                <div
                    className="d-flex justify-content-center align-items-center position-absolute"
                    style={{
                        left: 16,
                        right: 16,
                    }}
                >
                    <div className="px-16 py-32 radius-8 message-dialog-icon-container">
                        <div className={clsx('d-flex justify-content-center align-items-center', iconClass)}>
                            {Icon}
                        </div>
                        <div className="p-8 py-24">
                            {title && <div className="title-error">{title}</div>}
                            <div
                                className={clsx(
                                    'd-flex justify-content-center text-center',
                                    'fs-16 ff-noto-bold white-space-pre-line'
                                )}
                            >
                                {message}
                            </div>
                            {detail && (
                                // eslint-disable-next-line max-len
                                <div className="d-flex justify-content-center text-center fs-14 py-8 white-space-pre-line">
                                    {detail}
                                </div>
                            )}
                        </div>
                        {actions ? (
                            <div
                                className={clsx('d-flex', {
                                    'flex-column': actions.direction === 'vertical',
                                    'flex-row': actions.direction === 'horizontal',
                                })}
                            >
                                {actions?.children?.map(renderActionItem)}
                            </div>
                        ) : (
                            <div className="d-flex justify-content-center">
                                <Button
                                    variant="outlined"
                                    maxHeight={44}
                                    className="w-100"
                                    onClick={onCloseButtonClickHandler}
                                >
                                    とじる
                                </Button>
                            </div>
                        )}
                    </div>
                    {useDialogCloseButton && (
                        <div className="app-fixed-element top" onClick={onCloseIconClickHandler}>
                            <div className="d-flex justify-content-end">
                                <Icons.DialogCloseIcon />
                            </div>
                        </div>
                    )}
                </div>
            </>
        ),
        [
            Icon,
            actions,
            detail,
            iconClass,
            message,
            onBackdropClickHandler,
            onCloseButtonClickHandler,
            onCloseIconClickHandler,
            renderActionItem,
            title,
            useDialogCloseButton,
        ]
    );
};

/**
 * MessageDialog
 */
const MessageDialog = (): React.JSX.Element => {
    // get message dialogs
    const { messageDialogs = [] } = useCommon();

    // open variable
    const open = useMemo(() => messageDialogs.length > 0, [messageDialogs]);

    return useMemo(
        () => (
            <Dialog
                open={open}
                className="common-message-dialog-component"
                disableScrollLock={false}
                disableAutoFocus={true}
                disableEnforceFocus={true}
            >
                <div
                    className={clsx('d-flex w-100 h-100 message-dialog-content position-relative', {
                        'justify-content-center': true,
                        'align-items-center': true,
                    })}
                >
                    {messageDialogs.map((item) => (
                        <MessageDialogItem {...item} key={item.id} />
                    ))}
                </div>
            </Dialog>
        ),
        [messageDialogs, open]
    );
};

export default memo(MessageDialog);
