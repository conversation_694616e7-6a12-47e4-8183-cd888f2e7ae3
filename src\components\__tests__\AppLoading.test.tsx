import { mount, shallow } from 'enzyme';
import { Provider } from 'react-redux';
import store from '../../redux';
import CommonUtils from '../../utils/common';
import AppLoading from '../AppLoading/AppLoading';
import ReactLoading from 'react-loading';

/**
 * Unit test for AppLoading component
 */
describe('Unit test for AppLoading component', () => {
    /**
     * setup element
     * @param p InputProps
     */
    const setup = () => {
        const s = shallow(
            <Provider store={store}>
                <AppLoading />
            </Provider>
        );
        const m = mount(
            <Provider store={store}>
                <AppLoading />
            </Provider>
        );

        return {
            s: s,
            m: m,
        };
    };

    /**
     * should render when lockCount greater than 0
     */
    it('should render when lockCount greater than 0', () => {
        // call lockScreen from CommonUtils to increase lockCount
        CommonUtils.lockScreen();

        const { m } = setup();

        expect(m.find('.common-app-loading-component').length).toBeGreaterThan(0);
    });

    /**
     * should display ReactLoading
     */
    it('should display ReactLoading', () => {
        // call lockScreen from CommonUtils to increase lockCount
        CommonUtils.lockScreen();

        const { m } = setup();

        const reactLoading = m.find(ReactLoading);

        expect(reactLoading.length).toBe(1);

        expect(reactLoading.props().type).toBe('spokes');
        expect(reactLoading.props().color).toBe('#fff');
        expect(reactLoading.props().width).toBe(35);
        expect(reactLoading.props().height).toBe(35);
    });
});
