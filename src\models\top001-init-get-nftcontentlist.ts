import { CommonCallback } from './common';

/**
 * Top001InitGetNftContentListModel
 */
export interface Top001InitGetNftContentListModel extends CommonCallback {
    // ブランドID
    BrandID: string;
    // 1ページあたりの取得アイテム数
    PageSize: number;
}

/**
 * Top001InitGetNftContentItem
 */
export interface Top001InitGetNftContentItem {
    // NFTID
    NFTContentID: string;
    // NFT画像
    ContentImage: string;
    // NFT名
    ContentName: string;
    // NFT価格
    ContentPrice: string;
    // NFTカテゴリID
    NFTCategoryID: string;
}

/**
 * Top001InitGetNftContentListResult
 */
export interface Top001InitGetNftContentListResult {
    // NFT一覧
    NFTContentList: Top001InitGetNftContentItem[];
}
