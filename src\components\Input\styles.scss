.common-input-component {
    user-select: none;
    -moz-user-select: none;
    -webkit-user-select: none;
    -ms-user-select: none;
    padding-top: 16px;
    padding-bottom: 20px;

    .MuiOutlinedInput-input {
        min-height: 44px;
        height: 44px;
        max-height: 45px;
        padding-left: 10px !important;
        padding-right: 10px !important;
        padding-top: 0px !important;
        padding-bottom: 0px !important;
        overflow: hidden !important;
    }

    .label-session {
        margin-bottom: 12px;

        .sub-label {
            font-size: 12px;
        }
    }

    .main-input {
        flex: 1;
        box-shadow: 0px 0px 0px 1px #bfbfbf;
        border-radius: 8px;
        overflow: hidden !important;
        &:has(input:hover) {
            box-shadow: 0px 0px 0px 2px var(--app-base-color);
        }
        &:has(input:focus) {
            box-shadow: 0px 0px 0px 2px var(--app-base-color);
        }
        &.error {
            border-color: #d31e2d !important;
            background-color: #fce9e8 !important;
            > input {
                background-color: #fce9e8 !important;
            }
        }
        input {
            width: 100%;
            font-size: 15px;
            color: #222222;
            min-height: 44px;
            line-height: normal !important;
            padding-left: 10px !important;
            padding-right: 10px !important;
            height: 100% !important;
            border: none !important;
            &:hover {
                border: none !important;
                outline: none !important;
            }
            &:focus {
                border: none !important;
                outline: none !important;
            }
            &:active {
                border: none !important;
                outline: none !important;
            }
            &::placeholder {
                color: #707070;
                opacity: 1;
            }
            &:-ms-input-placeholder {
                color: #707070;
            }

            &::-ms-input-placeholder {
                color: #707070;
            }
            &::-ms-reveal {
                display: none;
            }
            &::-ms-clear {
                display: none;
            }
        }
        .password-field {
            cursor: pointer;
            margin-right: 10px;
            margin-left: 10px;
            height: 100%;
            min-height: 30px;
            min-width: 30px;
        }
    }
}
