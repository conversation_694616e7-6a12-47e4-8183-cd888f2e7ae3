import { Button as ButtonBase } from '@mui/material';
import clsx from 'clsx';
import React, { ButtonHTMLAttributes, CSSProperties, memo, useMemo } from 'react';
import SpokesLoading from '../SpokesLoading';
import './styles.scss';

/**
 * LoadingProps
 */
export interface LoadingProps {
    /**
     * isLoading
     */
    isLoading?: boolean;
    /**
     * loadingProps
     */
    loadingProps?: {
        color?: string;
        type?: 'blank' | 'balls' | 'bars' | 'bubbles' | 'cubes' | 'cylon' | 'spin' | 'spinningBubbles' | 'spokes';
    };
}

// PickedButtonElement
type PickedButtonElement = Pick<ButtonHTMLAttributes<HTMLButtonElement>, 'name' | 'className' | 'type' | 'disabled'>;

export interface ActionHistory {
    HistoryType: string;
    HistoryKey: string;
    HistoryValue: string;
}

/**
 * ButtonProps
 */
export interface ButtonProps extends CSSProperties, LoadingProps, PickedButtonElement {
    /**
     * The variant to use.
     * @default 'contained'
     */
    variant?: 'text' | 'outlined' | 'contained';
    /**
     * The content of the component.
     */
    children?: React.ReactNode;
    /**
     * If `true`, no elevation is used.
     * @default false
     */
    disableElevation?: boolean;
    /**
     * If `true`, the  keyboard focus ripple is disabled.
     * @default false
     */
    disableFocusRipple?: boolean;
    /**
     * disableHover
     */
    disableHover?: boolean;
    /**
     * onClick
     */
    onClick?: (event?: React.MouseEvent) => void;
    /**
     * button's id
     */
    id?: string;
    /**
     * action key
     */
    actionKey?: string;

    /**
     * action history
     */
    actionHistory?: ActionHistory;
}

/**
 * Button
 */
const Button = (props: ButtonProps): React.JSX.Element => {
    // Material Button props
    const {
        children,
        variant = 'contained',
        type,
        name,
        disabled,
        className,
        isLoading,
        loadingProps,
        disableElevation,
        disableFocusRipple,
        disableHover,
        onClick,
        id,
        ...propsRest
    } = useMemo(() => props, [props]);

    // css properties
    const { borderRadius = 8, minHeight = 44, fontSize = 17, ...cssRest } = useMemo(() => propsRest, [propsRest]);

    return useMemo(
        () => (
            <ButtonBase
                name={name}
                type={type}
                className={clsx('common-button-component', className, {
                    'disabled-hover': disableHover,
                    loading: isLoading,
                })}
                variant={variant}
                disabled={disabled || isLoading}
                disableElevation={disableElevation}
                disableFocusRipple={disableFocusRipple}
                onClick={onClick}
                style={{
                    borderRadius,
                    minHeight,
                    fontSize,
                    ...cssRest,
                }}
                id={id}
            >
                {isLoading ? (
                    <div className="d-flex justify-content-center align-items-center button-loading-container">
                        <SpokesLoading color={loadingProps?.color} />
                    </div>
                ) : (
                    children
                )}
            </ButtonBase>
        ),
        [
            name,
            type,
            className,
            disableHover,
            isLoading,
            variant,
            disabled,
            disableElevation,
            disableFocusRipple,
            onClick,
            borderRadius,
            minHeight,
            fontSize,
            cssRest,
            id,
            loadingProps?.color,
            children,
        ]
    );
};

export default memo(Button);
