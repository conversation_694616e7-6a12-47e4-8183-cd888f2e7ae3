import API from '../constants/api';
import { BaseResponse } from '../models/common';
import {
    Wallet001InitGetBannerListModel,
    Wallet001InitGetBannerListResult,
} from '../models/wallet001-init-get-banner-list';
import { Wallet001InitGetCoinItem, Wallet001InitGetCoinModel } from '../models/wallet001-init-get-coin';
import {
    Wallet001InitGetCouponListModel,
    Wallet001InitGetCouponListResult,
} from '../models/wallet001-init-get-coupon-list';
import {
    Wallet001InitGetLimitedStoreListItem,
    Wallet001InitGetLimitedStoreListModel,
} from '../models/wallet001-init-get-limited-store-list';
import {
    Wallet001InitGetNewsUnreadLatestModel,
    Wallet001InitGetNewsUnreadLatestResult,
} from '../models/wallet001-init-get-news-unread-latest';
import {
    Wallet001InitGetRecommendationPopupDetailModel,
    Wallet001InitGetRecommendationPopupDetailResult,
} from '../models/wallet001-init-get-recommendation-popup-detail';
import {
    Wallet001InitGetUserTicketListModel,
    Wallet001InitGetUserTicketListResult,
} from '../models/wallet001-init-get-user-ticket-list';
import { Wallet001InitGetUserInfoModel, Wallet001InitGetUserInfoResult } from '../models/wallet001-init-get-userinfo';
import {
    Wallet001MainGetChargeAbleFlagModel,
    Wallet001MainGetChargeAbleFlagResult,
} from '../models/wallet001-main-get-chargeableflag';
import {
    Wallet001MainGetPayableFlagModel,
    Wallet001MainGetPayableFlagResult,
} from '../models/wallet001-main-get-payableflag';
import createAPI from './baseApi';

/**
 * WalletAPI
 */
class WalletAPI {
    /**
     * wallet001InitGetUserInfo
     * @param data Wallet001InitGetUserInfoModel
     * @returns Promise<BaseResponse<Wallet001InitGetUserInfoResult>>
     */
    static wallet001InitGetUserInfo = (
        data: Wallet001InitGetUserInfoModel
    ): Promise<BaseResponse<Wallet001InitGetUserInfoResult>> => {
        return createAPI<Wallet001InitGetUserInfoResult>({
            url: API.WALLET001_INIT_GET_USERINFO,
            data,
        });
    };

    /**
     * wallet001InitGetNewsUnreadLatest
     * @param data Wallet001InitGetNewsUnreadLatestModel
     * @returns Promise<BaseResponse<Wallet001InitGetNewsUnreadLatestResult>>
     */
    static wallet001InitGetNewsUnreadLatest = (
        data: Wallet001InitGetNewsUnreadLatestModel
    ): Promise<BaseResponse<Wallet001InitGetNewsUnreadLatestResult>> => {
        return createAPI<Wallet001InitGetNewsUnreadLatestResult>({
            url: API.WALLET001_INIT_GET_NEWS_UNREAD_LATEST,
            data,
        });
    };

    /**
     * wallet001InitGetCoin
     * @param data Wallet001InitGetCoinModel
     * @returns Promise<BaseResponse<Wallet001InitGetCoinItem[]>>
     */
    static wallet001InitGetCoin = (
        data: Wallet001InitGetCoinModel
    ): Promise<BaseResponse<Wallet001InitGetCoinItem[]>> => {
        return createAPI<Wallet001InitGetCoinItem[]>({
            url: API.WALLET001_INIT_GET_COIN,
            data,
        });
    };

    /**
     * wallet001InitGetUserTicketList
     * @param data Wallet001InitGetUserTicketListModel
     * @returns Promise<BaseResponse<Home001InitGetEventTicketListResult>>
     */
    static wallet001InitGetUserTicketList = (
        data: Wallet001InitGetUserTicketListModel
    ): Promise<BaseResponse<Wallet001InitGetUserTicketListResult>> => {
        return createAPI<Wallet001InitGetUserTicketListResult>({
            url: API.WALLET001_INIT_GET_USER_TICKET_LIST,
            data,
        });
    };

    /**
     * wallet001InitGetCouponList
     * @param data Wallet001InitGetCouponListModel
     * @returns Promise<BaseResponse<Wallet001InitGetCouponListResult>>
     */
    static wallet001InitGetCouponList = (
        data: Wallet001InitGetCouponListModel
    ): Promise<BaseResponse<Wallet001InitGetCouponListResult>> => {
        return createAPI<Wallet001InitGetCouponListResult>({
            url: API.WALLET001_INIT_GET_COUPON_LIST,
            data,
        });
    };

    /**
     * wallet001InitGetLimitedStoreList
     * @param data Wallet001InitGetLimitedStoreListModel
     * @returns Promise<BaseResponse<Wallet001InitGetLimitedStoreListItem[]>>
     */
    static wallet001InitGetLimitedStoreList = (
        data: Wallet001InitGetLimitedStoreListModel
    ): Promise<BaseResponse<Wallet001InitGetLimitedStoreListItem[]>> => {
        return createAPI<Wallet001InitGetLimitedStoreListItem[]>({
            url: API.WALLET001_INIT_GET_LIMITED_STORE_LIST,
            data,
        });
    };

    /**
     * wallet001InitGetBannerList
     * @param data Wallet001InitGetBannerListModel
     * @returns Promise<BaseResponse<Wallet001InitGetBannerListResult>>
     */
    static wallet001InitGetBannerList = (
        data: Wallet001InitGetBannerListModel
    ): Promise<BaseResponse<Wallet001InitGetBannerListResult>> => {
        return createAPI<Wallet001InitGetBannerListResult>({
            url: API.WALLET001_INIT_GET_BANNER_LIST,
            data,
        });
    };

    /**
     * wallet001MainGetPayableFlag
     * @param data Wallet001MainGetPayableFlagModel
     * @returns Promise<BaseResponse<Wallet001MainGetPayableFlagResult>>
     */
    static wallet001MainGetPayableFlag = (
        data: Wallet001MainGetPayableFlagModel
    ): Promise<BaseResponse<Wallet001MainGetPayableFlagResult>> => {
        return createAPI({
            url: API.WALLET001_MAIN_GET_PAYABLEFLAG,
            data,
        });
    };

    /**
     * wallet001MainGetChargeAbleFlag
     * @param data Wallet001MainGetChargeAbleFlagModel
     * @returns Promise<BaseResponse<Wallet001MainGetChargeAbleFlagResult>>
     */
    static wallet001MainGetChargeAbleFlag = (
        data: Wallet001MainGetChargeAbleFlagModel
    ): Promise<BaseResponse<Wallet001MainGetChargeAbleFlagResult>> => {
        return createAPI({
            url: API.WALLET001_MAIN_GET_CHARGEABLEFLAG,
            data,
        });
    };

    /**
     * wallet001InitGetRecommendationPopupDetail
     * @param data Wallet001InitGetRecommendationPopupDetailModel
     * @returns Promise<BaseResponse<Wallet001InitGetRecommendationPopupDetailResult>>
     */
    static wallet001InitGetRecommendationPopupDetail = (
        data: Wallet001InitGetRecommendationPopupDetailModel
    ): Promise<BaseResponse<Wallet001InitGetRecommendationPopupDetailResult>> => {
        return createAPI<Wallet001InitGetRecommendationPopupDetailResult>({
            url: API.WALLET001_INIT_GET_RECOMMENDATION_POPUP_DETAIL,
            data,
        });
    };
}

export default WalletAPI;
