/* eslint-disable @typescript-eslint/no-explicit-any */
import { ReactNode } from 'react';
import { AxiosError, GenericAbortSignal } from './../../node_modules/axios/index.d';

/**
 * Base
 * Generic interface
 */
export interface Base<T> {
    /**
     * status
     */
    status?: number;
    /**
     * result
     */
    result?: T;
    /**
     * statusCode
     */
    statusCode?: number;
    /**
     * Message
     */
    Message?: string;
    /**
     * ErrMessage
     */
    ErrMessage?: string;
}

/**
 * BaseResponse
 */
export type BaseResponse<T = unknown> = Base<T> & {
    /**
     * isSuccess
     */
    isSuccess: boolean;
    /**
     * error
     * error object, available when `isSuccess` is `false`
     */
    error?: AxiosError;
    /**
     * code
     */
    code?: string;
};

// APIResponse
export type APIResponse<T = unknown, K = unknown> = BaseResponse<T> & K;

/**
 * SuccessCallback
 */
export type SuccessCallback = (response: APIResponse<any, any>) => void;

/**
 * ErrorCallback
 */
export type ErrorCallback = (error: any) => void;

/**
 * CommonCallback
 */
export interface CommonCallback {
    /**
     * useLock
     * Default `true`
     */
    useLock?: boolean;
    /**
     * successCallback
     */
    successCallback?: SuccessCallback;
    /**
     * errorCallback
     */
    errorCallback?: ErrorCallback;
}

type RequestDataType<T = unknown> = CommonCallback & T;

/**
 * APIRequestOptions
 */
export interface APIRequestOptions {
    /**
     * url
     * request url
     */
    url: string;
    /**
     * data
     * request data. it will passed as a param if method is GET
     */
    data?: RequestDataType;
    /**
     * method
     * request method
     */
    method?: string;
    /**
     * signal
     */
    signal?: GenericAbortSignal;
}

/**
 * APICommonPayload
 */
export interface APICommonPayload extends Omit<CommonCallback, 'useLock'> {
    /**
     * promiser
     */
    promiser: Promise<BaseResponse>;
}

const TypeValue = {
    INFO: 'INFO',
    ERROR: 'ERROR',
    WARNING: 'WARNING',
    SUCCESS: 'SUCCESS',
};

// DialogType
export const DialogTypeValues = Object.values(TypeValue);

// DialogKeyType
export type DialogKeyType = keyof typeof TypeValue;

/**
 * DialogCallback
 */
export interface DialogCallback {
    /**
     * callback function
     */
    callback?: () => void;
}

/**
 * CommonMessageDialog
 */
export interface CommonMessageDialog {
    /**
     * dialog's id
     */
    id: string;
    /**
     * dialog type
     */
    type?: DialogKeyType;
    /**
     * title
     */
    title?: ReactNode;
    /**
     * message
     */
    message?: ReactNode;
    /**
     * detail
     */
    detail?: ReactNode;
    /**
     * actions
     */
    actions?: {
        direction?: 'vertical' | 'horizontal';
        children?: ReactNode[];
    };
    /**
     * onClose
     */
    onClose?: () => void;
    /**
     * useDialogCloseButton
     */
    useDialogCloseButton?: boolean | DialogCallback;
    /**
     * useBackdropDismiss
     */
    useBackdropDismiss?: boolean | DialogCallback;
    /**
     * autoClose
     */
    autoClose?: number;
    /**
     * iconClass
     */
    iconClass?: string;
}
