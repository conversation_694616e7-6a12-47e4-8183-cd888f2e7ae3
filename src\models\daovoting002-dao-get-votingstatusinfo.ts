import { CommonCallback } from './common';

/**
 * DAOVoting002DaoGetVotingStatusInfoModel
 */
export interface DAOVoting002DaoGetVotingStatusInfoModel extends CommonCallback {
    // DAOID
    DaoID: string;
    // 投票テーマID
    ThemeID: string;
}

/**
 * DAOVoting002VotingStatusItem
 */
export interface DAOVoting002VotingStatusItem {
    // 投票候補内容
    CandidateContent: string;
    // 投票の重み数
    VotingWeightCount: number;
    // CandidateNo
    CandidateNo: number;
}

/**
 * DAOVoting002DaoGetVotingStatusInfoResult
 */
export interface DAOVoting002DaoGetVotingStatusInfoResult {
    // 投票状況リスト
    VotingStatusList: DAOVoting002VotingStatusItem[];
    // 投票成立数
    VotingSuccessCount: number;
    // 投票数
    VotingCount: number;
}
