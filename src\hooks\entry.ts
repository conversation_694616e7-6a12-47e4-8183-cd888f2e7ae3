import { useCallback, useMemo } from 'react';
import { useDispatch } from 'react-redux';
import EntryAPI from '../apis/entry';
import { BaseResponse } from '../models/common';
import { Entry003MainCheckAuthCodeModel } from '../models/entry003-main-check-authcode';
import { Entry006MainGetTokenModel, Entry006MainGetTokenResult } from '../models/entry006-main-get-token';
import { Entry006MainRegisterAccountModel } from '../models/entry006-main-register-account';
import { Entry002MainSendAuthCodeModel, Entry003MainSendAuthCodeModel } from '../models/entry00x-main-send-authcode';
import { apiCommon } from '../redux/actions/common';
import { Entry007MainRegisterAccountModel } from '../models/entry007-main-register-account';
import { Entry007MainGetTokenModel, Entry007MainGetTokenResult } from '../models/entry007-main-get-token';

interface UseEntry {
    postEntry002MainSendAuthCode: (payload: Entry002MainSendAuthCodeModel) => void;
    postEntry003MainCheckAuthCode: (payload: Entry003MainCheckAuthCodeModel) => void;
    postEntry003MainSendAuthCode: (payload: Entry003MainSendAuthCodeModel) => void;
    postEntry006MainGetToken: (payload: Entry006MainGetTokenModel) => void;
    postEntry007MainGetToken: (payload: Entry007MainGetTokenModel) => void;
    postEntry006MainRegisterAccount: (payload: Entry006MainRegisterAccountModel) => void;
    postEntry007MainRegisterAccount: (payload: Entry007MainRegisterAccountModel) => void;
}

/**
 * useEntry
 */
const useEntry = (): UseEntry => {
    const dispatch = useDispatch();

    /**
     * postEntry002MainSendAuthCode
     * @param payload Entry002MainSendAuthCodeModel
     */
    const postEntry002MainSendAuthCode = useCallback(
        (payload: Entry002MainSendAuthCodeModel) => {
            // get payload
            const { successCallback } = payload || {};

            // create promiser
            const promiser = EntryAPI.entry002MainSendAuthCode(payload);

            /**
             * handleResponse
             * @param response BaseResponse
             */
            const handleResponse = (response: BaseResponse): void => {
                successCallback?.(response);
            };

            // dispatch apiCommon
            dispatch(
                apiCommon({
                    promiser,
                    successCallback: handleResponse,
                    errorCallback: handleResponse,
                })
            );
        },
        [dispatch]
    );

    /**
     * postEntry003MainSendAuthCode
     * @param payload Entry003MainSendAuthCodeModel
     */
    const postEntry003MainSendAuthCode = useCallback(
        (payload: Entry003MainSendAuthCodeModel) => {
            // get payload
            const { successCallback } = payload || {};

            // create promiser
            const promiser = EntryAPI.entry003MainSendAuthCode(payload);

            /**
             * handleResponse
             * @param response BaseResponse
             */
            const handleResponse = (response: BaseResponse): void => {
                successCallback?.(response);
            };

            // dispatch apiCommon
            dispatch(
                apiCommon({
                    promiser,
                    successCallback: handleResponse,
                    errorCallback: handleResponse,
                })
            );
        },
        [dispatch]
    );

    /**
     * postEntry003MainCheckAuthCode
     * @param payload Entry003MainCheckAuthCodeModel
     */
    const postEntry003MainCheckAuthCode = useCallback(
        (payload: Entry003MainCheckAuthCodeModel) => {
            // get payload
            const { successCallback } = payload || {};

            // create promiser
            const promiser = EntryAPI.entry003MainCheckAuthCode(payload);

            /**
             * handleResponse
             * @param response BaseResponse
             */
            const handleResponse = (response: BaseResponse): void => {
                successCallback?.(response);
            };

            // dispatch apiCommon
            dispatch(
                apiCommon({
                    promiser,
                    successCallback: handleResponse,
                    errorCallback: handleResponse,
                })
            );
        },
        [dispatch]
    );

    /**
     * postEntry006MainGetToken
     * @param payload Entry006MainGetTokenModel
     */
    const postEntry006MainGetToken = useCallback(
        (payload: Entry006MainGetTokenModel) => {
            // get payload
            const { successCallback } = payload || {};

            // create promiser
            const promiser = EntryAPI.entry006MainGetToken(payload);

            /**
             * handleResponse
             * @param response BaseResponse<Entry006MainGetTokenResult>
             */
            const handleResponse = (response: BaseResponse<Entry006MainGetTokenResult>): void => {
                successCallback?.(response);
            };

            // dispatch apiCommon
            dispatch(
                apiCommon({
                    promiser,
                    successCallback: handleResponse,
                    errorCallback: handleResponse,
                })
            );
        },
        [dispatch]
    );

    /**
     * postEntry007MainGetToken
     * @param payload Entry007MainGetTokenModel
     */
    const postEntry007MainGetToken = useCallback(
        (payload: Entry007MainGetTokenModel) => {
            // get payload
            const { successCallback } = payload || {};

            // create promiser
            const promiser = EntryAPI.entry007MainGetToken(payload);

            /**
             * handleResponse
             * @param response BaseResponse<Entry007MainGetTokenResult>
             */
            const handleResponse = (response: BaseResponse<Entry007MainGetTokenResult>): void => {
                successCallback?.(response);
            };

            // dispatch apiCommon
            dispatch(
                apiCommon({
                    promiser,
                    successCallback: handleResponse,
                    errorCallback: handleResponse,
                })
            );
        },
        [dispatch]
    );

    /**
     * postEntry006MainRegisterAccount
     * @param payload Entry006MainRegisterAccountModel
     */
    const postEntry006MainRegisterAccount = useCallback(
        (payload: Entry006MainRegisterAccountModel) => {
            // get payload
            const { successCallback } = payload || {};

            // create promiser
            const promiser = EntryAPI.entry006MainRegisterAccount(payload);

            /**
             * handleResponse
             * @param response BaseResponse
             */
            const handleResponse = (response: BaseResponse): void => {
                successCallback?.(response);
            };

            // dispatch apiCommon
            dispatch(
                apiCommon({
                    promiser,
                    successCallback: handleResponse,
                    errorCallback: handleResponse,
                })
            );
        },
        [dispatch]
    );

    /**
     * postEntry007MainRegisterAccount
     * @param payload Entry007MainRegisterAccountModel
     */
    const postEntry007MainRegisterAccount = useCallback(
        (payload: Entry007MainRegisterAccountModel) => {
            // get payload
            const { successCallback } = payload || {};

            // create promiser
            const promiser = EntryAPI.entry007MainRegisterAccount(payload);

            /**
             * handleResponse
             * @param response BaseResponse
             */
            const handleResponse = (response: BaseResponse): void => {
                successCallback?.(response);
            };

            // dispatch apiCommon
            dispatch(
                apiCommon({
                    promiser,
                    successCallback: handleResponse,
                    errorCallback: handleResponse,
                })
            );
        },
        [dispatch]
    );

    return useMemo(
        () => ({
            postEntry002MainSendAuthCode,
            postEntry003MainCheckAuthCode,
            postEntry003MainSendAuthCode,
            postEntry006MainGetToken,
            postEntry007MainGetToken,
            postEntry006MainRegisterAccount,
            postEntry007MainRegisterAccount,
        }),
        [
            postEntry002MainSendAuthCode,
            postEntry003MainCheckAuthCode,
            postEntry003MainSendAuthCode,
            postEntry006MainGetToken,
            postEntry007MainGetToken,
            postEntry006MainRegisterAccount,
            postEntry007MainRegisterAccount,
        ]
    );
};

export default useEntry;
