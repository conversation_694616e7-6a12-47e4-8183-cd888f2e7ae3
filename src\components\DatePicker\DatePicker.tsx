import { TextFieldProps } from '@mui/material';
import {
    DatePicker as DatePickerBase,
    DesktopDatePicker,
    LocalizationProvider,
    MobileDatePicker,
} from '@mui/x-date-pickers';
import { AdapterMoment } from '@mui/x-date-pickers/AdapterMoment';
import { BaseDatePickerProps } from '@mui/x-date-pickers/DatePicker/shared';
import { BaseTimeValidationProps } from '@mui/x-date-pickers/internals';
import clsx from 'clsx';
import { Moment } from 'moment';
import { memo, useMemo } from 'react';
import PickerIcon from '../../assets/images/icon/date-picker-icon.png';
import './styles.scss';

/**
 * DatePickerProps
 */
export interface DatePickerProps extends BaseTimeValidationProps, BaseDatePickerProps<Moment> {
    /**
     * DatePicker's variant
     * @default 'desktop'
     */
    variant?: 'desktop' | 'mobile' | 'responsive';
    /**
     * inputFormat
     * @default 'YYYY/MM/DD'
     */
    inputFormat?: string;
    /**
     * inputProps
     */
    inputProps?: Omit<TextFieldProps, 'disabled'>;
    /**
     * readOnly
     * @default false
     */
    readOnly?: boolean;
    /**
     * disabled
     * @default false
     */
    disabled?: boolean;
    /**
     * error
     */
    error?: boolean | string;
    /**
     * iconCalendar
     */
    iconCalendar?: () => React.JSX.Element;
}

/**
 * DatePickerIcon
 * @returns React.JSX.Element
 */
export const DatePickerIcon = (): React.JSX.Element => {
    return <img src={PickerIcon} width={17} height={18} />;
};

/**
 * DatePicker
 */
const DatePicker = (props: DatePickerProps): React.JSX.Element => {
    const {
        variant = 'desktop',
        inputFormat = 'YYYY/MM/DD',
        inputProps,
        readOnly,
        disabled,
        error,
        iconCalendar,
        ...rest
    } = useMemo(() => props, [props]);

    // DatePickerComponent
    const DatePickerComponent = useMemo(() => {
        switch (variant) {
            case 'mobile':
                return MobileDatePicker;
            case 'desktop':
                return DesktopDatePicker;
            case 'responsive':
            default:
                return DatePickerBase;
        }
    }, [variant]);

    return useMemo(
        () => (
            <LocalizationProvider
                dateAdapter={AdapterMoment}
                adapterLocale="ja-JP"
                dateFormats={{ year: 'YYYY年', month: 'MM月', monthShort: 'M月', monthAndYear: 'M月 YYYY' }}
            >
                <DatePickerComponent
                    {...rest}
                    disabled={disabled}
                    format={inputFormat}
                    className={clsx('common-date-picker-component', {
                        error,
                    })}
                    slots={{
                        openPickerIcon: iconCalendar ? iconCalendar : DatePickerIcon,
                    }}
                    slotProps={{
                        textField: {
                            ...inputProps,
                            disabled: readOnly || disabled,
                            className: clsx({
                                'read-only': readOnly,
                            }),
                        },
                    }}
                />
                <div className="d-flex align-items-center common-date-picker-error-container">
                    {error && <div className="error">{error}</div>}
                </div>
            </LocalizationProvider>
        ),
        [DatePickerComponent, disabled, error, inputFormat, inputProps, readOnly, rest, iconCalendar]
    );
};

export default memo(DatePicker);
