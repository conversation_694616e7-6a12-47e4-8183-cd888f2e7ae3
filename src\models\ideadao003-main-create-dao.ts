import { CommonCallback } from './common';

/**
 * RevenueSharingRatio
 */
export interface RevenueSharingRatio {
    // 収益分配割合（発案者・応援者）
    InvatorAndCheers: number;
    // 収益分配割合（リーダー）
    Leaders: number;
    // 収益分配割合（メンバー・投資家（タスク））
    MembersAndInvestors: number;
}

/**
 * IdeaDao003MainCreateDaoModel
 */
export interface IdeaDao003MainCreateDaoModel extends CommonCallback {
    // DAO名
    DaoName: string;
    // DAO画像ファイル名
    DaoImageFileName: string;
    // 解決したい課題・叶えたい未来
    OurMission: string;
    // 概要
    OverView: string;
    // 求めるスキル
    RequiredSkill: string;
    // 収益分配割合
    RevenueSharingRatio: RevenueSharingRatio;
}

/**
 * IdeaDao003MainCreateDaoResult
 */
export interface IdeaDao003MainCreateDaoResult {
    // DaoID
    DaoID: string;
}
