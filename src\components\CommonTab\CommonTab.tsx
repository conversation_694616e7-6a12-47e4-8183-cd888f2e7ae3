import React, { memo, useCallback, useMemo } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import Button from '../Button/Button';

/**
 * TabBarProps
 */
export interface TabBarProps {
    focused?: boolean;
    onClick?: () => void;
}

/**
 * TabItemProps
 */
interface TabItemProps {
    /**
     * label
     */
    label: string;
    /**
     * router
     */
    router: string;
    /**
     * component
     */
    component: React.ReactNode;
    /**
     * renderTab
     */
    renderTab?: React.ElementType;
    /**
     * currentRouter
     */
    readonly currentRouter?: string;
}

/**
 * TabItem
 * @param props TabItemProps
 */
export const TabItem = (props: TabItemProps): React.JSX.Element => {
    const { component, router, currentRouter } = useMemo(() => props, [props]);

    return useMemo(() => {
        return <div hidden={router !== currentRouter}>{component}</div>;
    }, [component, currentRouter, router]);
};

/**
 * CommonTabProps
 */
export interface CommonTabProps {
    /**
     * tabBarOptions
     */
    tabBarOptions?: React.CSSProperties;
    /**
     * children
     */
    children: React.ReactElement<TabItemProps>[];
    /**
     * navigationType
     */
    navigationType?: 'PUSH' | 'REPLACE';
    /**
     * initialRouter
     */
    initialRouter?: string;
}

// OmittedTabItemProps
type OmittedTabItemProps = Omit<TabItemProps, 'component' | 'currentRouter'>;

/**
 * CommonTab
 * @returns React.JSX.Element
 */
const CommonTab = (props: CommonTabProps): React.JSX.Element => {
    // hooks
    const location = useLocation();
    const navigate = useNavigate();

    // get props
    const { children, tabBarOptions, navigationType = 'REPLACE', initialRouter } = useMemo(() => props, [props]);

    // get routers
    const routers = useMemo(() => children.map((child) => child.props.router), [children]);

    // get current router
    const currentRouter = useMemo(
        () => (routers.includes(location?.pathname) ? location.pathname : initialRouter || routers[0]),
        [initialRouter, location?.pathname, routers]
    );

    // get tabBar props
    const tabBar: OmittedTabItemProps[] = useMemo(() => {
        return children.map(({ props }) => props);
    }, [children]);

    /**
     * onChangeTab
     * @param router string
     */
    const onChangeTab = useCallback(
        (router: string) => () => {
            if (location?.pathname !== router) {
                switch (navigationType) {
                    case 'PUSH': {
                        navigate(router);
                        break;
                    }
                    case 'REPLACE':
                    default: {
                        navigate(router, { replace: true });
                        break;
                    }
                }
            }
        },
        [location?.pathname, navigate, navigationType]
    );

    /**
     * renderTabItem
     * @param item OmittedTabItemProps
     * @param index number
     */
    const renderTabItem = useCallback(
        (item: OmittedTabItemProps, index: number) => {
            const { router, label, renderTab: TabBar } = item;
            return TabBar ? (
                <TabBar key={index} focused={currentRouter === router} onClick={onChangeTab(router)} />
            ) : (
                <Button key={index} onClick={onChangeTab(router)}>
                    {label}
                </Button>
            );
        },
        [currentRouter, onChangeTab]
    );

    return useMemo(
        () => (
            <React.Fragment>
                <div
                    className="d-flex flex-row"
                    style={{
                        ...tabBarOptions,
                    }}
                >
                    {tabBar.map(renderTabItem)}
                </div>

                {React.Children.map(children, (child) =>
                    React.cloneElement(child, {
                        currentRouter,
                    })
                )}
            </React.Fragment>
        ),
        [tabBarOptions, tabBar, renderTabItem, children, currentRouter]
    );
};

export default memo(CommonTab);
