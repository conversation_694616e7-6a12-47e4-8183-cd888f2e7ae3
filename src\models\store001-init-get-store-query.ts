import { CommonCallback } from './common';

/**
 * Store001InitGetStoreQueryModel
 */
export interface Store001InitGetStoreQueryModel extends CommonCallback {
    BrandID: string;
}

/**
 * MedalGroupsType
 */
export interface MedalGroupsType {
    MedalServiceID?: string;
    MedalServiceName: string;
}

/**
 * AreaType
 */
export interface AreaItem {
    AreaID?: string;
    AreaName: string;
}

/**
 * MedalServiceListType
 */
export interface CategoriesType {
    StoreCategoryID: string;
    StoreCategoryName: string;
}

/**
 * Store001InitGetStoreQueryResult
 */
export interface Store001InitGetStoreQueryResult {
    MedalGroups: MedalGroupsType[];
    Categories: CategoriesType[];
    Areas: AreaItem[];
}
