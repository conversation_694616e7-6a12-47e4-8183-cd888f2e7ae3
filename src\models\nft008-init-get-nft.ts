import { CommonCallback } from './common';

/**
 * Nft008InitGetNft
 */
export type Nft008InitGetNftModel = CommonCallback & {
    // 'NFTコンテンツID
    NFTContentID: string;
    // 加盟店ID
    StoreID: string;
    // 緯度
    CurrentLatitude?: number;
    // 経度
    CurrentLongitude?: number;
};

/**
 * Nft008InitGetNftResult
 */
export interface Nft008InitGetNftResult {
    // コンテンツ名
    ContentName: string;
    // コンテンツ画像
    ContentImage: string;
    // コンテンツ音声
    ContentSound?: string;
    // コンテンツ動画
    ContentMovie?: string;
    // コンテンツ画像サンプル
    ContentImageSample: string;
    // コンテンツ音声サンプル
    ContentSoundSample?: string;
    // コンテンツ動画サンプル
    ContentMovieSample?: string;
    // NFT販売店舗名
    StoreName: string;
    // NFT販売店舗住所の都道府県
    StorePrefectures: string;
    // NFT販売店舗住所の市区町村
    StoreMunicipalities: string;
    // NFT販売店舗住所の町域
    StoreTownArea?: string;
    // NFT販売店舗住所の番地
    StoreHouseNumber: string;
    // NFT販売店舗住所の建物名
    StoreBuildingName?: string;
    // 販売開始日
    StartDate: string;
    // 販売終了日
    EndDate: string;
    // NFTコンテンツ価格
    ContentPrice: number;
    // シリアル番号
    SerialNumber: number[];
    // 総数
    Total: number;
    // 在庫数
    Stock: number;
    // Maximum
    Maximum: number;
}
