import { CommonCallback } from './common';

/**
 * ChargeIntBnkPay001InitGetBankInfoModel
 */
export type ChargeIntBnkPay001InitGetBankInfoModel = CommonCallback;

/**
 * ChargeIntBnkPay001InitGetBankInfoResult
 */
export interface ChargeIntBnkPay001InitGetBankInfoResult {
    // BankPay登録フラグ(0:未登録, 1: 登録済）
    BankPayRegisterFlag: boolean;
    // BankInfo
    BankList: BankListModel[];
}

/**
 * BankListModel
 */
export interface BankListModel {
    // 有効区分（0:無効, 1:有効）
    IsValid: boolean;
    // 銀行コード
    BankCode: string;
    // 支店コード
    BranchCode: string;
    // 預金種別（1:普通口座, 2:口座口座）
    DepositType: string;
    // 口座番号（マスクした値）
    MaskedAccountNum: string;
}
