import React, { useCallback, useEffect, useMemo, useState } from 'react';
import Icons from '../Icons';

/**
 * ScrollToTopProps
 */
export interface ScrollToTopProps {
    icon?: React.ReactNode;
}

/**
 * ScrollToTop
 * @param props ScrollToTopProps
 * @returns React.JSX.Element
 */
const ScrollToTop = (props: ScrollToTopProps): React.JSX.Element => {
    const [showBackToTop, setShowBackToTop] = useState<boolean>(false);
    const { icon } = useMemo(() => props, [props]);

    /**
     * Handle click back to top button
     */
    const handleBackToTop = useCallback(() => {
        window.scrollTo({ top: 0, behavior: 'smooth' });
    }, []);

    /**
     * runOnScroll function
     */
    const runOnScroll = useCallback(() => {
        const top = document.body.getBoundingClientRect().top;
        setShowBackToTop(!(top === 0));
    }, []);

    useEffect(() => {
        window.addEventListener('scroll', runOnScroll, { passive: true });
        return () => {
            window.removeEventListener('scroll', runOnScroll);
        };
    }, [runOnScroll]);

    return useMemo(
        () => (
            <div className="scroll-top-box">
                {showBackToTop && <span onClick={handleBackToTop}>{icon ? icon : <Icons.IconArrowCircle />}</span>}
            </div>
        ),
        [handleBackToTop, icon, showBackToTop]
    );
};

export default ScrollToTop;
