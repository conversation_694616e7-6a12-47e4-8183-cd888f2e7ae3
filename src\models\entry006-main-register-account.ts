import { CommonCallback } from './common';

/**
 * Entry006MainRegisterAccountModel
 */
export interface Entry006MainRegisterAccountModel extends CommonCallback {
    // ブランドID
    BrandID: string;
    // ユーザーID
    UserID: string;
    // 電話番号
    TelephoneNumber: string;
    // メールアドレス
    MailAddress: string;
    // パスワード
    Password: string;
    // 性別
    Gender: string;
    // 年齢
    Age: string;
    // 居住地
    Residence: string;
    // ニックネーム
    NickName: string;
    // 性
    LastName: string;
    // 名
    FirstName: string;
    // 生年月日
    DateOfBirth: string;
    // 郵便番号
    PostCode: string;
    // 都道府県
    Prefectures: string;
    // 市区町村
    Municipalities: string;
    // 番地
    HouseNumber: string;
    // 建物名
    BuildingName?: string;
    // メールマガジン
    MailMagazine: boolean;
    // 必須項目リスト
    RequiredFieldList: Array<keyof Omit<Entry006MainRegisterAccountModel, 'RequiredFieldList' | 'BuildingName'>>;
    // LINEユーザーID
    LineUserID?: string;
}
