import { Paper } from '@mui/material';
import { shallow } from 'enzyme';
import React from 'react';
import CommonAPI from '../../apis/common';
import AuthUtils from '../../utils/auth';
import Header, { FullSizeLogo, HalfSizeLogo, HeaderProps, LogoProps } from '../Header/Header';
import Icons from '../Icons/Icons';
import LanguageDropdown from '../LanguageDropdown';

// mock useNavigate function
const mockedUseNavigate = jest.fn();

/**
 * mock for react-router-dom lib
 */
jest.mock('react-router-dom', () => ({
    ...jest.requireActual('react-router-dom'),
    useSearchParams: () => null,
    useLoaderData: () => null,
    useNavigate: () => mockedUseNavigate,
    useLocation: () => ({
        pathname: '/',
    }),
}));

/**
 * Unit test for Header component
 */
describe('Unit test for Header component', () => {
    beforeEach(() => {
        AuthUtils.storeAuthToken({ idToken: undefined });
    });

    /**
     * setup element
     */
    const setup = (p?: HeaderProps): React.JSX.Element => {
        return <Header {...p} />;
    };

    /**
     * should render correctly without props
     */
    it('should render correctly without props', () => {
        const wrapper = shallow(setup());

        expect(wrapper.find(Paper)).toBeTruthy();
    });

    it('should call api commonHeader001InitGetNewsUnreadFlag success', () => {
        AuthUtils.storeAuthToken({ idToken: 'idToken' });
        const useEffect = jest.spyOn(React, 'useEffect').mockImplementationOnce((f) => f());
        AuthUtils.retrieveToken('idToken');
        jest.spyOn(CommonAPI, 'commonHeader001InitGetNewsUnreadFlag').mockResolvedValueOnce({
            status: 0,
            isSuccess: true,
            result: { UnreadFlag: true },
        });

        const wrapper = shallow(setup());

        expect(useEffect).toHaveBeenCalled();
        expect(wrapper.find(Paper)).toBeTruthy();
    });

    it('should call api commonHeader001InitGetNewsUnreadFlag fail', () => {
        AuthUtils.storeAuthToken({ idToken: 'idToken' });
        const useEffect = jest.spyOn(React, 'useEffect').mockImplementationOnce((f) => f());
        AuthUtils.retrieveToken('idToken');
        jest.spyOn(CommonAPI, 'commonHeader001InitGetNewsUnreadFlag').mockResolvedValueOnce({
            status: 1,
            isSuccess: false,
            Message: 'api.common.unknown_error',
        });

        const wrapper = shallow(setup());

        expect(useEffect).toHaveBeenCalled();
        expect(wrapper.find(Paper)).toBeTruthy();
    });

    /**
     * should display FullSizeLogo when type is main
     */
    it('should display FullSizeLogo when headerType is main', () => {
        jest.spyOn(React, 'useState').mockReturnValueOnce([true, jest.fn()]);
        const wrapper = shallow(
            setup({
                type: 'main',
            })
        );

        expect(wrapper.find(FullSizeLogo)).toBeTruthy();
    });

    // it('should display logo on the left side and title in center when type is logoLeftWithTitle', () => {
    //     const wrapper = shallow(
    //         setup({
    //             type: 'logoLeftWithTitle',
    //             title: 'This is mock title',
    //         })
    //     );

    //     expect(wrapper.find(HalfSizeLogo)).toBeTruthy();
    //     expect(wrapper.find('div.title').text()).toBe('This is mock title');
    // });

    // it('should display back icon on the left and logo in center when type is backLeftWithMainLogo', () => {
    //     const wrapper = shallow(
    //         setup({
    //             type: 'backLeftWithMainLogo',
    //         })
    //     );

    //     expect(wrapper.find(Icons.BackIcon)).toBeTruthy();
    //     expect(wrapper.find(FullSizeLogo)).toBeTruthy();
    // });

    // it('should display back icon on the left and logo in center when type is backLeftWithMainLogo', () => {
    //     const wrapper = shallow(
    //         setup({
    //             type: 'backLeftWithMainLogo',
    //         })
    //     );

    //     expect(wrapper.find(Icons.BackIcon)).toBeTruthy();
    //     expect(wrapper.find(FullSizeLogo)).toBeTruthy();
    // });

    it('should display back icon on the left and title in center when type is backLeftWithTitle', () => {
        jest.spyOn(React, 'useState').mockReturnValueOnce([true, jest.fn()]);
        const wrapper = shallow(
            setup({
                type: 'backLeftWithTitle',
                title: 'This is mock title',
            })
        );

        expect(wrapper.find(Icons.BackIcon)).toBeTruthy();
        expect(wrapper.find('div.title').text()).toBe('This is mock title');
    });

    it('should display close icon on the right side and title in center when type is closeRightWithTitle', () => {
        const wrapper = shallow(
            setup({
                type: 'closeRightWithTitle',
                title: 'This is mock title',
            })
        );

        expect(wrapper.find(Icons.CloseIcon)).toBeTruthy();
        expect(wrapper.find('div.title').text()).toBe('This is mock title');
    });

    it('should display only title in center when type is withTitle', () => {
        jest.spyOn(React, 'useState').mockReturnValueOnce([true, jest.fn()]);
        const wrapper = shallow(
            setup({
                type: 'withTitle',
                title: 'This is mock title',
            })
        );

        expect(wrapper.find('div.title').text()).toBe('This is mock title');
    });

    it('should display FullSizeLogo on the left and MyPage section on the right', () => {
        const wrapper = shallow(
            setup({
                type: 'withMyPage',
            })
        );
        expect(wrapper.find(FullSizeLogo)).toBeTruthy();
        expect(wrapper.find(Icons.LanguageIcon)).toBeTruthy();
        expect(wrapper.find('div .my-page-container .my-page-btn')).toBeTruthy();
        expect(wrapper.find(Icons.MyPage)).toBeTruthy();
    });

    it('should display back icon on the left and menu on the right when type is backLeftWithTitleNotiMenu', () => {
        const wrapper = shallow(
            setup({
                type: 'backLeftWithTitleNotiMenu',
                title: 'This is mock title',
            })
        );
        expect(wrapper.find(Icons.BackIcon)).toBeTruthy();
        expect(wrapper.find('div.title').text()).toBe('This is mock title');
    });

    it('should display back icon on the left and menu on the right when type is backLeftWithTitleNotiMenu with unreadIcon', () => {
        jest.spyOn(CommonAPI, 'commonHeader001InitGetNewsUnreadFlag').mockResolvedValueOnce({
            status: 0,
            isSuccess: true,
            result: { UnreadFlag: true },
        });
        jest.spyOn(React, 'useState').mockReturnValueOnce([true, jest.fn()]);
        const wrapper = shallow(
            setup({
                type: 'backLeftWithTitleNotiMenu',
                title: 'This is mock title',
            })
        );
        expect(wrapper.find(Icons.BackIcon)).toBeTruthy();
        expect(wrapper.find('div.title').text()).toBe('This is mock title');
    });

    it('should display back icon on the left and menu on the right when type is backLeftWithTitleMenu', () => {
        const wrapper = shallow(
            setup({
                type: 'backLeftWithTitleMenu',
                title: 'This is mock title',
            })
        );
        expect(wrapper.find(Icons.BackIcon)).toBeTruthy();
        expect(wrapper.find('div.title').text()).toBe('This is mock title');
    });

    it('should display home icon on the left and menu on the right when type is mainWithQrCode', () => {
        const wrapper = shallow(
            setup({
                type: 'mainWithQrCode',
            })
        );
        expect(wrapper.find(Icons.BackIcon)).toBeTruthy();
        expect(wrapper.find(FullSizeLogo).length).toBe(1);
    });

    it('should display home icon on the left and menu on the right when type is mainWithQrCode with unreadIcon', () => {
        jest.spyOn(React, 'useState').mockReturnValueOnce([true, jest.fn()]);
        const wrapper = shallow(
            setup({
                type: 'mainWithQrCode',
            })
        );
        expect(wrapper.find(Icons.BackIcon)).toBeTruthy();
        expect(wrapper.find(FullSizeLogo).length).toBe(1);
    });

    it('should display FullSizeLogon at center when type is withFullSizeLogoOnly', () => {
        jest.spyOn(React, 'useState').mockReturnValueOnce([true, jest.fn()]);
        const wrapper = shallow(
            setup({
                type: 'withFullSizeLogoOnly',
            })
        );
        expect(wrapper.find(FullSizeLogo).length).toBe(1);
    });

    it('should display home icon on the left and FullSizeLogo at center and translate section on the right when type is mainTranslate', () => {
        jest.spyOn(React, 'useState').mockReturnValueOnce([true, jest.fn()]);
        const wrapper = shallow(
            setup({
                type: 'mainTranslate',
            })
        );
        const translateContainer = wrapper.find('div.translate-container div').at(2);

        expect(wrapper.find('div.translate-container')).toBeTruthy();
        expect(wrapper.find(Icons.HomeIcon)).toBeTruthy();
        expect(wrapper.find(FullSizeLogo)).toBeTruthy();

        expect(translateContainer).toBeTruthy();
        expect(translateContainer.find('div').at(0).find(Icons.LanguageIcon)).toBeTruthy();
        expect(translateContainer.find('div').at(0).find(LanguageDropdown)).toBeTruthy();

        expect(translateContainer.find('div').at(1).find(Icons.NotificationIcon)).toBeTruthy();
        expect(translateContainer.find('div').at(1).find(Icons.UnreadNotificationIcon)).toBeTruthy();

        expect(translateContainer.find('div').at(2).find(Icons.MenuIcon)).toBeTruthy();
    });

    it('should display home icon on the left and title at center and translate section on the right when type is mainTranslate', () => {
        jest.spyOn(React, 'useState').mockReturnValueOnce([true, jest.fn()]);
        const wrapper = shallow(
            setup({
                type: 'withTitleAndTranslate',
                title: 'This is mock title',
            })
        );

        const translateContainer = wrapper.find('div.translate-container div').at(2);

        expect(wrapper.find(Icons.BackIcon)).toBeTruthy();
        expect(wrapper.find('div.title').text()).toBe('This is mock title');
        expect(translateContainer).toBeTruthy();
        expect(translateContainer.find('div').at(0).find(Icons.LanguageIcon)).toBeTruthy();
        expect(translateContainer.find('div').at(0).find(LanguageDropdown)).toBeTruthy();

        expect(translateContainer.find('div').at(1).find(Icons.NotificationIcon)).toBeTruthy();
        expect(translateContainer.find('div').at(1).find(Icons.UnreadNotificationIcon)).toBeTruthy();

        expect(translateContainer.find('div').at(2).find(Icons.MenuIcon)).toBeTruthy();
    });

    it('should display home icon on the left and FullSizeLogo at center and translate section on the right when type is withTitleAndTranslate', () => {
        jest.spyOn(React, 'useState').mockReturnValueOnce([true, jest.fn()]);
        const wrapper = shallow(
            setup({
                type: 'withTitleAndTranslate',
                title: 'This is mock title',
            })
        );

        const translateContainer = wrapper.find('div.translate-container div').at(2);

        expect(wrapper.find(Icons.BackIcon)).toBeTruthy();
        expect(wrapper.find('div.title').text()).toBe('This is mock title');
        expect(translateContainer).toBeTruthy();
        expect(translateContainer.find('div').at(0).find(Icons.LanguageIcon)).toBeTruthy();
        expect(translateContainer.find('div').at(0).find(LanguageDropdown)).toBeTruthy();

        expect(translateContainer.find('div').at(1).find(Icons.NotificationIcon)).toBeTruthy();
        expect(translateContainer.find('div').at(1).find(Icons.UnreadNotificationIcon)).toBeTruthy();

        expect(translateContainer.find('div').at(2).find(Icons.MenuIcon)).toBeTruthy();
    });

    it('should display when type is withTitleOnly', () => {
        jest.spyOn(React, 'useState').mockReturnValueOnce([true, jest.fn()]);
        const wrapper = shallow(
            setup({
                type: 'withTitleOnly',
                title: 'This is mock title',
            })
        );

        expect(wrapper.find('div.title').text()).toBe('This is mock title');
    });

    it('should call onLeftButtonClick when click on left icon', () => {
        const onLeftButtonClick = jest.fn();
        const wrapper = shallow(
            setup({
                type: 'backLeftWithTitle',
                title: 'This is mock title',
                actionKey: 'actionKey',
                onLeftButtonClick,
            })
        );

        const backIcon = wrapper.find('div.back-icon');

        backIcon.at(0).simulate('click');

        expect(onLeftButtonClick).toHaveBeenCalled();
    });

    it('should call onRightButtonClick when click on right icon', () => {
        const onRightButtonClick = jest.fn();
        const wrapper = shallow(
            setup({
                type: 'closeRightWithTitle',
                title: 'This is mock title',
                actionKey: 'actionKey',
                onRightButtonClick,
            })
        );

        const backIcon = wrapper.find('div.close-icon');

        backIcon.at(1).simulate('click');

        expect(onRightButtonClick).toHaveBeenCalled();
    });

    it('should click withMyPage when click on MyPage icon with idToken', () => {
        AuthUtils.storeAuthToken({ idToken: 'idToken' });
        AuthUtils.retrieveToken('idToken');
        const wrapper = shallow(
            setup({
                type: 'withMyPage',
            })
        );

        const btn = wrapper.find('div.my-page-btn');
        btn.simulate('click');
        expect(mockedUseNavigate).toHaveBeenLastCalledWith('/wallet');
    });
    
    it('should trigger withMyPage when click on MyPage icon without idToken', () => {
        AuthUtils.storeAuthToken({ idToken: undefined });
        AuthUtils.retrieveToken('idToken');
        const wrapper = shallow(
            setup({
                type: 'withMyPage',
            })
        );

        const btn = wrapper.find('div.my-page-btn');

        expect(btn.simulate('click'));
    });

    it('should trigger handleClickMenuButton when click on MenuIcon icon', () => {
        const wrapper = shallow(
            setup({
                type: 'main',
            })
        );

        const btn = wrapper.find('div.menu-button');

        expect(btn.simulate('click'));
    });

    it('should trigger handleClickInfoButton when click on NotificationIcon icon', () => {
        const wrapper = shallow(
            setup({
                type: 'main',
            })
        );

        const btn = wrapper.find('div.info-button');

        expect(btn.simulate('click'));
    });

    it('should trigger handleClickTopIcon when click on HomeIcon icon', () => {
        const wrapper = shallow(
            setup({
                type: 'main',
            })
        );

        const btn = wrapper.find('div.back-left-with-title-center-and-menu-right__left div').at(0);

        expect(btn.simulate('click'));
    });

    it('should display home icon on the left and menu on the right when type is mainWithQrCode', () => {
        const wrapper = shallow(
            setup({
                type: 'mainWithQrCode',
            })
        );

        const btn = wrapper.find('div.qr-code-container').at(0)

        expect(btn.simulate('click'));
    });
});

/**
 * Unit test for FullSizeLogo
 */
describe('Unit test for FullSizeLogo', () => {
    /**
     * setup element
     */
    const setup = (p?: LogoProps): React.JSX.Element => {
        return <FullSizeLogo {...p} />;
    };

    /**
     * should display correctly
     */
    it('should display correctly', () => {
        const wrapper = shallow(setup());

        expect(wrapper).toBeTruthy();
    });

    /**
     * should call onLogoClick when click on Logo
     */
    it('should call onLogoClick when click on Logo', () => {
        const onLogoClick = jest.fn();

        const wrapper = shallow(
            setup({
                onLogoClick,
            })
        );

        const logo = wrapper.find('div.logo-full');

        logo.simulate('click');

        expect(onLogoClick).toHaveBeenCalled();
    });
});

/**
 * Unit test for HalfSizeLogo
 */
describe('Unit test for HalfSizeLogo', () => {
    /**
     * setup element
     */
    const setup = (p?: LogoProps): React.JSX.Element => {
        return <HalfSizeLogo {...p} />;
    };

    /**
     * should display correctly
     */
    it('should display correctly', () => {
        const wrapper = shallow(setup());

        expect(wrapper).toBeTruthy();
    });

    /**
     * should call onLogoClick when click on Logo
     */
    it('should call onLogoClick when click on Logo', () => {
        const onLogoClick = jest.fn();

        const wrapper = shallow(
            setup({
                onLogoClick,
            })
        );

        const logo = wrapper.find('div.logo-half');

        logo.simulate('click');

        expect(onLogoClick).toHaveBeenCalled();
    });
});
