import API from '../constants/api';
import { BaseResponse } from '../models/common';
import { Entry002InitGetBrandInfoModel, Entry002InitGetBrandInfoResult } from '../models/entry002-init-get-brandinfo';
import { Entry003MainCheckAuthCodeModel } from '../models/entry003-main-check-authcode';
import { Entry006MainGetTokenModel, Entry006MainGetTokenResult } from '../models/entry006-main-get-token';
import { Entry006MainRegisterAccountModel } from '../models/entry006-main-register-account';
import { Entry007MainGetTokenModel, Entry007MainGetTokenResult } from '../models/entry007-main-get-token';
import { Entry007MainRegisterAccountModel } from '../models/entry007-main-register-account';
import { Entry002MainSendAuthCodeModel, Entry003MainSendAuthCodeModel } from '../models/entry00x-main-send-authcode';
import createAPI from './baseApi';

/**
 * EntryAPI
 */
class EntryAPI {
    /**
     * entry002InitGetBrandInfo
     * @param data Entry002InitGetBrandInfoModel
     * @returns Promise<BaseResponse<Entry002InitGetBrandInfoResult>>
     */
    static entry002InitGetBrandInfo = (
        data: Entry002InitGetBrandInfoModel
    ): Promise<BaseResponse<Entry002InitGetBrandInfoResult>> => {
        return createAPI({
            url: API.ENTRY002_INIT_GET_BRANDINFO,
            data,
        });
    };

    /**
     * entry002MainSendAuthCode
     * @param data Entry002MainSendAuthCodeModel
     * @returns Promise<BaseResponse>
     */
    static entry002MainSendAuthCode = (data: Entry002MainSendAuthCodeModel): Promise<BaseResponse> => {
        return createAPI({
            url: API.ENTRY002_MAIN_SEND_AUTHCODE,
            data,
        });
    };

    /**
     * entry003MainSendAuthCode
     * @param data Entry003MainSendAuthCodeModel
     * @returns Promise<BaseResponse>
     */
    static entry003MainSendAuthCode = (data: Entry003MainSendAuthCodeModel): Promise<BaseResponse> => {
        return createAPI({
            url: API.ENTRY003_MAIN_SEND_AUTHCODE,
            data,
        });
    };

    /**
     * entry003MainCheckAuthCode
     * @param data Entry003MainCheckAuthCodeModel
     * @returns Promise<BaseResponse>
     */
    static entry003MainCheckAuthCode = (data: Entry003MainCheckAuthCodeModel): Promise<BaseResponse> => {
        return createAPI({
            url: API.ENTRY003_MAIN_CHECK_AUTHCODE,
            data,
        });
    };

    /**
     * entry006MainGetToken
     * @param data Entry006MainGetTokenModel
     * @returns Promise<BaseResponse<Entry006MainGetTokenResult>>
     */
    static entry006MainGetToken = (
        data: Entry006MainGetTokenModel
    ): Promise<BaseResponse<Entry006MainGetTokenResult>> => {
        return createAPI({
            url: API.ENTRY006_MAIN_GET_TOKEN,
            data,
        });
    };

    /**
     * entry006MainRegisterAccount
     * @param data Entry006MainRegisterAccountModel
     * @returns Promise<BaseResponse>
     */
    static entry006MainRegisterAccount = (data: Entry006MainRegisterAccountModel): Promise<BaseResponse> => {
        return createAPI({
            url: API.ENTRY006_MAIN_REGISTER_ACCOUNT,
            data,
        });
    };

    /**
     * entry007MainGetToken
     * @param data Entry007MainGetTokenModel
     * @returns Promise<BaseResponse<Entry007MainGetTokenResult>>
     */
    static entry007MainGetToken = (
        data: Entry007MainGetTokenModel
    ): Promise<BaseResponse<Entry007MainGetTokenResult>> => {
        return createAPI({
            url: API.ENTRY007_MAIN_GET_TOKEN,
            data,
        });
    };

    /**
     * entry007MainRegisterAccount
     * @param data Entry007MainRegisterAccountModel
     * @returns Promise<BaseResponse>
     */
    static entry007MainRegisterAccount = (data: Entry007MainRegisterAccountModel): Promise<BaseResponse> => {
        return createAPI({
            url: API.ENTRY007_MAIN_REGISTER_ACCOUNT,
            data,
        });
    };
}

export default EntryAPI;
