import { CommonCallback } from './common';

export interface AreaWalletPaymentConfigGetModel extends CommonCallback {
    CheerID?: number;
}

/**
 * AreaWalletPaymentConfigGetResult
 */
export interface AreaWalletPaymentConfigGetResult {
    CardUsedFlag?: boolean;
    CvsUsedFlag?: boolean;
    BankUsedFlag?: boolean;
    PrepaidUsedFlag?: boolean;
    CardIcons?: string[];
    CvsIcons?: string[];
    BankIcons?: string[];
    BankPayIcons?: string[];
}
