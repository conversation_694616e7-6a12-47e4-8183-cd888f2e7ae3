import { CommonCallback } from './common';

/**
 * Survey003MedalDistribution
 */
export interface Survey003MedalDistribution {
    // メダルサービスID
    MedalserviceID: string;
    // 選択回答ID
    MedalAmount: number;
}

/**
 * Survey003AnswerItem
 */
export interface Survey003AnswerItem {
    // 質問ID
    QuestionID: string;
    // 選択回答ID
    ChoiceID: string[];
    // 自由記述回答内容
    FreeInputText: string;
    // メダル配布内容
    MedalDistributionList: Survey003MedalDistribution[];
    // メダル配布フラグ
    MedalDistributionFlag: boolean;
}

/**
 * Survey003MainSendAnswerModel
 */
export interface Survey003MainSendAnswerModel extends CommonCallback {
    // アンケートID
    SurveyID: string;
    // 回答結果
    AnswerList: Survey003AnswerItem[];
}
