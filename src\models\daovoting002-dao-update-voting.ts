import { CommonCallback } from './common';

/**
 * DAOVoting002CandidateItemModel
 */
export interface DAOVoting002CandidateItemModel {
    // 投票候補番号
    CandidateNo: number;
    // 投票候補内容
    CandidateContent: string;
    // 選択フラグ
    IsSelect: boolean;
}

/**
 * DAOVoting002DaoUpdateVotingModel
 */
export interface DAOVoting002DaoUpdateVotingModel extends CommonCallback {
    // DAOID
    DaoID: string;
    // 投票テーマID
    ThemeID: string;
    // 投票候補リスト
    VotingCandidateList: DAOVoting002CandidateItemModel[];
}
