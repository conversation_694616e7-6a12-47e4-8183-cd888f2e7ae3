import { CommonCallback } from './common';

/**
 * Home001InitGetNewsUnreadLatestModel
 */
export type Home001InitGetNewsUnreadLatestModel = CommonCallback;

/**
 * Home001InitGetNewsUnreadLatestItem
 */
export interface Home001InitGetNewsUnreadLatestItem {
    // お知らせID
    MessageID: string;
    // タイトル
    Subject: string;
}

/**
 * Home001InitGetNewsUnreadLatestResult
 */
export interface Home001InitGetNewsUnreadLatestResult {
    // お知らせ一覧
    NewsList: Home001InitGetNewsUnreadLatestItem[];
}
