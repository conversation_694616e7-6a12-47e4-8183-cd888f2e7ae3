import { CommonCallback } from './common';

/**
 * Item001InitGetItemListModel
 */
export interface Item001InitGetItemListModel extends CommonCallback {
    PageSize: number;
    ExclusiveStartKey?: string;
    Keyword?: string;
    // 1：新着順、2：古い順
    Rearrange?: number;
    Kind?: string[];
    Status?: string[];
    AreaID?: string;
}

/**
 * UserEventTicketItem
 */
export interface UserEventTicketItem {
    // ブランドID#ユーザーID
    BrandCheerID: string;
    // ユーザーイベントチケットID
    UserEventTicketID: string;
    // イベントチケットID
    EventTicketID: string;
    // イベントチケット詳細ID
    EventTicketDetailID: string;
    // イベントチケット入場時間
    EventTicketEntryTime: string;
    // イベントチケット退場時間
    EventTicketExitTime: string;
    // イベントチケット価格名
    EventTicketPriceName: string;
    // イベントチケット価格
    EventTicketPrice: number;
    // イベントチケット利用済フラグ
    EventTicketUsedFlag: boolean;
    // イベントチケットトップ画像
    EventTicketTopImage: string;
    // イベントチケット名
    EventTicketName: string;
    // イベントチケット開催日
    EventTicketDateTime: string;
    // イベント開催日付/利用時間状態
    EventDateTimeStatus: number;
}

/**
 * Item001InitGetItemListResult
 */
export interface Item001InitGetItemListResult {
    UserEventTicketList: UserEventTicketItem[];
    LastEvaluatedKey?: string;
}
