import clsx from 'clsx';
import React, { memo, useCallback, useMemo, useRef } from 'react';
import { Pagination, Swiper as SwiperClass } from 'swiper';
import { Swiper, SwiperSlide } from 'swiper/react';
import { Login001InitGetKeyVisualListItem } from '../../models/login001-init-get-keyvisual-list';
import Frame from '../Frame/Frame';
import Icons from '../Icons/Icons';
import Image from '../Image/Image';
import './styles.scss';
import CommonAPI from '../../apis/common';
import { isBrowser, isTablet } from 'react-device-detect';
import Utils from '../../utils/utils';

/**
 * CarouselProps
 */
export type CarouselProps = {
    data: Login001InitGetKeyVisualListItem[];
};

/**
 * ID: Carousel
 * Name: Carousel common
 * @returns React.JSX.Element
 */
function Carousel(props: CarouselProps): React.JSX.Element {
    const { data } = useMemo(() => props, [props]);

    // refs
    const swiperRef = useRef<SwiperClass>();

    /**
     * handleOnSwiper
     * @param swiper SwiperClass
     */
    const handleOnSwiper = useCallback((swiper: SwiperClass): void => {
        swiperRef.current = swiper;
    }, []);

    /**
     * handleClickSwiperLeftButton
     * slide [キービジュアル] to prev item
     */
    const handleClickSwiperLeftButton = useCallback(() => {
        // slide to prev item
        swiperRef.current?.slidePrev();
    }, []);

    /**
     * handleClickSwiperRightButton
     * slide [キービジュアル] to next item
     */
    const handleClickSwiperRightButton = useCallback(() => {
        // slide to next item
        swiperRef.current?.slideNext();
    }, []);

    /**
     * handleClickImage
     * handle click on image item, should transition to LinkURL if available
     * @param item Login001InitGetKeyVisualListItem
     * @param type '_blank' | '_self'
     */
    const handleClickImage = useCallback(
        (item: Login001InitGetKeyVisualListItem, type: '_blank' | '_self' = '_blank') =>
            () => {
                if (item.LinkURL) {
                    CommonAPI.commonUserTransitionHistory({
                        OriginScreenName: window.location.pathname,
                        ReferrerURL: item.LinkURL,
                        ElementName: Utils.t('common.button.banner_link')?.replaceAll('\n', ''),
                        DeviceID: isBrowser ? 'PC' : isTablet ? 'Tablet' : 'mobile',
                    });
                    window.open(item.LinkURL, type);
                }
            },
        []
    );

    /**
     * renderKeyVisualItem
     * @param item Login001InitGetKeyVisualListItem
     */
    const renderKeyVisualItem = useCallback(
        (item: Login001InitGetKeyVisualListItem) => {
            return (
                <SwiperSlide key={item.KeyVisualID}>
                    <Frame aspectRatio={{ w: 375, h: 250 }}>
                        <Image
                            className="w-100 h-100 image-carousel"
                            src={item.FileName}
                            alt=""
                            onClick={handleClickImage(item)}
                        />
                    </Frame>
                </SwiperSlide>
            );
        },
        [handleClickImage]
    );

    return React.useMemo(
        () => (
            <div id="carousel-common" className="position-relative key-visual-container">
                <Swiper pagination loop modules={[Pagination]} onSwiper={handleOnSwiper}>
                    {data.map(renderKeyVisualItem)}
                </Swiper>
                {data.length > 1 && (
                    <div
                        className={clsx(
                            'd-flex justify-content-center align-items-center',
                            'position-absolute key-visual-control-button left'
                        )}
                        onClick={handleClickSwiperLeftButton}
                    >
                        <Icons.ArrowLeft />
                    </div>
                )}
                {data.length > 1 && (
                    <div
                        className={clsx(
                            'd-flex justify-content-center align-items-center',
                            'position-absolute key-visual-control-button right'
                        )}
                        onClick={handleClickSwiperRightButton}
                    >
                        <Icons.ArrowRight />
                    </div>
                )}
            </div>
        ),
        [handleClickSwiperLeftButton, handleClickSwiperRightButton, handleOnSwiper, data, renderKeyVisualItem]
    );
}

export default memo(Carousel);
