import API from '../constants/api';
import { BaseResponse } from '../models/common';
import {
    Event001InitGetEventTicketListModel,
    Event001InitGetEventTicketListResult,
} from '../models/event001-init-get-event-ticket-list';
import {
    Event001InitGetEventTicketQueryModel,
    Event001InitGetEventTicketQueryResult,
} from '../models/event001-init-get-event-ticket-query';
import {
    Event003InitGetAvailableCoinIdModel,
    Event003InitGetAvailableCoinIdResult,
} from '../models/event003-init-get-availablecoinid';
import {
    Event003InitGetEventTicketDetailModel,
    Event003InitGetEventTicketDetailResult,
} from '../models/event003-init-get-event-ticket-detail';
import { Event005MainWalletCashTransferModel } from '../models/event005-main-wallet-cash-transfer';
import { EventForm002MainSendAnswerModel } from '../models/eventform002-main-send-answer';
import {
    EventForm001InitGetEventFormListModel,
    EventForm001InitGetEventFormListResult,
} from '../models/eventform001-init-get-eventform-list';

import createAPI from './baseApi';
import Environments from '../constants/environments';

/**
 * EventAPI
 */
class EventAPI {
    /**
     * event001InitGetEventTicketList
     * @param data Event001InitGetEventTicketListModel
     * @returns Promise<BaseResponse<Event001InitGetEventTicketListResult>>
     */
    static event001InitGetEventTicketList = (
        data: Event001InitGetEventTicketListModel
    ): Promise<BaseResponse<Event001InitGetEventTicketListResult>> => {
        return createAPI<Event001InitGetEventTicketListResult>({
            url: API.EVENT001_INIT_GET_EVENT_TICKET_LIST,
            data: {
                // set default brandID
                BrandID: Environments.brandID,
                ...data,
            },
        });
    };

    /**
     * event001InitGetEventTicketQuery
     * @param data Event001InitGetEventTicketQueryModel
     * @returns Promise<BaseResponse<Event001InitGetEventTickeQueryResult>>
     */
    static event001InitGetEventTicketQuery = (
        data?: Event001InitGetEventTicketQueryModel
    ): Promise<BaseResponse<Event001InitGetEventTicketQueryResult>> => {
        return createAPI<Event001InitGetEventTicketQueryResult>({
            url: API.EVENT001_INIT_GET_EVENT_TICKET_QUERY,
            data: {
                // set default brandID
                BrandID: Environments.brandID,
                ...data,
            },
        });
    };

    /**
     * event003InitGetEventTicketDetail
     * @param data Event003InitGetEventTicketDetailModel
     * @returns Promise<BaseResponse<Event003InitGetEventTicketDetailResult>>
     */
    static event003InitGetEventTicketDetail = (
        data: Event003InitGetEventTicketDetailModel
    ): Promise<BaseResponse<Event003InitGetEventTicketDetailResult>> => {
        return createAPI<Event003InitGetEventTicketDetailResult>({
            url: API.EVENT003_INIT_GET_EVENT_TICKET_DETAIL,
            data: {
                // set default brandID
                BrandID: Environments.brandID,
                ...data,
            },
        });
    };

    /**
     * event003InitGetAvailableCoinId
     * @param data Event003InitGetAvailableCoinIdModel
     * @returns Promise<BaseResponse<Event003InitGetAvailableCoinIdResult>>
     */
    static event003InitGetAvailableCoinId = (
        data: Event003InitGetAvailableCoinIdModel
    ): Promise<BaseResponse<Event003InitGetAvailableCoinIdResult>> => {
        return createAPI<Event003InitGetAvailableCoinIdResult>({
            url: API.EVENT003_INIT_GET_AVAILABLECOINID,
            data: {
                // set default brandID
                BrandID: Environments.brandID,
                ...data,
            },
        });
    };

    /**
     * event005MainWalletCashTransfer
     * @param data Event005MainWalletCashTransferModel
     * @returns Promise<BaseResponse>
     */
    static event005MainWalletCashTransfer = (data: Event005MainWalletCashTransferModel): Promise<BaseResponse> => {
        return createAPI({
            url: API.EVENT005_MAIN_WALLET_CASH_TRANSFER,
            data,
        });
    };

    /**
     * eventForm002MainSendAnswer
     * @param data EventForm002MainSendAnswerModel
     * @returns Promise<BaseResponse>
     */
    static eventForm002MainSendAnswer = (data: EventForm002MainSendAnswerModel): Promise<BaseResponse> => {
        return createAPI<BaseResponse>({
            url: API.EVENTFORM002_MAIN_SEND_ANSWER,
            data,
        });
    };

    /**
     * eventForm001InitGetEventFormList
     * @param data EventForm001InitGetEventFormListModel
     * @returns Promise<BaseResponse<EventForm001InitGetEventFormListResult>>
     */
    static eventForm001InitGetEventFormList = (
        data: EventForm001InitGetEventFormListModel
    ): Promise<BaseResponse<EventForm001InitGetEventFormListResult>> => {
        return createAPI<EventForm001InitGetEventFormListResult>({
            url: API.EVENTFORM001_INIT_GET_EVENTFORM_LIST,
            data,
        });
    };
}

export default EventAPI;
