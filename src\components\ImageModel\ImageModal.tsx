import { IconButton, Modal } from '@mui/material';
import CloseIcon from '@mui/icons-material/Close';
import './styles.scss';
import Image from '../Image/Image';

type Props = {
    openImage: boolean;
    imgSrc: string;
    purchasedStatus?: number;
    handleCloseImage: () => void;
};

/**
 * ImageModal
 * @param props Props
 * @returns
 */
const ImageModal = ({ openImage, imgSrc, handleCloseImage }: Props): JSX.Element => {
    return (
        <Modal onClose={handleCloseImage} open={openImage} disableAutoFocus disableEnforceFocus disableRestoreFocus>
            <>
                <div className="image-model-container">
                    <Image src={imgSrc} className="image-modal" alt="" />
                </div>
                <IconButton className="button-icon" onClick={handleCloseImage}>
                    <CloseIcon sx={{ color: 'white' }} />
                </IconButton>
            </>
        </Modal>
    );
};

export default ImageModal;
