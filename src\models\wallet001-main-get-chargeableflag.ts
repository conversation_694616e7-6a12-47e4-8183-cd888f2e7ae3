import { CommonCallback } from './common';

/**
 * Wallet001MainGetChargeAbleFlagModel
 */
export interface Wallet001MainGetChargeAbleFlagModel extends CommonCallback {
    MedalServiceID?: string;
}

/**
 * Wallet001MainGetChargeAbleFlagResult
 */
export interface Wallet001MainGetChargeAbleFlagResult {
    // チャージボタンの有効フラグ
    ChargeButtonEnableFlag: boolean;
    // チャージ開始日
    ChargeStartDate: string;
    // チャージ終了日
    ChargeEndDate: string;
}
