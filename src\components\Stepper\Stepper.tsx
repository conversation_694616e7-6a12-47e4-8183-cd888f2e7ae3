import clsx from 'clsx';
import { memo, useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { IconsProps } from '../Icons/Icons';
import './styles.scss';

/**
 * StepperRightArrow
 */
const RightArrow = ({ color = '', bgColor = 'transparent' }: IconsProps): React.JSX.Element => {
    return (
        <svg height="28" width="9">
            <polyline points="0,0 8,14 0,28" fill={bgColor} stroke={color} strokeWidth={1} />
        </svg>
    );
};

/**
 * StepperItemProps
 */
interface StepperItemProps {
    text: string;
    isSelected: boolean;
    isLastItem?: boolean;
    isDisabled?: boolean;
}

/**
 * StepperItem
 */
export const StepperItem = ({ text, isSelected, isLastItem, isDisabled }: StepperItemProps): React.JSX.Element => {
    const ref = useRef<HTMLDivElement>(null);

    useEffect(() => {
        const selected = isSelected;
        setTimeout(() => {
            if (selected) {
                ref?.current?.scrollIntoView({
                    behavior: 'smooth',
                    block: 'nearest',
                    inline: 'start',
                });
            }
        }, 1000);
    }, [isSelected]);

    return useMemo(() => {
        const bgColorNotSelected = isDisabled ? '#cccccc' : '#ffffff';

        return (
            <div className="d-flex flex-row stepper-item" ref={ref}>
                <div className="d-flex flex-row stepper-item-arrow">
                    <div
                        className={clsx('stepper-item-main d-flex justify-content-center align-items-center', {
                            'last-child': isLastItem,
                            selected: isSelected,
                            unselected: !isSelected,
                            disabled: isDisabled,
                        })}
                    >
                        <span className={clsx('step-label')}>{text}</span>
                    </div>
                    {!isLastItem && (
                        <RightArrow
                            color={isSelected ? '#2B8272' : '#ccc'}
                            bgColor={isSelected ? '#2B8272' : bgColorNotSelected}
                        />
                    )}
                </div>
            </div>
        );
    }, [isDisabled, isLastItem, isSelected, text]);
};

StepperItem.displayName = 'StepperItem';

/**
 * StepperProps
 */
export interface StepperProps {
    /**
     * configs
     * data for stepper
     */
    configs: { text: string }[];
    /**
     * step
     * current step
     */
    step: number;
    /**
     * spacer
     * space on the left, right
     */
    spacer?: number;
    /**
     * disablePreviousStep
     * disable all step smaller than current step
     */
    disablePreviousStep?: boolean;
    /**
     * disableAllStep
     * disable all step except current step
     */
    disableAllStep?: boolean;
}

/**
 * Stepper
 */
const Stepper = (props: StepperProps): React.JSX.Element => {
    const {
        configs,
        step,
        spacer = 16,
        disableAllStep = false,
        disablePreviousStep = false,
    } = useMemo(() => props, [props]);
    const [currentStep, setCurrentStep] = useState(0);

    useEffect(() => {
        setCurrentStep(step);
    }, [step]);

    /**
     * renderItem
     * @param item StepperItemProps
     * @param index number
     */
    const renderItem = useCallback(
        (item: Omit<StepperItemProps, 'isSelected'>, index: number) => {
            const disablePrevious = disablePreviousStep && index + 1 < currentStep ? true : false;

            return (
                <StepperItem
                    text={item.text}
                    key={index}
                    isSelected={currentStep === index + 1}
                    isLastItem={index + 1 === configs.length}
                    isDisabled={disableAllStep || disablePrevious}
                />
            );
        },
        [configs.length, currentStep, disableAllStep, disablePreviousStep]
    );

    return useMemo(
        () => (
            <div className="common-stepper-component">
                <div
                    className="d-flex flex-row stepper-container"
                    style={{
                        paddingLeft: spacer,
                        paddingRight: spacer,
                        scrollPaddingLeft: spacer,
                        scrollPaddingRight: spacer,
                    }}
                >
                    {configs.map(renderItem)}
                </div>
            </div>
        ),
        [configs, renderItem, spacer]
    );
};

export default memo(Stepper);
