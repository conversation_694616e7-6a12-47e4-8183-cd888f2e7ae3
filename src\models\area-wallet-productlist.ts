import { CommonCallback } from './common';

/**
 * BrandCommonAdModel
 */
export interface AreaWalletProductListModel extends CommonCallback {
    // ブランドID
    BrandID: string;
    CheerID: number;
}

/**
 * ProductItemListItem
 */
export interface ProductItemListItem {
    ProductID: number;
    Price: number;
    ProductRate: number;
    AreaCouponID?: number;
}

/**
 * AreaWalletProductListResponse
 */
export interface AreaWalletProductListResponse {
    ProductItemList: ProductItemListItem[];
    MaximumPrice: number;
    PopClientKey: string;
    MaximumChargePrice: number;
}
