import API from '../constants/api';
import { BaseResponse } from '../models/common';
import {
    Store003InitGetStoreDetailModel,
    Store003InitGetStoreDetailResult,
} from '../models/store003-init-get-store-detail';
import {
    Store001MainGetStoreQueryModel,
    Store001MainGetStoreQueryResult,
} from '../models/store001-main-get-store-query';
import {
    Store001InitGetStoreQueryModel,
    Store001InitGetStoreQueryResult,
} from '../models/store001-init-get-store-query';
import createAPI from './baseApi';

/**
 * StoreAPI
 */
class StoreAPI {
    /**
     * store001InitGetStoreQuery
     * @param data Store001InitGetStoreQueryModel
     * @returns Promise<BaseResponse<Store001InitGetStoreQueryResult>>
     */
    static store001InitGetStoreQuery = (
        data: Store001InitGetStoreQueryModel
    ): Promise<BaseResponse<Store001InitGetStoreQueryResult>> => {
        return createAPI<Store001InitGetStoreQueryResult>({
            url: API.STORE001_INIT_GET_STORE_QUERY,
            data: data,
        });
    };

    /**
     * store001MainGetStoreQuery
     * @param data Store001MainGetStoreQueryModel
     * @returns Promise<BaseResponse<Store001MainGetStoreQueryResult>>
     */
    static store001MainGetStoreQuery = (
        data: Store001MainGetStoreQueryModel
    ): Promise<BaseResponse<Store001MainGetStoreQueryResult>> => {
        return createAPI<Store001MainGetStoreQueryResult>({
            url: API.STORE001_MAIN_GET_STORE_QUERY,
            data: data,
        });
    };

    /**
     * store003InitGetStoreDetail
     * @param data Store003InitGetStoreDetailModel
     * @returns Promise<BaseResponse<Store003InitGetStoreDetailResult>>
     */
    static store003InitGetStoreDetail = (
        data: Store003InitGetStoreDetailModel
    ): Promise<BaseResponse<Store003InitGetStoreDetailResult>> => {
        return createAPI<Store003InitGetStoreDetailResult>({
            url: API.STORE003_INIT_GET_STORE_DETAIL,
            data: data,
        });
    };
}

export default StoreAPI;
