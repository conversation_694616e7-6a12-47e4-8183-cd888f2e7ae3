import API from '../constants/api';
import { BaseResponse } from '../models/common';
import {
    Coupon003InitGetCouponDetailResult,
    Coupon003InitGetCouponDetailModel,
} from '../models/coupon003-init-get-coupon-detail';
import createAPI from './baseApi';
import { Coupon001InitCouponListModel, Coupon001InitCouponListResult } from '../models/coupon001-init-coupon-list';

class CouponAPI {
    /**
     * couponList
     * @params data Coupon001InitCouponListModel
     * @returns Promise<BaseResponse<Coupon001InitCouponListResult>
     */
    static couponList = (data: Coupon001InitCouponListModel): Promise<BaseResponse<Coupon001InitCouponListResult>> => {
        return createAPI<Coupon001InitCouponListResult>({
            url: API.COUPON001_INIT_GET_COUPON_LIST,
            data,
        });
    };

    /**
     * couponDetail
     * @params data Coupon003InitGetCouponDetailModel
     * @returns Promise<BaseResponse<Coupon003InitGetCouponDetailResult>
     */
    static couponDetail = (
        data: Coupon003InitGetCouponDetailModel
    ): Promise<BaseResponse<Coupon003InitGetCouponDetailResult>> => {
        return createAPI<Coupon003InitGetCouponDetailResult>({
            url: API.COUPON003_INIT_GET_COUPON_DETAIL,
            data,
        });
    };

    /**
     * couponDetailUpdate
     * @params data Coupon003InitGetCouponDetailModel
     * @returns Promise<BaseResponse<Coupon003InitGetCouponDetailResult>
     */
    static couponDetailUpdate = (
        data: Coupon003InitGetCouponDetailModel
    ): Promise<BaseResponse<Coupon003InitGetCouponDetailResult>> => {
        return createAPI<Coupon003InitGetCouponDetailResult>({
            url: API.COUPON004_MAIN_USE_COUPON,
            data,
        });
    };
}

export default CouponAPI;
