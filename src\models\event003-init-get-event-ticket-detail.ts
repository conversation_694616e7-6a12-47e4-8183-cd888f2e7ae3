import { CommonCallback } from './common';

/**
 * Event003InitGetEventTicketDetailModel
 */
export interface Event003InitGetEventTicketDetailModel extends CommonCallback {
    EventTicketID: string;
    CheerID?: string;
    BrandID?: string;
}

/**
 * AnswerItem
 */
export interface AnswerItem {
    // 選択回答ID
    ChoiceID?: string;
    // 選択回答内容
    Text?: string;
    // 次質問マッピング
    NextQuestionMapping?: string;
    // 自由記述回答フラグ
    FreeInputFlag?: boolean;
}

/**
 * QuestionItem
 */
export interface QuestionItem {
    // 質問ID
    QuestionID?: string;
    // 質問タイプ
    QuestionType?: string;
    // 必須フラグ
    RequireFlag?: boolean;
    // 質問文
    Text?: string;
    // 優先順位フラグ
    PriorityRequiredFlag?: boolean;
    // 最大選択数
    MaxSelectableChoices?: number;
    AnswerList: AnswerItem[];
}

/**
 * SurveyItem
 */
export interface SurveyItem {
    // フォームID
    FormID?: string;
    // タイトル
    FormTitle?: string;
    // 説明
    FormDetail?: string;
    // 開始日時
    StartDateTime?: string;
    // 終了日次
    EndDateTime?: string;
    QuestionList: QuestionItem[];
}

/**
 * EventTicketDetailItem
 */
export interface EventTicketDetailItem {
    BrandEventTicketID: string;
    EventTicketDetailID: string;
    EventTicketEntryTime: string;
    EventTicketExitTime: string;
    EventTicketDetailViewFlag: boolean;
    EventTicketPriceName: string;
    EventTicketPrice: number;
    EventTicketMaxSaleNumber: number;
    EventTicketSoldNumber: number;
    EventTicketMaxBuyNumber: number;
}

/**
 * Event003InitGetEventTicketDetailResult
 */
export interface Event003InitGetEventTicketDetailResult {
    EventTicketID: string;
    EventTicketName: string;
    EventTicketTopImage: string;
    EventTicketDescription: string;
    EventTicketDescriptionImage: string;
    EventTicketAddress: string;
    EventTicketLatitude: number;
    EventTicketLongitude: number;
    EventTicketViewFlag: boolean;
    EventTicketBuyStartDateTime: string;
    EventTicketBuyEndDateTime: string;
    EventTicketDateTime: string;
    EventTicketType: number;
    StoreID: string;
    AreaID: string;
    TwitterURL: string;
    FacebookURL: string;
    InstagramURL: string;
    HPURL: string;
    AccessByTrain: string;
    AccessByCar: string;
    EventTicketPriceInfo: string;
    EventTicketNotes: string;
    EventTicketDetailList: EventTicketDetailItem[];
    SurveyList: SurveyItem[];
}
