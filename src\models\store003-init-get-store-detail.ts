import { CommonCallback } from './common';

/**
 * Store003InitGetStoreDetailModel
 */
export interface Store003InitGetStoreDetailModel extends CommonCallback {
    // ブランドID
    BrandID: string;
    // 応援者ID
    CheerID?: string;
    // 店舗ID
    StoreID: string;
}

/**
 * Store003InitGetStoreDetailResult
 */
export interface Store003InitGetStoreDetailResult {
    // 加盟店名称
    StoreName: string;
    // 店舗画像
    StoreImage?: string;
    // 店舗画像2
    StoreImage2?: string;
    // 店舗画像3
    StoreImage3?: string;
    // 店舗画像4
    StoreImage4?: string;
    // 店舗画像5
    StoreImage5?: string;
    // 店舗区分
    StoreCategory: string;
    // 補足店舗区分リスト
    StoreCategorySubList: string[];
    // 店舗概要
    StoreInformation?: string;
    // 取扱メダル画像
    AvailableMedalServices: string[];
    // この店舗で使えるカード・商品券の合計金額
    AvailableCoins?: number;
    // 定休日
    BusinessHoliday?: string;
    // 営業時間
    BusinessHour?: string;
    // 店舗の公式サイトのURL
    StoreUrl?: string;
    // 外部リンク1
    StoreExternalLink1?: string;
    // 外部リンク2
    StoreExternalLink2?: string;
    // 外部リンク3
    StoreExternalLink3?: string;
    // 住所
    StoreAddress?: string;
    // アクセス（電車・バスで）
    AccessByTrain?: string;
    // アクセス（お車で）
    AccessByCar?: string;
    // 取扱決済方式（1:MPM方式のみ, 2:MPM方式・CPM方式の併用）
    PaymentType?: number;
    // 注意事項
    Notes?: string;
}
