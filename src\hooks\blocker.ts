import { useEffect } from 'react';
import { useLocation } from 'react-router-dom';
import { CURRENT_PATH_NAME } from '../constants/variables';

// BACK_PREVENT_ROUTER_LIST
const BACK_PREVENT_ROUTER_LIST: string[] = [];

// eslint-disable-next-line @typescript-eslint/no-explicit-any
(window as any)[CURRENT_PATH_NAME] = window.location.pathname;

// add event listener
window?.addEventListener(
    'popstate',
    (event) => {
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        const currentUrl = (window as any)[CURRENT_PATH_NAME];

        if (BACK_PREVENT_ROUTER_LIST.includes(currentUrl)) {
            event.stopPropagation();
            event.stopImmediatePropagation();
            window.history.pushState(null, '', window.location.pathname);
        }
    },
    {
        passive: false,
    }
);

// if current location pathname equals to item in BACK_PREVENT_ROUTER_LIST
if (BACK_PREVENT_ROUTER_LIST.includes(window.location.pathname)) {
    // push empty state into stack
    window.history.pushState(null, '', window.location.pathname);
    // change url hash
    window.location.hash = `${Date.now()}`;
}

/**
 * useBackBlocker
 */
const useBackBlocker = (): void => {
    const location = useLocation();

    useEffect(() => {
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        (window as any)[CURRENT_PATH_NAME] = location.pathname;
        if (BACK_PREVENT_ROUTER_LIST.includes(location.pathname)) {
            // if current location pathname equals to item in BACK_PREVENT_ROUTER_LIST
            // push empty state into stack
            window.history.pushState(null, '', window.location.pathname);
            // change url hash
            window.location.hash = `${Date.now()}`;
        }
    }, [location]);
};

export default useBackBlocker;
