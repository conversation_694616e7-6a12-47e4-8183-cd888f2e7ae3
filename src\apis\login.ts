import API from '../constants/api';
import Environments from '../constants/environments';
import { APIResponse, BaseResponse } from '../models/common';
import {
    Login000InitGetKeyVisualListModel,
    Login000InitGetKeyVisualListResult,
} from '../models/login000-init-get-keyvisual-list';
import {
    Login000MainGetEntryEnableFlagModel,
    Login000MainGetEntryEnableFlagResult,
} from '../models/login000-main-get-entryenableflag';
import {
    Login001InitGetBannerListModel,
    Login001InitGetBannerListResult,
} from '../models/login001-init-get-banner-list';
import {
    Login001InitGetKeyVisualListModel,
    Login001InitGetKeyVisualListResult,
} from '../models/login001-init-get-keyvisual-list';
import {
    Login001MainGetLoginEnableFlagModel,
    Login001MainGetLoginEnableFlagResult,
} from '../models/login001-main-get-enableflag';
import { Login001MainGetLineTokenModel, Login001MainGetLineTokenResult } from '../models/login001-main-get-line-token';
import { Login001MainGetTokenModel, Login001MainGetTokenResult } from '../models/login001-main-get-token';
import createAPI from './baseApi';

/**
 * LoginAPI
 * for login screen
 */
class LoginAPI {
    /**
     * login001MainGetToken
     * login001/main/get/token API
     * @param data Login001MainGetTokenModel
     * @returns Promise<APIResponse<Login001MainGetTokenResult>>
     */
    static login001MainGetToken = (
        data: Login001MainGetTokenModel
    ): Promise<APIResponse<Login001MainGetTokenResult>> => {
        return createAPI({
            url: API.LOGIN001_MAIN_GET_TOKEN,
            data: {
                // set default brandID
                BrandID: Environments.brandID,
                ...data,
            },
        });
    };

    /**
     * login000InitGetKeyVisualList
     * @param data Login000InitGetKeyVisualListModel
     * @returns Promise<APIResponse<Login000InitGetKeyVisualListResult>>
     */
    static login000InitGetKeyVisualList = (
        data?: Login000InitGetKeyVisualListModel
    ): Promise<APIResponse<Login000InitGetKeyVisualListResult>> => {
        return createAPI({
            url: API.LOGIN000_INIT_GET_KEYVISUAL_LIST,
            data: {
                BrandID: Environments.brandID,
                ...data,
            },
        });
    };

    /**
     * login001InitGetKeyvisualList
     * login001/init/get/keyvisual/list API
     * @param data Login001InitGetKeyvisualListModel
     * @returns Promise<APIResponse<Login001InitGetKeyvisualListResult>>
     */
    static login001InitGetKeyvisualList = (
        data?: Login001InitGetKeyVisualListModel
    ): Promise<APIResponse<Login001InitGetKeyVisualListResult>> => {
        return createAPI({
            url: API.LOGIN001_INIT_GET_KEYVISUAL_LIST,
            data: {
                BrandID: Environments.brandID,
                ...data,
            },
        });
    };

    /**
     * login001InitGetBannerList
     * login001/init/get/banner/list API
     * @param data Login001InitGetBannerListModel
     * @returns Promise<APIResponse<Login001InitGetBannerListResult>>
     */
    static login001InitGetBannerList = (
        data?: Login001InitGetBannerListModel
    ): Promise<APIResponse<Login001InitGetBannerListResult>> => {
        return createAPI({
            url: API.LOGIN001_INIT_GET_BANNER_LIST,
            data: {
                BrandID: Environments.brandID,
                ...data,
            },
        });
    };

    /**
     * login001MainGetLineToken
     * @param data Login001MainGetTokenSnsModel
     * @returns Promise<APIResponse<Login001MainGetTokenSnsResult>>
     */
    static login001MainGetLineToken = (
        data: Login001MainGetLineTokenModel
    ): Promise<APIResponse<Login001MainGetLineTokenResult>> => {
        return createAPI({
            url: API.LOGIN001_MAIN_GET_LINE_TOKEN,
            data: {
                BrandID: Environments.brandID,
                ...data,
            },
        });
    };

    /**
     * login000MainGetEntryEnableFlag
     * @param data Login000MainGetEntryEnableFlagModel
     * @returns Promise<BaseResponse<Login000MainGetEntryEnableFlagResult>>
     */
    static login000MainGetEntryEnableFlag = (
        data: Login000MainGetEntryEnableFlagModel
    ): Promise<BaseResponse<Login000MainGetEntryEnableFlagResult>> => {
        return createAPI({
            url: API.LOGIN000_MAIN_GET_ENTRYENABLE_FLAG,
            data: {
                BrandID: Environments.brandID,
                ...data,
            },
        });
    };

    /**
     * login001MainGetLoginEnableFlag
     * @param data Login001MainGetLoginEnableFlagModel
     * @returns Promise<BaseResponse<Login001MainGetLoginEnableFlagResult>>
     */
    static login001MainGetLoginEnableFlag = (
        data: Login001MainGetLoginEnableFlagModel
    ): Promise<BaseResponse<Login001MainGetLoginEnableFlagResult>> => {
        return createAPI({
            url: API.LOGIN001_MAIN_GET_LOGIN_ENABLE_FLAG,
            data: {
                BrandID: Environments.brandID,
                ...data,
            },
        });
    };
}

export default LoginAPI;
