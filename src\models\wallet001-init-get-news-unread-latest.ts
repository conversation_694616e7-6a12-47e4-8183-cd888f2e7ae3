import { CommonCallback } from './common';

/**
 * Wallet001InitGetNewsUnreadLatestModel
 */
export type Wallet001InitGetNewsUnreadLatestModel = CommonCallback;

/**
 * Wallet001InitGetNewsUnreadLatestItem
 */
export interface Wallet001InitGetNewsUnreadLatestItem {
    // お知らせID
    MessageID: string;
    // タイトル
    Subject: string;
}

/**
 * Wallet001InitGetNewsUnreadLatestResult
 */
export interface Wallet001InitGetNewsUnreadLatestResult {
    // お知らせ一覧
    NewsList: Wallet001InitGetNewsUnreadLatestItem[];
}
