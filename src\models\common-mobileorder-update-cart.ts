import { CommonCallback } from './common';

/**
 * CommonMobileOrderUpdateCartModel
 */
export interface CommonMobileOrderUpdateCartModel extends CommonCallback {
    ItemID: string;
    Quantity: number;
    UpdateDateTime?: string;
    QuantityUpdateType: number;
    IsItemUpdate?: boolean;
}

/**
 * CommonMobileOrderUpdateCartResult
 */
export interface CommonMobileOrderUpdateCartResult {
    Quantity: number;
    UpdateDateTime: string;
    ItemCount: number;
    IsAdded: boolean;
}
