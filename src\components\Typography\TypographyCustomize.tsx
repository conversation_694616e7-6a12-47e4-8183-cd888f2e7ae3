import { Typography as TypographyBase } from '@mui/material';

import clsx from 'clsx';
import React, { CSSProperties, memo, useCallback, useMemo } from 'react';

/**
 * TypographyProps
 */
export interface TypographyProps extends CSSProperties {
    /**
     * The content of the component.
     */
    children?: React.ReactNode;
    /**
     * onClick
     */
    onClick?: (event?: React.MouseEvent) => void;
    /**
     * Typography's id
     */
    id?: string;
    /**
     * action key
     */
    actionKey?: string;
    /**
     * class name
     */
    className?: string;
}

/**
 * TypographyCustomize
 * @param props TypographyProps
 * @returns React.JSX.Element
 */
const TypographyCustomize = (props: TypographyProps): React.JSX.Element => {
    // Material Typography props
    const { children, className, onClick, id, ...propsRest } = useMemo(() => props, [props]);

    // css properties
    const { ...cssRest } = useMemo(() => propsRest, [propsRest]);

    /**
     * Handle action click
     */
    const handleOnClick = useCallback(() => {
        onClick && onClick();
    }, [onClick]);

    return useMemo(
        () => (
            <TypographyBase
                className={clsx('common-Typography-component', className)}
                onClick={handleOnClick}
                style={{
                    ...cssRest,
                }}
                id={id}
            >
                {children}
            </TypographyBase>
        ),
        [children, className, cssRest, handleOnClick, id]
    );
};

export default memo(TypographyCustomize);
