import { mount, shallow } from 'enzyme';
import Button, { ButtonProps } from '../Button/Button';
import SpokesLoading from '../SpokesLoading';

/**
 * Unit test for Button component
 */
describe('Unit test for Button component', () => {
    /**
     * setup element
     * @param p InputProps
     */
    const setup = (p?: ButtonProps) => {
        const s = shallow(<Button {...p} />);
        const m = mount(<Button {...p} />);

        return {
            s: s,
            m: m,
            props: p,
        };
    };

    /**
     * should render default Button
     */
    it('should render default Button component', () => {
        const { m } = setup();

        expect(m.find('button').length).toBe(1);
    });

    /**
     * should render Button with default props
     */
    it('should render Button with default props', () => {
        const { m } = setup();

        const button = m.find('button');

        expect(button.at(0).props().style?.borderRadius).toEqual(8);
        expect(button.at(0).props().style?.minHeight).toEqual(44);
        expect(button.at(0).props().style?.fontSize).toEqual(16);
    });

    /**
     * should render Button with custom props
     */
    it('should render Button with custom props', () => {
        const { m } = setup({
            borderRadius: 4,
            minHeight: 50,
            fontSize: 14,
        });

        const button = m.find('button');

        expect(button.at(0).props().style?.borderRadius).toEqual(4);
        expect(button.at(0).props().style?.minHeight).toEqual(50);
        expect(button.at(0).props().style?.fontSize).toEqual(14);
    });

    /**
     * should render Button with label inside
     */
    it('should render Button with label inside', () => {
        const { m } = setup({
            children: 'This is label of Button',
        });

        expect(m.find('button').at(0).text()).toEqual('This is label of Button');
    });

    /**
     * should disable Button when disabled = true
     */
    it('should disable Button when disabled passed is true', () => {
        const { m } = setup({
            disabled: true,
        });

        expect(m.find('button[disabled=true]').length).toBe(1);
    });

    /**
     * should display SpokesLoading inside button when isLoading passed is true
     */
    it('should display SpokesLoading inside button when isLoading passed is true', () => {
        const { m } = setup({
            isLoading: true,
        });

        expect(m.find(SpokesLoading).length).toBe(1);
    });

    /**
     * should display SpokesLoading inside with loadingProps
     */
    it('should display SpokesLoading inside with loadingProps', () => {
        const { m } = setup({
            isLoading: true,
            loadingProps: {
                color: 'red',
                type: 'bars',
            },
        });

        expect(m.find(SpokesLoading).length).toBe(1);

        expect(m.find(SpokesLoading).at(0).props().color).toBe('red');
    });

    /**
     * should render with another variant
     */
    it('should render with another variant', () => {
        const { m } = setup({
            variant: 'outlined',
        });

        expect(m.find('button.MuiButton-outlinedPrimary').length).toBe(1);
    });

    /**
     * should fire onClick event
     */
    it('should fire onClick event', () => {
        const onClick = jest.fn();

        const { m } = setup({
            onClick,
        });

        const button = m.find('button');

        button.simulate('click');

        expect(onClick).toBeCalledTimes(1);
    });
});
