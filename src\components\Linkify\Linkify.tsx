import { memo, useMemo } from 'react';
import { Regexes } from '../../constants/regexes';
import _ from 'lodash';

/**
 * ButtonProps
 */
export interface LinkifyProps {
    /**
     * The content of the component.
     */
    children: string;
    /**
     * option
     */
    option?: '_blank' | '_self';
}

/**
 * Linkify
 */
const Linkify = (props: LinkifyProps): JSX.Element => {
    //  Linkify props
    const { children, option = '_blank' } = useMemo(() => props, [props]);

    const content = useMemo(() => {
        const matched = children?.match(Regexes.LINKIFY) || [];
        const unique = _.union(matched) || [];

        let result = children;
        unique.forEach((item) => {
            const regex = new RegExp(`(${item})`, 'gm');
            const rep = `<a href="${item}" target="${option}">${item}</a>`;
            result = String(result).replace(regex, rep);
        });
        return result;
    }, [children, option]);

    return useMemo(() => <span dangerouslySetInnerHTML={{ __html: content }} />, [content]);
};

export default memo(Linkify);
