import { CommonCallback } from './common';

/**
 * Login001MainGetTokenModel
 */
export interface Login001MainGetTokenModel extends CommonCallback {
    // ブランドID
    BrandID?: string;

    // ユーザーID
    UserID: string;

    // パスワード
    Password: string;
}

/**
 * UserLoginResult
 */
export interface Login001MainGetTokenResult {
    // IdToken
    IdToken: string;
    // RefreshToken
    RefreshToken: string;
}
