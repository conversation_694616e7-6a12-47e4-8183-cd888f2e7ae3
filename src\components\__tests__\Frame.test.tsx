import { mount, shallow } from 'enzyme';
import Frame, { FrameForwardedRef, FrameProps } from '../Frame/Frame';
import Image from '../Image';
import React from 'react';

/**
 * Unit test for Frame component
 */
describe('Unit test for Frame component', () => {
    /**
     * setup element
     * @param props FrameProps
     */
    const setup = (props?: FrameProps) => {
        const r = React.createRef<FrameForwardedRef>();
        const s = shallow(<Frame {...props} />);
        const m = mount(<Frame {...props} />);

        return {
            ref: r,
            shallow: s,
            mount: m,
            props,
        };
    };

    /**
     * display enough elements
     */
    describe('display enough elements', () => {
        const { shallow, mount } = setup({
            children: <Image className="img-fluid" src="https://via.placeholder.com/375x250" alt="" />,
            aspectRatio: { w: 100, h: 100 },
            frameWidth: 375,
            frameHeight: 250,
        });

        /**
         * require children
         */
        it('require children', () => {
            expect(mount.at(0).props().children).not.toBeUndefined();
        });

        /**
         * display children
         */
        it('display children', () => {
            expect(shallow.find(Image).length).toEqual(1);
        });

        /**
         * added frame width, frame height with aspectRatio
         */
        it('added frame width, frame height with aspectRatio', () => {
            expect(mount.find('div').length).toEqual(1);
            expect(mount.find('div').props().style?.height).toEqual(375);
            expect(mount.find('div').props().style?.width).toEqual(375);
        });

        /**
         * added frame width, frame height with aspectRatio
         */
        it('added frame width, frame height with aspectRatio', () => {
            const { mount } = setup({
                children: <Image className="img-fluid" src="https://via.placeholder.com/375x250" alt="" />,
                aspectRatio: { w: 200, h: 100 },
                frameWidth: 375,
                frameHeight: 250,
            });

            expect(mount.find('div').length).toEqual(1);
            expect(mount.find('div').props().style?.height).toEqual(187.5);
            expect(mount.find('div').props().style?.width).toEqual(375);
        });

        /**
         * added frame width, frame height without aspectRatio
         */
        it('added frame width, frame height without aspectRatio', () => {
            const { mount } = setup({
                frameWidth: 375,
                frameHeight: 250,
            });

            expect(mount.find('div').length).toEqual(1);
            expect(mount.find('div').props().style?.height).toEqual(250);
            expect(mount.find('div').props().style?.width).toEqual(375);
        });

        /**
         * added frame width, frame height without aspectRatio
         */
        it('added frame width, frame height without aspectRatio', () => {
            const { mount } = setup({
                // frameWidth: 0,
                frameHeight: 250,
            });

            expect(mount.find('div').length).toEqual(1);
            expect(mount.find('div').props().style?.height).toEqual(250);
        });
    });
});
