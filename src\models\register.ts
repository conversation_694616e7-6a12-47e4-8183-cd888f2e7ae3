import { CommonCallback } from './common';

/**
 * RegisterModel
 */
export interface RegisterModel extends CommonCallback {
    // 応援者メールアドレス
    CheerMailAddress: string;
    // 応援者パスワード
    CheerPassword: string;
    // 電話番号
    TelephoneNumber: string;
    // 当選者情報チェックフラグ
    WinnerListCheckFlg: boolean;
    // メイル送信有無フラグ
    SendMailFlg: number;
}

/**
 * RegisterResponse
 */
export interface RegisterResponse {
    // 応援者ID
    CheerID?: number;
    // AccountStatus
    AccountStatus?: string;
}

/**
 * RegisterAuthModel
 */
export interface RegisterAuthModel extends CommonCallback {
    // 応援者メールアドレス
    CheerID?: number;
    // 応援者パスワード
    CheerAuthCode: string;
    // AccountStatus
    AccountStatus?: string;
    // CheerPassword
    CheerPassword?: string;
}
