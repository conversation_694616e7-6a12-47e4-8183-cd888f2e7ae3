import { CommonCallback } from './common';

/**
 * IdeaDao011Assignee
 */
export type IdeaDao011Assignee = {
    // 担当者応援者ID
    CheerID: string;
    // 担当者ニックネーム
    NickName: string;
    // 担当者プロフィール画像
    ProfileImageFileName: string;
};

/**
 * IdeaDao011Creator
 */
export type IdeaDao011Creator = {
    // 登録者応援者ID
    CheerID: string;
    // 登録者ニックネーム
    NickName: string;
    // 登録者プロフィール画像
    ProfileImageFileName: string;
};

/**
 * IdeaDao011InitGetTaskInfoDetailModel
 */
export interface IdeaDao011InitGetTaskInfoDetailModel extends CommonCallback {
    // DAOID
    DaoID: string;
    // タスクID
    TaskID: string;
}

/**
 * Attachments
 */
export interface IAttachments {
    // 登録ファイル名
    FileName: string;
    // 表示ファイル名
    DisplayFileName: string;
    // アップロード日時
    UploadDateTime: string;
}

/**
 * IdeaDao011InitGetTaskInfoDetailResult
 */
export interface IdeaDao011InitGetTaskInfoDetailResult {
    // DAO名
    DaoName: string;
    // リーダーフラグ
    // LeaderFlag: number;
    // 担当者フラグ
    AssigneeFlag: number;
    // タスク名称
    TaskName: string;
    // タスク内容
    Description: string;
    // 報酬
    RewardAmount: number;
    // ステータス
    Status: number;
    // 担当者
    Assignee?: IdeaDao011Assignee;
    // 担当者
    Creater: IdeaDao011Creator;
    // Attachments
    Attachments: IAttachments[];
}
