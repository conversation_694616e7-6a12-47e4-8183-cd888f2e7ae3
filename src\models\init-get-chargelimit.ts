import { CommonCallback } from './common';

/**
 * InitGetChargeLimitModel
 */
export interface InitGetChargeLimitModel extends CommonCallback {
    // メダルサービスID
    MedalServiceID: string;
}

/**
 * ChargeCredit001InitGetChargeLimitModel
 */
export type ChargeCredit001InitGetChargeLimitModel = InitGetChargeLimitModel;

/**
 * ChargeCnveni001InitGetChargeLimitModel
 */
export type ChargeCnveni001InitGetChargeLimitModel = InitGetChargeLimitModel;

/**
 * ChargeIntBnk001InitGetChargeLimitModel
 */
export type ChargeIntBnk001InitGetChargeLimitModel = InitGetChargeLimitModel;

/**
 * InitGetChargeLimitResult
 */
export interface InitGetChargeLimitResult {
    // チャージ限度額
    ChargeLimit: number;
}

/**
 * ChargeCredit001InitGetChargeLimitResult
 */
export type ChargeCredit001InitGetChargeLimitResult = InitGetChargeLimitResult;

/**
 * ChargeCnveni001InitGetChargeLimitResult
 */
export type ChargeCnveni001InitGetChargeLimitResult = InitGetChargeLimitResult;

/**
 * ChargeIntBnk001InitGetChargeLimitResult
 */
export type ChargeIntBnk001InitGetChargeLimitResult = InitGetChargeLimitResult;
