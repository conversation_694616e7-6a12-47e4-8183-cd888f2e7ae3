import { CommonCallback } from './common';

/**
 * Top001InitGetStoreListModel
 */
export interface Top001InitGetStoreListModel extends CommonCallback {
    // ブランドID
    BrandID: string;
    // 店舗タイプ
    StoreType: number;
    // 1ページあたりの取得アイテム数
    PageSize: number;
}

/**
 * Top001InitGetStoreItem
 */
export interface Top001InitGetStoreItem {
    // 店舗ID
    StoreID: string;
    // 店舗画像
    StoreImage: string;
    // 店舗名
    StoreName: string;
    // 店舗説明
    StoreInformation: string;
}

/**
 * Top001InitGetStoreListResult
 */
export interface Top001InitGetStoreListResult {
    // 店舗一覧
    StoreList: Top001InitGetStoreItem[];
    // 店舗タイプIDリスト
    StoreCategoryIDs: string[];
}
