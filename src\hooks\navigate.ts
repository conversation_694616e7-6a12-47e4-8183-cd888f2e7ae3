import { useCallback } from 'react';
import { NavigateOptions, To, useNavigate as useRRDNavigate } from 'react-router-dom';
import StorageServices from '../services/storage.service';

/**
 * UseNavigateOptions
 */
interface UseNavigateOptions<T> extends NavigateOptions {
    /**
     * keepStateInStorage
     */
    keepStateInStorage?: boolean;
    /**
     * state
     */
    state?: T;
    /**
     * storage
     * Default: sessionStorage
     */
    storage?: StorageServices;
}

/**
 * UseNavigateFunction
 */
export interface UseNavigateFunction {
    <T>(to: To, options?: UseNavigateOptions<T>): Promise<void>;
    (delta: number): void;
}

/**
 * useNavigate
 */
const useNavigate = (): UseNavigateFunction => {
    // react-router-dom useNavigate hook
    const navigateHook = useRRDNavigate();

    /**
     * to
     * @param to To
     * @param options UseNavigateOptions
     */
    const to = useCallback(
        async <T = unknown>(to: To, options?: UseNavigateOptions<T>) => {
            // if state exists in options and keepStateInStorage flag is true
            if (options?.keepStateInStorage && options?.state) {
                // get options storage or default will be Session Storage
                const storage = options?.storage || StorageServices.Session;

                // keep data in session storage
                await storage.set(to.toString(), options.state);
            }

            // use react-router-dom navigate hook to navigate
            options ? navigateHook(to, options) : navigateHook(to);
        },
        // eslint-disable-next-line react-hooks/exhaustive-deps
        []
    );

    /**
     * delta
     * @param delta number
     */
    const delta = useCallback((delta: number) => {
        navigateHook(delta);
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);

    return (to || delta) as UseNavigateFunction;
};

export default useNavigate;
