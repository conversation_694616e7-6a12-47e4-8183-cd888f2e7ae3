import API from '../constants/api';
import {
    AreaNftAreaNftAgreementVersionGetModel,
    AreaNftAreaNftAgreementVersionGetResult,
} from '../models/area-nft-agreement-version-get';
import { AreaNftAreaNftAgreementVersionUpdateModel } from '../models/area-nft-agreement-version-update';
import { AreaOCRModel, AreaOCRResponse } from '../models/area-ocr';
import { AreaWalletCardModel, AreaWalletCardResponse } from '../models/area-wallet-card';
import {
    AreaWalletCashReceiveRealModel,
    AreaWalletCashReceiveRealResponse,
} from '../models/area-wallet-cash-receive-real';
import { AreaWalletCouponItem, AreaWalletCouponModel } from '../models/area-wallet-coupon';
import {
    AreaWalletPaymentConfigGetModel,
    AreaWalletPaymentConfigGetResult,
} from '../models/area-wallet-payment-config-get';
import { AreaWalletProductListResponse } from '../models/area-wallet-productlist';
import { APIResponse, BaseResponse } from '../models/common';
import createAPI from './baseApi';

/**
 * AreaAPI
 */
class AreaAPI {
    /**
     * areaWalletProductList
     * @returns Promise<BaseResponse>
     */
    static areaWalletProductList = (): Promise<APIResponse<AreaWalletProductListResponse>> => {
        return createAPI({
            url: API.AREA_WALLET_PRODUCTLIST,
        });
    };

    /**
     * areaWalletPaymentConfigGet
     * @returns Promise<BaseResponse>
     */
    static areaWalletPaymentConfigGet = (data: AreaWalletPaymentConfigGetModel): Promise<BaseResponse> => {
        return createAPI<AreaWalletPaymentConfigGetResult>({
            url: API.AREA_WALLET_PAYMENT_CONFIG_GET,
            data,
        });
    };

    /**
     * areaWalletCashReceiveReal
     * @returns Promise<BaseResponse<AreaWalletCashReceiveRealResponse>>
     */
    static areaWalletCashReceiveReal = (
        data: AreaWalletCashReceiveRealModel
    ): Promise<BaseResponse<AreaWalletCashReceiveRealResponse>> => {
        return createAPI<AreaWalletCashReceiveRealResponse>({
            data,
            url: API.AREA_WALLET_CASH_RECEIVE_REAL,
        });
    };

    /**
     * areaWalletCard
     * @returns Promise<BaseResponse<AreaWalletCardResponse>>
     */
    static areaWalletCard = (data: AreaWalletCardModel): Promise<BaseResponse<AreaWalletCardResponse>> => {
        return createAPI<AreaWalletCardResponse>({
            data,
            url: API.AREA_WALLET_CARD,
        });
    };

    /**
     * areaOCR
     * @returns Promise<BaseResponse<AreaORCResponse>>
     */
    static areaOCR = (data: AreaOCRModel): Promise<BaseResponse<AreaOCRResponse>> => {
        return createAPI<AreaOCRResponse>({
            data,
            url: API.AREA_OCR,
        });
    };

    /**
     * areaWalletCoupon
     * @returns Promise<BaseResponse<AreaWalletCouponItem>>
     */
    static areaWalletCoupon = (data: AreaWalletCouponModel): Promise<BaseResponse<AreaWalletCouponItem>> => {
        return createAPI<AreaWalletCouponItem>({
            data,
            url: API.AREA_WALLET_COUPON,
        });
    };

    /**
     * areaNftAgreementVersionGet
     * @param data AreaNftAreaNftAgreementVersionGetModel
     * @returns Promise<BaseResponse<AreaNftAreaNftAgreementVersionGetResult>>
     */
    static areaNftAgreementVersionGet = (
        data: AreaNftAreaNftAgreementVersionGetModel
    ): Promise<BaseResponse<AreaNftAreaNftAgreementVersionGetResult>> => {
        return createAPI<AreaNftAreaNftAgreementVersionGetResult>({
            data,
            url: API.AREA_NFT_AGREEMENT_VERSION_GET,
        });
    };

    /**
     * areaNftAgreementVersionUpdate
     * @param data AreaNftAreaNftAgreementVersionUpdateModel
     * @returns Promise<BaseResponse>
     */
    static areaNftAgreementVersionUpdate = (data: AreaNftAreaNftAgreementVersionUpdateModel): Promise<BaseResponse> => {
        return createAPI({
            data,
            url: API.AREA_NFT_AGREEMENT_VERSION_UPDATE,
        });
    };
}

export default AreaAPI;
