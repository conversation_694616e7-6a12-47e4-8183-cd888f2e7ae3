import { CommonCallback } from './common';

/**
 * DAOCommunity007DaoPutChatModel
 */
export interface DAOCommunity007DaoPutChatModel extends CommonCallback {
    // DaoID
    DaoID: string;
    // カテゴリーID
    CategoryID: string;
    // チャネルID
    ChannelID: string;
    // スレッドID
    ThreadID?: string;
    // スレッド種別
    ThreadType: number;
    // 本文タイトル
    Title?: string;
    // 投稿内容
    Content: string;
}

/**
 * DAOCommunity007DaoPutChatMentionItem
 */
export interface DAOCommunity007DaoPutChatMentionItem {
    // 応援者ID
    CheerID: string;
    // ニックネーム
    NickName: string;
    // メッセージ
    Message: string;
    // プロフィール画像ファイル名
    ProfileImageFileName: string;
}

/**
 * DAOCommunity007DaoPutChatReactionItem
 */
export interface DAOCommunity007DaoPutChatReactionItem {
    // スレッドID
    ThreadID: string;
    // チャットID
    ChatID: string;
    // リアクション画像ID
    ReactionImageID: string;
    // リアクション応援者ID
    ReactionCheerID: string;
}

/**
 * DAOCommunity007DaoPutChatResult
 */
export interface DAOCommunity007DaoPutChatResult {
    // スレッドID
    ThreadID: string;
    // 有効並び順
    VaridSortNo: number;
}
