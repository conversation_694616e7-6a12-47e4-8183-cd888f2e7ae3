import { CommonCallback } from './common';

/**
 * Wallet001InitGetCouponListModel
 */
export interface Wallet001InitGetCouponListModel extends CommonCallback {
    // 1ページあたりの取得アイテム数
    PageSize: number;
}

/**
 * Wallet001InitGetCouponListItem
 */
export interface Wallet001InitGetCouponListItem {
    // クーポンID
    PresentationCouponID: string;
    // タイトル
    Title: string;
    // 画像
    Image?: string;
    // 加盟店名
    StoreName: string;
    // 有効期限
    EndDateTime: string;
}

/**
 * Wallet001InitGetCouponListResult
 */
export interface Wallet001InitGetCouponListResult {
    // クーポン一覧
    CouponList: Wallet001InitGetCouponListItem[];
}
