import { CommonCallback } from './common';

/**
 * EventTicketType
 */
export interface EventTicketType {
    EventTicketID: string;
    EventTicketName: string;
    EventTicketTopImage?: string;
    EventTicketDescription?: string;
    EventTicketDateTime: string;
    AreaID?: string;
}

export interface Event001InitGetEventTicketListModel extends CommonCallback {
    PageSize: number;
    ExclusiveStartKey?: string | null | undefined;
    Keyword?: string;
    Rearrange?: number;
    AreaID?: string;
    StartDate?: string;
    EndDate?: string;
    BrandID?: string;
    CheerID?: string;
}

/**
 * Event001InitGetEventTicketListResult
 */
export interface Event001InitGetEventTicketListResult {
    EventTicketList?: EventTicketType[] | null;
    LastEvaluatedKey?: string;
}
